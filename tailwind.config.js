import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import twElements from 'tw-elements/plugin.cjs';


export default {
  content: [
    './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
    './storage/framework/views/*.php',
    './resources/views/**/*.blade.php',
    './resources/views/components/**/*.blade.php', 
    './node_modules/tw-elements/dist/js/**/*.js',
  ],
  safelist: [
    'text-blue-400',
    'text-green-400',
    'text-yellow-400',
    'text-purple-400',
    'text-red-400',
    'text-indigo-400',
    'text-pink-400',
    'text-cyan-400',
    'text-gray-400',
    'text-gray-300',
    'text-white',
  ],  
  theme: {
    extend: {
      fontFamily: {
        sans: ['Figtree', ...defaultTheme.fontFamily.sans],
      },
    },
  },
  plugins: [forms, twElements],
};
