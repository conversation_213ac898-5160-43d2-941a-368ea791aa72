-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 20, 2025 at 03:25 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.1.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `kc`
--

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_elements`
--

CREATE TABLE `kazcms_elements` (
  `id` int NOT NULL,
  `name` json NOT NULL COMMENT 'Elements name',
  `slug` json NOT NULL COMMENT 'Elements slug',
  `seo_keywords` json NOT NULL,
  `seo_description` json NOT NULL,
  `created_at` int UNSIGNED NOT NULL COMMENT 'Unix timestamp of creation',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'Order for sorting in UI',
  `show_on_front` tinyint NOT NULL DEFAULT '0' COMMENT 'Visible on frontend',
  `user_id` int UNSIGNED NOT NULL COMMENT 'Creator user ID',
  `has_form` tinyint NOT NULL DEFAULT '0' COMMENT 'Should display form',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Icon name (e.g., FontAwesome)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Content Elements Table';

--
-- Dumping data for table `kazcms_elements`
--

INSERT INTO `kazcms_elements` (`id`, `name`, `slug`, `seo_keywords`, `seo_description`, `created_at`, `sort_order`, `show_on_front`, `user_id`, `has_form`, `icon`) VALUES
(1, '{\"en\": \"News\", \"zh\": \"新闻\"}', '{\"en\": \"news\", \"zh\": \"xinwen\"}', 'null', 'null', 1748828918, 5, 1, 2, 0, 'newspaper'),
(2, 'null', 'null', 'null', 'null', 1748828950, 10, 1, 2, 0, 'box'),
(3, 'null', 'null', 'null', 'null', 1748829116, 15, 1, 2, 0, 'images'),
(4, 'null', 'null', 'null', 'null', 1748829145, 20, 1, 2, 0, 'journal-text'),
(5, 'null', 'null', 'null', 'null', 1748829280, 25, 1, 2, 0, 'info-circle'),
(6, 'null', 'null', 'null', 'null', 1749480519, 40, 1, 2, 1, 'bar-chart'),
(7, 'null', 'null', 'null', 'null', 1749480554, 60, 1, 2, 1, 'clipboard-check');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_failed_jobs`
--

CREATE TABLE `kazcms_failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_visibility_rules`
--

CREATE TABLE `kazcms_field_visibility_rules` (
  `id` bigint UNSIGNED NOT NULL COMMENT '自增ID',
  `element_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '如 product',
  `field_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '如 price',
  `action` enum('view','edit') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '控制查看还是编辑',
  `allowed_group_ids` json NOT NULL COMMENT '允许的用户组ID数组，例如 [1,2,5]',
  `allowed_user_ids` json NOT NULL COMMENT '允许的用户ID数组，例如 [1,2,5]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段权限控制表，控制字段可见和编辑权限';

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_languages`
--

CREATE TABLE `kazcms_languages` (
  `id` int UNSIGNED NOT NULL,
  `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language code (e.g., en, fr, zh)',
  `name` json NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is the language enabled',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'For ordering in UI',
  `is_default` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Available languages';

--
-- Dumping data for table `kazcms_languages`
--

INSERT INTO `kazcms_languages` (`id`, `code`, `name`, `enabled`, `sort_order`, `is_default`) VALUES
(1, 'en', '{\"en\": \"English\", \"kz\": \"ағылшын\", \"zh\": \"英语\"}', 1, 1, 1),
(2, 'zh', '{\"en\": \"Chinese\", \"kz\": \"қытай\", \"zh\": \"中文\"}', 1, 2, 0),
(3, 'fr', '{\"en\": \"French\", \"kz\": \"француз\", \"zh\": \"法语\"}', 1, 3, 0),
(4, 'kz', '{\"en\": \"Kazakh\", \"kz\": \"қазақ\", \"zh\": \"哈萨克语\"}', 1, 4, 0),
(5, 'ar', '{\"en\": \"Arabic\", \"kz\": \"араб\", \"zh\": \"阿拉伯语\"}', 1, 5, 0);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_migrations`
--

CREATE TABLE `kazcms_migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_migrations`
--

INSERT INTO `kazcms_migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_multilang_checker`
--

CREATE TABLE `kazcms_multilang_checker` (
  `id` bigint UNSIGNED NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `json_column_name` varchar(100) NOT NULL,
  `column_description` json DEFAULT NULL COMMENT '字段解释说明，多语言 JSON 格式',
  `table_id` varchar(30) NOT NULL,
  `module` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

--
-- Dumping data for table `kazcms_multilang_checker`
--

INSERT INTO `kazcms_multilang_checker` (`id`, `table_name`, `json_column_name`, `column_description`, `table_id`, `module`, `created_at`, `updated_at`) VALUES
(1, 'elements', 'name', '{\"en\": \"name\", \"zh\": \"元素名\"}', 'id', '{\"en\": \"elements\", \"zh\": \"元素\"}', '2025-06-20 02:50:33', '2025-06-20 02:50:33'),
(2, 'elements', 'slug', NULL, 'id', '{\"en\": \"elements\", \"zh\": \"元素\"}', '2025-06-20 02:50:33', '2025-06-20 02:50:33'),
(3, 'user_groups', 'name', NULL, 'id', '{\"en\": \"user group\", \"zh\": \"用户组\"}', '2025-06-20 03:20:33', '2025-06-20 03:20:33');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_password_reset_tokens`
--

CREATE TABLE `kazcms_password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_permissions`
--

CREATE TABLE `kazcms_permissions` (
  `id` int UNSIGNED NOT NULL,
  `parent_id` int UNSIGNED DEFAULT NULL COMMENT 'Parent permission ID, NULL if top-level',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Internal name like create_post, edit_product',
  `label` json NOT NULL COMMENT 'Display name in multiple languages',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional module or feature name'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_permissions`
--

INSERT INTO `kazcms_permissions` (`id`, `parent_id`, `name`, `label`, `module`) VALUES
(1, NULL, 'manage_elements', '{\"en\": \"Manage Elements\", \"kz\": \"Элементтерді басқару\", \"zh\": \"元素管理\"}', 'element'),
(3, 1, 'show_list', '{\"en\": \"Show List\", \"kz\": \"Тізімді көрсету\", \"zh\": \"查看列表\"}', 'element'),
(4, 1, 'add_update_elements', '{\"en\": \"Add/Update Elements\", \"kz\": \"Элементтерді қосу/жаңарту\", \"zh\": \"添加/更新元素\"}', 'element'),
(5, 1, 'delete_element', '{\"en\": \"Delete Element\", \"kz\": \"Элементті жою\", \"zh\": \"删除元素\"}', 'element'),
(6, 1, 'show_data_list', '{\"en\": \"Show Data List\", \"kz\": \"Деректер тізімін көру\", \"zh\": \"查看数据列表\"}', 'element'),
(7, 1, 'export_data', '{\"en\": \"Export Data\", \"kz\": \"Деректерді экспорттау\", \"zh\": \"导出数据\"}', 'element'),
(8, 1, 'add_edit_templates', '{\"en\": \"Add/Edit Templates\", \"kz\": \"Үлгілерді қосу/өңдеу\", \"zh\": \"添加/编辑模板\"}', 'element'),
(9, 1, 'manage_properties', '{\"en\": \"Manage Properties\", \"kz\": \"Қасиеттерді басқару\", \"zh\": \"管理属性\"}', 'element'),
(10, 9, 'add_update_properties', '{\"en\": \"Add/Update Properties\", \"kz\": \"Қасиеттерді қосу/жаңарту\", \"zh\": \"添加/更新属性\"}', 'element'),
(11, 9, 'delete_properties', '{\"en\": \"Delete Properties\", \"kz\": \"Қасиеттерді жою\", \"zh\": \"删除属性\"}', 'element'),
(12, 9, 'field_permission', '{\"en\": \"Field Permission\", \"kz\": \"Өріс рұқсаттары\", \"zh\": \"字段权限\"}', 'element'),
(13, 9, 'other_manage', '{\"en\": \"Other Manage\", \"kz\": \"Басқа басқару\", \"zh\": \"其他管理\"}', 'element');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_permission_assignments`
--

CREATE TABLE `kazcms_permission_assignments` (
  `id` bigint UNSIGNED NOT NULL,
  `permission_id` int UNSIGNED NOT NULL,
  `assignee_type` enum('user','group') COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignee_id` int UNSIGNED NOT NULL,
  `created_by` bigint UNSIGNED NOT NULL COMMENT '添加数据的用户ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_permission_assignments`
--

INSERT INTO `kazcms_permission_assignments` (`id`, `permission_id`, `assignee_type`, `assignee_id`, `created_by`, `created_at`, `updated_at`) VALUES
(3, 3, 'group', 1, 1, '2025-06-20 02:06:09', '2025-06-20 02:06:09'),
(4, 12, 'group', 1, 1, '2025-06-20 02:06:09', '2025-06-20 02:06:09'),
(9, 5, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(10, 6, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(11, 10, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(12, 11, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(13, 4, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(14, 5, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(15, 6, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(16, 10, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(17, 11, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(20, 11, 'group', 6, 1, '2025-06-20 02:10:22', '2025-06-20 02:10:22'),
(21, 12, 'group', 6, 1, '2025-06-20 02:10:22', '2025-06-20 02:10:22');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_personal_access_tokens`
--

CREATE TABLE `kazcms_personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_site_settings`
--

CREATE TABLE `kazcms_site_settings` (
  `id` bigint UNSIGNED NOT NULL,
  `site_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'KAZCMS',
  `site_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `favicon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `is_maintenance_mode` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_multilang` tinyint NOT NULL DEFAULT '0' COMMENT '0 = Single language, 1 = Multi-language '
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_site_settings`
--

INSERT INTO `kazcms_site_settings` (`id`, `site_name`, `site_url`, `meta_title`, `meta_keywords`, `meta_description`, `favicon`, `logo`, `language`, `is_maintenance_mode`, `created_at`, `updated_at`, `is_multilang`) VALUES
(1, 'KAZCMS', 'http://localhost/abc', 'KAZCMS', 'a,c,c,c', 'fdsfds', 'images/eb4bc1d9-ebff-481b-86ed-dd13a9571a81.png', 'images/88ecc2fd-c556-48d0-a943-464cb1b9e1ba.png', 'en', 0, NULL, '2025-05-31 05:41:14', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_users`
--

CREATE TABLE `kazcms_users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_group_id` int NOT NULL DEFAULT '6'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_users`
--

INSERT INTO `kazcms_users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `user_group_id`) VALUES
(1, 'kz', '<EMAIL>', NULL, '$2y$12$AnbhjL4U2XpuBStWDbV4uuTj7E595K1Mo.QzDivfKdKo66/XsCv36', '218w4Rb8tqmQFpEcTG2HDfPArX5oxoyexSY697HYs5GW0MLU4bVJL3bqvd2G', '2025-06-14 22:17:10', '2025-06-14 22:17:10', 6);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_user_groups`
--

CREATE TABLE `kazcms_user_groups` (
  `id` int UNSIGNED NOT NULL COMMENT 'Primary key',
  `name` json NOT NULL COMMENT 'Group name in multiple languages, e.g. { "en": "Editor", "fr": "Éditeur" }',
  `parent_id` int UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Direct parent group ID',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Pipe-separated ancestor group IDs, e.g. |1|3|5|',
  `level` tinyint UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Depth level in the hierarchy (root is 0)',
  `is_leaf` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this is a leaf group (no children)',
  `sort_order` tinyint UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Optional custom ordering within siblings',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hierarchical user groups with multi-language support';

--
-- Dumping data for table `kazcms_user_groups`
--

INSERT INTO `kazcms_user_groups` (`id`, `name`, `parent_id`, `path`, `level`, `is_leaf`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, '{\"en\": \"Administrator\", \"kz\": \"Әкімші\", \"zh\": \"管理员\"}', 0, '|', 0, 0, 1, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(2, '{\"en\": \"Editor\", \"kz\": \"Редактор\", \"zh\": \"编辑\"}', 1, '|1|', 1, 1, 2, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(3, '{\"en\": \"Author\", \"kz\": \"Автор\", \"zh\": \"作者\"}', 1, '|1|', 1, 1, 3, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(4, '{\"en\": \"Moderator\", \"kz\": \"Модератор\", \"zh\": \"审核员\"}', 1, '|1|', 1, 1, 4, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(5, '{\"en\": \"Guest\", \"kz\": \"Қонақ\", \"zh\": \"访客\"}', 0, '|', 0, 1, 5, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(6, '{\"en\": \"Registered\", \"kz\": \"Тіркелген қолданушы\", \"zh\": \"注册用户\"}', 0, '|', 0, 1, 6, '2025-06-19 02:03:04', '2025-06-19 02:03:04'),
(7, '{\"en\": \"VIP\", \"kz\": \"VIP қолданушы\", \"zh\": \"VIP用户\"}', 0, '|', 0, 1, 7, '2025-06-19 02:03:04', '2025-06-19 02:03:04');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_user_user_group`
--

CREATE TABLE `kazcms_user_user_group` (
  `user_id` int UNSIGNED NOT NULL,
  `group_id` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `kazcms_elements`
--
ALTER TABLE `kazcms_elements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user_id` (`user_id`);

--
-- Indexes for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `kazcms_field_visibility_rules`
--
ALTER TABLE `kazcms_field_visibility_rules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_element_field_action` (`element_id`,`field_id`,`action`);

--
-- Indexes for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_code` (`code`);

--
-- Indexes for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_multilang_checker`
--
ALTER TABLE `kazcms_multilang_checker`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_password_reset_tokens`
--
ALTER TABLE `kazcms_password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_assignee` (`assignee_type`,`assignee_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Indexes for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `kazcms_user_groups`
--
ALTER TABLE `kazcms_user_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_user_user_group`
--
ALTER TABLE `kazcms_user_user_group`
  ADD PRIMARY KEY (`user_id`,`group_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `kazcms_elements`
--
ALTER TABLE `kazcms_elements`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_visibility_rules`
--
ALTER TABLE `kazcms_field_visibility_rules`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID';

--
-- AUTO_INCREMENT for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `kazcms_multilang_checker`
--
ALTER TABLE `kazcms_multilang_checker`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_user_groups`
--
ALTER TABLE `kazcms_user_groups`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Primary key', AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  ADD CONSTRAINT `fk_permissions_parent` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  ADD CONSTRAINT `kazcms_permission_assignments_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
