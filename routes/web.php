<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserGroupController;
use App\Http\Controllers\Admin\PermissionsController;
use App\Http\Controllers\Admin\MultilangController;
use App\Http\Controllers\Admin\ContentTypeController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\fields\FieldsController;
use App\Http\Controllers\Admin\FieldSubmitController;
use App\Http\Controllers\Admin\DataController;
use App\Http\Controllers\Front\ContentTypeController as FrontContentTypeController;
use App\Http\Controllers\Front\ErrorController;
use App\Http\Controllers\Admin\DocumentContentController;
use App\Http\Controllers\Admin\DocumentContentVersionController;
use App\Http\Controllers\Front\DocumentController;
use App\Http\Controllers\Admin\KazViewController;
use App\Http\Controllers\Admin\PagesController;
use App\Http\Controllers\Front\FrontPagesController as FrontPagesController;

use App\Models\Page;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application.
| These routes are loaded by the RouteServiceProvider and assigned
| to the "web" middleware group.
|
*/

// 根路径重定向（多语言站点跳转到默认语言）
if (is_multilang_site()) {
    Route::get('/', function () {
        $defaultLang = getDefaultLanguage()?->code ?? config('app.fallback_locale');
        return redirect("/{$defaultLang}");
    });
} else {
    // 单语言站点首页
    Route::get('/', fn() => view('frontend.layouts.kazcms'))->name('home');
}

////////////////////////////////////////////////
// 通用的前端静态页面路由（不含动态 slug）
$frontRoutes = function () {
    Route::get('/', fn() => view('frontend.home.index'))->name('home');
    Route::get('/ff', fn() => view('welcome'))->name('products');
    Route::get('/ffff', fn() => view('welcome'))->name('blog');
    Route::get('/xx', fn() => view('welcome'))->name('about');
    Route::get('/xttx', fn() => view('welcome'))->name('contact');
    Route::get('/xtddtx', fn() => view('welcome'))->name('cart.index');
    Route::get('/xtffddtx', fn() => view('welcome'))->name('account.index');
    Route::get('/xtffffddtx', fn() => view('welcome'))->name('language.switch');
    Route::get('/ajax/categories/{categoryId}', [FrontContentTypeController::class, 'getCategoryChildren'])
        ->name('ajax.categories.children');
    Route::get('/error/{code}', [ErrorController::class, 'index'])
        ->where('code', '[0-9]+')
        ->name('error.page');
};

////////////////////////////////////////////////
// 用户认证相关路由
$authRoutes = function () {
    Route::get('/dashboard', fn() => view('dashboard'))
        ->middleware(['auth', 'verified'])
        ->name('dashboard');

    Route::middleware('auth')->group(function () {
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    });
};

////////////////////////////////////////////////
// 后台管理路由
$adminRoutes = function () {
    Route::prefix('admin')->middleware(['auth'])->as('admin.')->group(function () {

        // 管理后台首页 & 示例页
        Route::get('/', fn() => view('layouts.admin'))->name('dashboard');
        Route::get('/t', fn() => view('admin.welcome'))->name('users.index');

        /**
         * 用户组 UserGroupController
         */
        Route::prefix('user-groups')->name('user_groups.')->group(function () {
            Route::get('/', [UserGroupController::class, 'index'])->name('index');
            Route::get('/form/{id?}', [UserGroupController::class, 'form'])->name('form')->whereNumber('id');
            Route::post('/save', [UserGroupController::class, 'save'])->name('save');
            Route::delete('/{id}', [UserGroupController::class, 'destroy'])->name('destroy');
        });

        /**
         * 权限管理 PermissionsController
         */
        Route::prefix('permissions')->name('permissions.')->group(function () {
            Route::get('/assign/{type}/{ids}', [PermissionsController::class, 'showAssignForm'])->name('assign')
                ->where('ids', '[\w\W]+?');
            Route::post('/save', [PermissionsController::class, 'saveAssignments'])->name('save');
        });

        /**
         * 多语言管理 MultilangController
         */
        Route::prefix('multilang')->name('multilang.')->group(function () {
            Route::get('/checkers', [MultilangController::class, 'showBulkCheckerPage']);
            Route::post('/bulk-scan', [MultilangController::class, 'bulkScan']);
            Route::post('/save-translations', [MultilangController::class, 'saveTranslations']);
        });

        /**
         * 内容模型 ContentTypeController
         */
        Route::prefix('content_types')->name('content_types.')->group(function () {
            Route::get('/', [ContentTypeController::class, 'index'])->name('index');
            Route::get('/edit/{id?}', [ContentTypeController::class, 'edit'])->name('edit');
            Route::post('/save/{id?}', [ContentTypeController::class, 'save'])->name('save');
            Route::delete('/destroy/{id}', [ContentTypeController::class, 'destroy'])->name('destroy');
        });

        /**
         * 分类管理 CategoryController
         */
        Route::prefix('categories')->name('categories.')->group(function () {
            Route::get('/', [CategoryController::class, 'index'])->name('index');
            Route::get('/{id}', [CategoryController::class, 'show']);
            Route::post('/save', [CategoryController::class, 'save']);
            Route::delete('/{id}', [CategoryController::class, 'destroy']);

            // 拓展管理表单和层级结构
            Route::get('/{categoryId}/{parentId}/{id}/{show}', [CategoryController::class, 'form'])
                ->name('form')
                ->whereNumber('categoryId')
                ->whereNumber('parentId')
                ->whereNumber('id')
                ->where('show', '[01]');
            Route::get('/children/{id}', [CategoryController::class, 'getChildren']);
            Route::post('/form', [CategoryController::class, 'storeOrUpdate'])->name('store-update');
            Route::delete('/{categoryId}/{id}', [CategoryController::class, 'destroy'])->name('destroy');
        });

        /**
         * 字段管理 FieldsController + FieldSubmitController
         */
        Route::prefix('fields')->name('fields.')->group(function () {
            Route::get('/list/{content_type_id}', [FieldsController::class, 'showFieldsList'])
                ->whereNumber('content_type_id')->name('list');
            Route::post('/update-order', [FieldsController::class, 'updateOrder'])->name('updateOrder');

            Route::get('/content-types/{contentTypeId}/add', [FieldsController::class, 'showAddFieldStep1'])
                ->name('add_step1');
            Route::match(['get', 'post'], '/content-types/{contentTypeId}/add/{fieldTypeId}/{fieldId}', [FieldsController::class, 'showAddFieldStep2'])
                ->name('add_step2');
            Route::post('/save', [FieldsController::class, 'save'])->name('save');
            Route::post('/delete', [FieldsController::class, 'deleteField'])->name('delete');
            Route::post('/allowed-search', [FieldsController::class, 'allowedSearch'])->name('allowedSearch');

            // 字段数据提交相关
            Route::post('/data/save', [FieldSubmitController::class, 'save'])->name('data.save');

            // 模板生成
            Route::post('/generate-templates/{id}', [TemplatesController::class, 'generateTemplates'])
                ->whereNumber('id')->name('generateTemplates');
        });

        /**
         * 数据管理 DataController + FieldSubmitController
         */
        Route::prefix('data')->name('data.')->group(function () {
            Route::get('/list/{id}', [DataController::class, 'list'])->name('list')->whereNumber('id');
            Route::get('/submit/{content_type_id}/{data_id}', [FieldSubmitController::class, 'submit'])
                ->name('submit')->whereNumber('content_type_id')->whereNumber('data_id');
        });

        Route::prefix('document-content')->group(function () {

            Route::post('{content_type_id}/{data_id}/versions', [DocumentContentVersionController::class, 'store']);


            // 获取章节列表和当前章节内容
            Route::get('{content_type_id}/{data_id}', [DocumentContentController::class, 'index'])
                ->name('admin.document-content.index');

            // 创建新章节（POST，必须提供最基本字段，返回新章节ID）
            Route::post('chapter/update-fields', [DocumentContentController::class, 'updateChapterFields'])
                ->name('admin.document-content.createChapter');

            // 获取某章节详情（GET，章节ID作为 query）
            Route::get('{content_type_id}/{data_id}/chapter', [DocumentContentController::class, 'readChapter'])
                ->name('admin.document-content.readChapter');

            // 更新章节内容（POST，章节ID必填）
            Route::post('{content_type_id}/{data_id}/chapter/{chapter_id}', [DocumentContentController::class, 'updateChapter'])
                ->name('admin.document-content.updateChapter');

            // 删除章节
            Route::post('{content_type_id}/{data_id}/delete-chapter/{chapter_id}', [DocumentContentController::class, 'deleteChapter'])
                ->name('admin.document-content.deleteChapter');


            // 保存指定 field_data 的配置（如默认展开状态）
            Route::post('{data_id}/config', [DocumentContentController::class, 'saveConfig'])
                ->name('admin.field-data.saveConfig');
        });


        Route::prefix('kazview')->name('kazview.')->group(function () {
            Route::get('/', [KazViewController::class, 'index'])->name('index');
            Route::get('/edit/{id?}', [KazViewController::class, 'edit'])->name('edit');
            Route::get('/show/{id}/{token}', [KazViewController::class, 'show'])->name('show');
            Route::post('/save/{id?}', [KazViewController::class, 'save'])->name('save');
            Route::post('/delete', [KazViewController::class, 'delete'])->name('delete');
            Route::post('/toggle-status/{id}', [KazViewController::class, 'toggleStatus'])->name('toggleStatus');
            Route::get('/content-type/{id}/fields', [KazViewController::class, 'getFields'])->name('fields');
            Route::get('/render/{id}/{token}', [KazViewController::class, 'render'])->name('render');

        });



        Route::prefix('pages')->name('pages.')->group(function () {
            // List pages (with optional pagination)
            Route::get('/', [PagesController::class, 'list'])->name('list');

            

            // Show edit form
            Route::get('/edit/{id?}', [PagesController::class, 'edit'])->name('edit');

            // Save page (create or update)
            Route::post('/save/{id?}', [PagesController::class, 'save'])->name('save');

            // Delete page
            Route::delete('/{id}', [PagesController::class, 'delete'])->name('delete');

            // Set main page
            Route::post('/set-main/{id}', [PagesController::class, 'setMain'])->name('set-main');

            // Check slug uniqueness
            Route::post('/check-slug', [PagesController::class, 'checkSlug'])->name('check-slug');

        });


    });
};

////////////////////////////////////////////////
// 根据是否开启多语言注册路由
if (!is_multilang_site()) {
    // 单语言注册
    $frontRoutes();
    $authRoutes();
    $adminRoutes();

    // 单语言动态多级路径页面（至少有一级 slug）
    Route::get('/{path}', function ($path) {
        $lang = config('app.fallback_locale');
        $segments = explode('/', $path);

        // 至少要有内容类型 slug
        $contentTypeSlug = array_shift($segments);
        if (!$contentTypeSlug) {
            abort(404);
        }

        $extraSegments = array_values($segments);

        $contentTypes = getFrontendContentTypes();

        // 先在 content_types 查找
        $found = $contentTypes->first(function ($contentType) use ($contentTypeSlug, $lang) {
            return isset($contentType->slug[$lang]) && $contentType->slug[$lang] === $contentTypeSlug;
        });

        if ($found) {
            // 区分列表页和详情页
            if (empty($extraSegments)) {
                return app(FrontContentTypeController::class)
                    ->index($lang, $contentTypeSlug, $extraSegments);
            }

            // 验证 slug 格式
            foreach ($extraSegments as $slugPart) {
                if (!preg_match('/^[a-z0-9\-]+$/i', $slugPart)) {
                    abort(404);
                }
            }

            return app(FrontContentTypeController::class)
                ->data($lang, $contentTypeSlug, $extraSegments);
        }

        // 如果 content_types 找不到，去 SinglePage 查找
        $page = Page::where(function($q) use ($contentTypeSlug, $lang) {
            $q->where("slug->all", $contentTypeSlug)
              ->orWhere("slug->$lang", $contentTypeSlug);
        })->first();
        if ($page) {
            return app(FrontPagesController::class)->show($lang, $page);
        }

        // 都找不到就 404
        abort(404);
    })->where('path', '.*');
} else {
    // 多语言注册
    Route::group([
        'prefix' => '{lang}',
        'middleware' => ['setlocale']
    ], function () use ($frontRoutes, $authRoutes, $adminRoutes) {
        $frontRoutes();
        $authRoutes();
        $adminRoutes();

        Route::get('/{path}', function ($path) {
            $lang = app()->getLocale();
            $segments = explode('/', $path);

            $contentTypeSlug = array_shift($segments);
            if (!$contentTypeSlug) {
                abort(404);
            }

            $extraSegments = array_values($segments);

            $contentTypes = getFrontendContentTypes();

            // 先在 content_types 查找
            $found = $contentTypes->first(function ($ct) use ($contentTypeSlug, $lang) {
                return isset($ct->slug[$lang]) && $ct->slug[$lang] === $contentTypeSlug;
            });

            if ($found) {
                // 验证 slug 格式
                foreach ($extraSegments as $slugPart) {
                    if (!preg_match('/^[a-z0-9\-]+$/i', $slugPart)) {
                        abort(404);
                    }
                }

                if (count($extraSegments) === 0) {
                    return app(FrontContentTypeController::class)
                        ->index($lang, $contentTypeSlug, $extraSegments);
                }

                if (count($extraSegments) === 1 && in_array($found->render_type, ['document','normal'])) {
                    return app(FrontContentTypeController::class)
                        ->detail($lang, $contentTypeSlug, $extraSegments);
                }
                if ($found->render_type === 'document' && count($extraSegments) >= 2) {
                    return app(DocumentController::class)
                        ->index($lang, $contentTypeSlug, $extraSegments);
                }

                abort(404);
            }

            // 如果 content_types 找不到，去 Page 查找
            $page = Page::where(function($q) use ($path, $lang) {
                $q->where("slug->all", $path)
                  ->orWhere("slug->$lang", $path);
            })->first();
            //return app(FrontPagesController::class)->show($lang, $path);

            if ($page) {
                return app(FrontPagesController::class)->show($lang, $page);
            }

            abort(404);
        })->where('path', '.*')->name('dynamic.frontend');
    })->where('lang', '[a-z]{2}(?:-[a-z]{2})?');
}




require __DIR__ . '/auth.php';
