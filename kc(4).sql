-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 23, 2025 at 04:55 PM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.1.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `kc`
--

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_content_types`
--

CREATE TABLE `kazcms_content_types` (
  `id` int UNSIGNED NOT NULL,
  `name` json NOT NULL COMMENT 'Content type name in multiple languages',
  `slug` json NOT NULL COMMENT 'Unique slug in multiple languages',
  `slug_en` varchar(120) COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`slug`,_utf8mb4'$.en'))) VIRTUAL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `display_order` int NOT NULL DEFAULT '0' COMMENT 'Order of display',
  `show_on_frontend` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether to show on frontend',
  `keywords` json DEFAULT NULL COMMENT 'SEO keywords in multiple languages',
  `description` json DEFAULT NULL COMMENT 'Description in multiple languages',
  `user_id` bigint UNSIGNED NOT NULL COMMENT 'Owner user id',
  `has_form` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether it has associated form',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon name/class for display',
  `deleted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Content types table with multi-language JSON fields';

--
-- Dumping data for table `kazcms_content_types`
--

INSERT INTO `kazcms_content_types` (`id`, `name`, `slug`, `created_at`, `display_order`, `show_on_frontend`, `keywords`, `description`, `user_id`, `has_form`, `icon`, `deleted_at`, `updated_at`) VALUES
(1, '{\"en-us\": \"aa\", \"kz-kz\": \"cc\", \"zh-cn\": \"vv\"}', '{\"en-us\": \"dd\", \"kz-kz\": \"ff\", \"zh-cn\": \"ee\"}', '2025-06-21 21:59:55', 0, 0, '{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}', '{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}', 1, 0, NULL, '2025-06-21 17:59:55', '2025-06-21 21:59:55'),
(2, '{\"en-us\": \"aa\", \"kz-kz\": \"cc\", \"zh-cn\": \"vv\"}', '{\"en-us\": \"dd\", \"kz-kz\": \"ff\", \"zh-cn\": \"ee\"}', '2025-06-21 22:02:34', 0, 0, '{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}', '{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}', 1, 0, NULL, '2025-06-21 18:02:34', '2025-06-21 22:02:34');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_entry_fields`
--

CREATE TABLE `kazcms_entry_fields` (
  `id` int UNSIGNED NOT NULL,
  `entry_id` int UNSIGNED NOT NULL COMMENT 'ID of the content entry this field belongs to',
  `field_id` int UNSIGNED NOT NULL COMMENT 'ID of the field definition',
  `file_type_id` int UNSIGNED DEFAULT NULL COMMENT 'Optional: ID of the file type if this is a file field',
  `content_type_id` int UNSIGNED NOT NULL COMMENT 'ID of the content type',
  `data` json DEFAULT NULL COMMENT 'Field value in JSON format (for multilingual, multiselect, etc.)',
  `user_id` int UNSIGNED NOT NULL DEFAULT '0' COMMENT 'ID of the user who submitted the data',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_entry_fields_backup`
--

CREATE TABLE `kazcms_entry_fields_backup` (
  `id` int UNSIGNED NOT NULL,
  `entry_id` int UNSIGNED NOT NULL,
  `field_id` int UNSIGNED NOT NULL,
  `file_type_id` int UNSIGNED DEFAULT NULL,
  `content_type_id` int UNSIGNED NOT NULL,
  `data` json DEFAULT NULL,
  `user_id` int UNSIGNED NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_failed_jobs`
--

CREATE TABLE `kazcms_failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_fields_order`
--

CREATE TABLE `kazcms_fields_order` (
  `id` int UNSIGNED NOT NULL,
  `content_type_id` int UNSIGNED NOT NULL COMMENT 'Content type this field order belongs to',
  `field_id` int UNSIGNED NOT NULL COMMENT 'Field ID',
  `field_type_id` int UNSIGNED NOT NULL COMMENT 'Field type ID',
  `sort_order` int UNSIGNED NOT NULL COMMENT 'Order number for sorting fields'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_categories`
--

CREATE TABLE `kazcms_field_categories` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `label_name` json NOT NULL COMMENT 'Field name, multi-language',
  `option_type` enum('radio','checkbox') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'radio' COMMENT 'Option type: single or multiple select',
  `show_in_frontend` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Show in frontend (tree display)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is field required',
  `max_select_count` int UNSIGNED DEFAULT NULL COMMENT 'Max selection count for multiple select, null means unlimited',
  `content_type_id` int UNSIGNED NOT NULL COMMENT 'Related content type ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_codes`
--

CREATE TABLE `kazcms_field_codes` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `content_type_id` int UNSIGNED NOT NULL COMMENT 'Related content type ID',
  `label_name` json NOT NULL COMMENT 'Field name (multi-language)',
  `html_code` json NOT NULL COMMENT 'HTML or code block (multi-language)',
  `description` json DEFAULT NULL COMMENT 'Code block description (multi-language)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is this code block enabled?',
  `variables` json DEFAULT NULL COMMENT 'Array of supported variable keys (e.g., ["title", "user.name"])',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_file`
--

CREATE TABLE `kazcms_field_file` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `content_type_id` int UNSIGNED NOT NULL,
  `label_name` json NOT NULL,
  `help_text` json DEFAULT NULL,
  `required` tinyint(1) NOT NULL DEFAULT '0',
  `allowed_file_types` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `max_upload_count` int UNSIGNED DEFAULT NULL,
  `max_file_size_mb` int UNSIGNED DEFAULT NULL,
  `enable_image_preview` tinyint(1) NOT NULL DEFAULT '0',
  `rename_on_upload` enum('original','uuid','timestamp') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'original',
  `display_as_gallery` tinyint(1) NOT NULL DEFAULT '0',
  `auto_compress_images` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_options`
--

CREATE TABLE `kazcms_field_options` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `content_type_id` int UNSIGNED NOT NULL COMMENT 'Foreign key to content type',
  `label_name` json NOT NULL COMMENT 'Field name (multi-language), e.g. {"en":"Color","zh":"颜色"}',
  `display_style` enum('Dropdown','Radio Buttons','Checkboxes') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display style type',
  `allow_multiple` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Allow multiple selection',
  `default_value` json DEFAULT NULL COMMENT 'Default selected value(s)',
  `options` json NOT NULL COMMENT 'Option list in JSON format',
  `max_select_count` int UNSIGNED DEFAULT NULL COMMENT 'Max number of selections allowed (null = unlimited)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this field required?',
  `prefix_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Prefix text for this field',
  `suffix_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Suffix text for this field',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_field_options`
--

INSERT INTO `kazcms_field_options` (`id`, `field_type_id`, `content_type_id`, `label_name`, `display_style`, `allow_multiple`, `default_value`, `options`, `max_select_count`, `is_required`, `prefix_text`, `suffix_text`, `created_at`, `updated_at`) VALUES
(1, 5, 1, '{\"en-us\": \"Color\", \"kz-kz\": \"Түс\", \"zh-cn\": \"颜色\"}', 'Dropdown', 0, '\"red\"', '[{\"label\": {\"en-us\": \"Red\", \"kz-kz\": \"Қызыл\", \"zh-cn\": \"红色\"}, \"value\": \"red\", \"tooltip\": {\"en-us\": \"Fiery and bold\", \"kz-kz\": \"Қызу және батыл\", \"zh-cn\": \"热情奔放\"}, \"disabled\": false, \"icon_url\": \"\", \"prefix_text\": \"\", \"suffix_text\": \"\", \"display_color\": \"#ff0000\"}, {\"label\": {\"en-us\": \"Green\", \"kz-kz\": \"Жасыл\", \"zh-cn\": \"绿色\"}, \"value\": \"green\", \"tooltip\": {\"en-us\": \"Fresh and natural\", \"kz-kz\": \"Таза және табиғи\", \"zh-cn\": \"清新自然\"}, \"disabled\": false, \"icon_url\": \"\", \"prefix_text\": \"\", \"suffix_text\": \"\", \"display_color\": \"#00ff00\"}, {\"label\": {\"en-us\": \"Blue\", \"kz-kz\": \"Көк\", \"zh-cn\": \"蓝色\"}, \"value\": \"blue\", \"tooltip\": {\"en-us\": \"Cool and calm\", \"kz-kz\": \"Салқын және сабырлы\", \"zh-cn\": \"冷静沉着\"}, \"disabled\": false, \"icon_url\": \"\", \"prefix_text\": \"\", \"suffix_text\": \"\", \"display_color\": \"#0000ff\"}]', NULL, 1, 'Choose:', 'color', '2025-06-23 02:48:57', '2025-06-23 16:02:22');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_subtype_labels`
--

CREATE TABLE `kazcms_field_subtype_labels` (
  `id` int UNSIGNED NOT NULL,
  `subtype_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `label_json` json NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_field_subtype_labels`
--

INSERT INTO `kazcms_field_subtype_labels` (`id`, `subtype_code`, `label_json`, `created_at`, `updated_at`) VALUES
(1, 'text', '{\"en-us\": \"Text\", \"kz-kz\": \"Мәтін\", \"zh-cn\": \"文本\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(2, 'email', '{\"en-us\": \"Email\", \"kz-kz\": \"Электрондық пошта\", \"zh-cn\": \"电子邮件\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(3, 'url', '{\"en-us\": \"URL\", \"kz-kz\": \"URL\", \"zh-cn\": \"网址\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(4, 'tel', '{\"en-us\": \"Telephone\", \"kz-kz\": \"Телефон\", \"zh-cn\": \"电话\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(5, 'date', '{\"en-us\": \"Date\", \"kz-kz\": \"Күні\", \"zh-cn\": \"日期\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(6, 'datetime-local', '{\"en-us\": \"Datetime\", \"kz-kz\": \"Күні мен уақыты\", \"zh-cn\": \"日期时间\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(7, 'password', '{\"en-us\": \"Password\", \"kz-kz\": \"Құпиясөз\", \"zh-cn\": \"密码\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(8, 'number', '{\"en-us\": \"Number\", \"kz-kz\": \"Сан\", \"zh-cn\": \"数字\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(9, 'search', '{\"en-us\": \"Search\", \"kz-kz\": \"Іздеу\", \"zh-cn\": \"搜索\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14'),
(10, 'color', '{\"en-us\": \"Color\", \"kz-kz\": \"Түс\", \"zh-cn\": \"颜色\"}', '2025-06-23 00:07:14', '2025-06-23 00:07:14');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_text`
--

CREATE TABLE `kazcms_field_text` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `content_type_id` int UNSIGNED NOT NULL,
  `label_json` json NOT NULL,
  `is_seo` tinyint(1) NOT NULL DEFAULT '0',
  `seo_type` enum('title','meta_description','keywords') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subtype` enum('text','email','url','tel','date','datetime-local','password','number','search','color') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text',
  `placeholder_json` json DEFAULT NULL,
  `default_value_json` json DEFAULT NULL,
  `prefix_text_json` json DEFAULT NULL,
  `suffix_text_json` json DEFAULT NULL,
  `required` tinyint(1) NOT NULL DEFAULT '0',
  `min_length` int UNSIGNED DEFAULT '0',
  `max_length` int UNSIGNED DEFAULT '0',
  `display_color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#000000',
  `help_text_json` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_textarea`
--

CREATE TABLE `kazcms_field_textarea` (
  `id` int UNSIGNED NOT NULL,
  `field_type_id` int UNSIGNED DEFAULT NULL,
  `label_json` json NOT NULL,
  `help_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `content_type_id` int UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_types`
--

CREATE TABLE `kazcms_field_types` (
  `id` int UNSIGNED NOT NULL,
  `type_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_table` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type_name_json` json NOT NULL,
  `description_json` json DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_field_types`
--

INSERT INTO `kazcms_field_types` (`id`, `type_code`, `related_table`, `type_name_json`, `description_json`, `is_enabled`, `created_at`, `updated_at`) VALUES
(1, 'text', 'text', '{\"en-us\": \"Single-line Text\", \"kz-kz\": \"Бір жолдық мәтін\", \"zh-cn\": \"单行文本\"}', '{\"en-us\": \"Single line input field\", \"kz-kz\": \"Бір жолдық енгізу өрісі\", \"zh-cn\": \"单行输入框\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:03'),
(2, 'textarea', 'textarea', '{\"en-us\": \"Multi-line Text\", \"kz-kz\": \"Көп жолды мәтін\", \"zh-cn\": \"多行文本\"}', '{\"en-us\": \"Multi line textarea\", \"kz-kz\": \"Көп жолды мәтін өрісі\", \"zh-cn\": \"多行文本框\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:06'),
(3, 'file', 'file', '{\"en-us\": \"File Upload\", \"kz-kz\": \"Файл жүктеу\", \"zh-cn\": \"文件上传\"}', '{\"en-us\": \"Upload file field\", \"kz-kz\": \"Файл жүктеу өрісі\", \"zh-cn\": \"上传文件字段\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:19'),
(4, 'category', 'categories', '{\"en-us\": \"Category\", \"kz-kz\": \"Санат\", \"zh-cn\": \"分类\"}', '{\"en-us\": \"Category selection field\", \"kz-kz\": \"Санат таңдау өрісі\", \"zh-cn\": \"分类选择字段\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:23'),
(5, 'options', 'options', '{\"en-us\": \"Options\", \"kz-kz\": \"Опциялар\", \"zh-cn\": \"选项\"}', '{\"en-us\": \"Selectable options field\", \"kz-kz\": \"Таңдауға болатын опциялар өрісі\", \"zh-cn\": \"可选择的选项字段\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:28'),
(6, 'code', 'codes', '{\"en-us\": \"Code\", \"kz-kz\": \"Код\", \"zh-cn\": \"代码\"}', '{\"en-us\": \"Code snippet field\", \"kz-kz\": \"Код үзіндісі өрісі\", \"zh-cn\": \"代码片段字段\"}', 1, '2025-06-23 00:00:37', '2025-06-23 15:07:34');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_field_visibility_rules`
--

CREATE TABLE `kazcms_field_visibility_rules` (
  `id` bigint UNSIGNED NOT NULL COMMENT '自增ID',
  `element_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '如 product',
  `field_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '如 price',
  `action` enum('view','edit') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '控制查看还是编辑',
  `allowed_group_ids` json NOT NULL COMMENT '允许的用户组ID数组，例如 [1,2,5]',
  `allowed_user_ids` json NOT NULL COMMENT '允许的用户ID数组，例如 [1,2,5]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段权限控制表，控制字段可见和编辑权限';

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_languages`
--

CREATE TABLE `kazcms_languages` (
  `id` int UNSIGNED NOT NULL,
  `code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language code (e.g., en, fr, zh)',
  `name` json NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is the language enabled',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'For ordering in UI',
  `is_default` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Available languages';

--
-- Dumping data for table `kazcms_languages`
--

INSERT INTO `kazcms_languages` (`id`, `code`, `name`, `enabled`, `sort_order`, `is_default`) VALUES
(1, 'en-us', '{\"en-us\": \"English\", \"kz-kz\": \"ағылшын\", \"zh-cn\": \"英语\"}', 1, 1, 1),
(2, 'zh-cn', '{\"en-us\": \"Chinese\", \"kz-kz\": \"қытай\", \"zh-cn\": \"中文\"}', 1, 2, 0),
(4, 'kz-kz', '{\"en-us\": \"Kazakh\", \"kz-kz\": \"қазақ\", \"zh-cn\": \"哈萨克语\"}', 1, 4, 0);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_menus`
--

CREATE TABLE `kazcms_menus` (
  `id` int UNSIGNED NOT NULL,
  `name` json NOT NULL,
  `key_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'admin',
  `position` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'header',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_index` int DEFAULT '0',
  `parent_id` int UNSIGNED DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_menus`
--

INSERT INTO `kazcms_menus` (`id`, `name`, `key_name`, `type`, `position`, `url`, `icon`, `order_index`, `parent_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '{\"en-us\": \"Dashboard\", \"kz-kz\": \"Басқару тақтасы\", \"zh-cn\": \"仪表板\"}', 'dashboard', 'admin', 'admin.left', '/admin/dashboard', 'fa-dashboard', 1, NULL, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(2, '{\"en-us\": \"User Management\", \"kz-kz\": \"Пайдаланушыларды басқару\", \"zh-cn\": \"用户管理\"}', 'users', 'admin', 'admin.left', '/admin/users', 'fa-users', 2, NULL, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(3, '{\"en-us\": \"Roles\", \"kz-kz\": \"Рөлдер\", \"zh-cn\": \"角色\"}', 'roles', 'admin', 'admin.left', '/admin/users/roles', 'fa-lock', 1, 2, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(4, '{\"en-us\": \"Permissions\", \"kz-kz\": \"Құқықтар\", \"zh-cn\": \"权限\"}', 'permissions', 'admin', 'admin.left', '/admin/users/permissions', 'fa-key', 2, 2, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(5, '{\"en-us\": \"Settings\", \"kz-kz\": \"Параметрлер\", \"zh-cn\": \"设置\"}', 'settings', 'admin', 'admin.left', '/admin/settings', 'fa-cogs', 3, NULL, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(6, '{\"en-us\": \"General\", \"kz-kz\": \"Жалпы\", \"zh-cn\": \"常规\"}', 'general_settings', 'admin', 'admin.left', '/admin/settings/general', 'fa-cog', 1, 5, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(7, '{\"en-us\": \"Security\", \"kz-kz\": \"Қауіпсіздік\", \"zh-cn\": \"安全\"}', 'security_settings', 'admin', 'admin.left', '/admin/settings/security', 'fa-shield-alt', 2, 5, 1, '2025-06-21 11:21:11', '2025-06-21 16:44:01'),
(8, '{\"en-us\": \"Profile\", \"kz-kz\": \"Профиль\", \"zh-cn\": \"个人资料\"}', 'profile', 'user', 'header', '/profile', 'fa-user', 1, NULL, 1, '2025-06-21 11:21:11', '2025-06-21 11:21:11'),
(9, '{\"en-us\": \"Orders\", \"kz-kz\": \"Тапсырыстар\", \"zh-cn\": \"订单\"}', 'orders', 'user', 'header', '/orders', 'fa-shopping-cart', 2, NULL, 1, '2025-06-21 11:21:11', '2025-06-21 11:21:11'),
(10, '{\"en-us\": \"Order Details\", \"kz-kz\": \"Тапсырыс мәліметтері\", \"zh-cn\": \"订单详情\"}', 'order_details', 'user', 'header', '/orders/details', 'fa-info-circle', 1, 9, 1, '2025-06-21 11:21:11', '2025-06-21 11:21:11');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_migrations`
--

CREATE TABLE `kazcms_migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_migrations`
--

INSERT INTO `kazcms_migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_multilang_checker`
--

CREATE TABLE `kazcms_multilang_checker` (
  `id` bigint UNSIGNED NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `json_column_name` varchar(100) NOT NULL,
  `column_description` json DEFAULT NULL COMMENT '字段解释说明，多语言 JSON 格式',
  `table_id` varchar(30) NOT NULL,
  `module` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

--
-- Dumping data for table `kazcms_multilang_checker`
--

INSERT INTO `kazcms_multilang_checker` (`id`, `table_name`, `json_column_name`, `column_description`, `table_id`, `module`, `created_at`, `updated_at`) VALUES
(1, 'elements', 'name', '{\"en-us\": \"name\", \"zh-cn\": \"元素名\"}', 'id', '{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}', '2025-06-20 02:50:33', '2025-06-20 02:50:33'),
(2, 'elements', 'slug', '{\"en-us\": \"slug\", \"zh-cn\": \"网址别名\"}', 'id', '{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}', '2025-06-20 02:50:33', '2025-06-20 02:50:33'),
(3, 'user_groups', 'name', '{\"en-us\": \"user group name\", \"zh-cn\": \"用户组名\"}', 'id', '{\"en-us\": \"user group\", \"zh-cn\": \"用户组\"}', '2025-06-20 03:20:33', '2025-06-20 03:20:33');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_password_reset_tokens`
--

CREATE TABLE `kazcms_password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_permissions`
--

CREATE TABLE `kazcms_permissions` (
  `id` int UNSIGNED NOT NULL,
  `parent_id` int UNSIGNED DEFAULT NULL COMMENT 'Parent permission ID, NULL if top-level',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Internal name like create_post, edit_product',
  `label` json NOT NULL COMMENT 'Display name in multiple languages',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional module or feature name'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_permissions`
--

INSERT INTO `kazcms_permissions` (`id`, `parent_id`, `name`, `label`, `module`) VALUES
(1, NULL, 'manage_elements', '{\"en\": \"Manage Elements\", \"kz\": \"Элементтерді басқару\", \"zh\": \"元素管理\"}', 'element'),
(3, 1, 'show_list', '{\"en\": \"Show List\", \"kz\": \"Тізімді көрсету\", \"zh\": \"查看列表\"}', 'element'),
(4, 1, 'add_update_elements', '{\"en\": \"Add/Update Elements\", \"kz\": \"Элементтерді қосу/жаңарту\", \"zh\": \"添加/更新元素\"}', 'element'),
(5, 1, 'delete_element', '{\"en\": \"Delete Element\", \"kz\": \"Элементті жою\", \"zh\": \"删除元素\"}', 'element'),
(6, 1, 'show_data_list', '{\"en\": \"Show Data List\", \"kz\": \"Деректер тізімін көру\", \"zh\": \"查看数据列表\"}', 'element'),
(7, 1, 'export_data', '{\"en\": \"Export Data\", \"kz\": \"Деректерді экспорттау\", \"zh\": \"导出数据\"}', 'element'),
(8, 1, 'add_edit_templates', '{\"en\": \"Add/Edit Templates\", \"kz\": \"Үлгілерді қосу/өңдеу\", \"zh\": \"添加/编辑模板\"}', 'element'),
(9, 1, 'manage_properties', '{\"en\": \"Manage Properties\", \"kz\": \"Қасиеттерді басқару\", \"zh\": \"管理属性\"}', 'element'),
(10, 9, 'add_update_properties', '{\"en\": \"Add/Update Properties\", \"kz\": \"Қасиеттерді қосу/жаңарту\", \"zh\": \"添加/更新属性\"}', 'element'),
(11, 9, 'delete_properties', '{\"en\": \"Delete Properties\", \"kz\": \"Қасиеттерді жою\", \"zh\": \"删除属性\"}', 'element'),
(12, 9, 'field_permission', '{\"en\": \"Field Permission\", \"kz\": \"Өріс рұқсаттары\", \"zh\": \"字段权限\"}', 'element'),
(13, 9, 'other_manage', '{\"en\": \"Other Manage\", \"kz\": \"Басқа басқару\", \"zh\": \"其他管理\"}', 'element');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_permission_assignments`
--

CREATE TABLE `kazcms_permission_assignments` (
  `id` bigint UNSIGNED NOT NULL,
  `permission_id` int UNSIGNED NOT NULL,
  `assignee_type` enum('user','group') COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignee_id` int UNSIGNED NOT NULL,
  `created_by` bigint UNSIGNED NOT NULL COMMENT '添加数据的用户ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_permission_assignments`
--

INSERT INTO `kazcms_permission_assignments` (`id`, `permission_id`, `assignee_type`, `assignee_id`, `created_by`, `created_at`, `updated_at`) VALUES
(3, 3, 'group', 1, 1, '2025-06-20 02:06:09', '2025-06-20 02:06:09'),
(4, 12, 'group', 1, 1, '2025-06-20 02:06:09', '2025-06-20 02:06:09'),
(9, 5, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(10, 6, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(11, 10, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(12, 11, 'group', 4, 1, '2025-06-20 02:06:24', '2025-06-20 02:06:24'),
(13, 4, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(14, 5, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(15, 6, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(16, 10, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(17, 11, 'group', 3, 1, '2025-06-20 02:08:56', '2025-06-20 02:08:56'),
(20, 11, 'group', 6, 1, '2025-06-20 02:10:22', '2025-06-20 02:10:22'),
(21, 12, 'group', 6, 1, '2025-06-20 02:10:22', '2025-06-20 02:10:22');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_personal_access_tokens`
--

CREATE TABLE `kazcms_personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_site_settings`
--

CREATE TABLE `kazcms_site_settings` (
  `id` int UNSIGNED NOT NULL,
  `site_name` json NOT NULL COMMENT 'Website name (multi-language)',
  `site_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Full website URL, e.g., https://example.com',
  `meta_title` json NOT NULL COMMENT 'SEO meta title (multi-language)',
  `meta_keywords` json NOT NULL COMMENT 'SEO keywords, comma-separated (multi-language support)',
  `meta_description` json NOT NULL COMMENT 'SEO meta description (multi-language)',
  `default_language_id` int UNSIGNED NOT NULL COMMENT 'Default language ID, linked to languages table',
  `maintenance_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is maintenance mode enabled',
  `maintenance_message` json DEFAULT NULL COMMENT 'Maintenance page message (multi-language)',
  `favicon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Favicon path or URL (32x32 recommended)',
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Logo path or URL (SVG or transparent PNG recommended)',
  `logo_alt_text` json DEFAULT NULL COMMENT 'Alt text for logo (multi-language)',
  `copyright` json DEFAULT NULL COMMENT 'Footer copyright text (multi-language)',
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact email address',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact phone number',
  `timezone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'UTC' COMMENT 'Website timezone, e.g. UTC, America/Montreal',
  `default_currency` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default currency code, e.g. USD, CAD',
  `open_graph_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Open Graph image URL (recommended 1200x630)',
  `robots_txt` text COLLATE utf8mb4_unicode_ci COMMENT 'Custom robots.txt content',
  `sitemap_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'URL to sitemap file',
  `social_links` json DEFAULT NULL COMMENT 'Social media links JSON, e.g. {"facebook": "...", "x": "..."}',
  `analytics_code` text COLLATE utf8mb4_unicode_ci COMMENT 'Analytics or other custom code snippets',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_multilang` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_site_settings`
--

INSERT INTO `kazcms_site_settings` (`id`, `site_name`, `site_url`, `meta_title`, `meta_keywords`, `meta_description`, `default_language_id`, `maintenance_mode`, `maintenance_message`, `favicon`, `logo`, `logo_alt_text`, `copyright`, `contact_email`, `contact_phone`, `timezone`, `default_currency`, `open_graph_image`, `robots_txt`, `sitemap_url`, `social_links`, `analytics_code`, `created_at`, `updated_at`, `is_multilang`) VALUES
(1, '{\"en-us\": \"KAZCMS Demo\", \"kz-kz\": \"KAZCMS Демо\", \"zh-cn\": \"KAZCMS演示站\"}', 'https://kazcms.com', '{\"en-us\": \"Best CMS for Kazakhstan\", \"kz-kz\": \"Қазақстандағы үздік CMS\", \"zh-cn\": \"最好的内容管理系统\"}', '{\"en-us\": \"cms,kazakhstan,kazcms\", \"kz-kz\": \"cms,қазақстан,kazcms\", \"zh-cn\": \"cms,哈萨克斯坦,kazcms\"}', '{\"en-us\": \"KAZCMS is a multilingual content management system.\", \"kz-kz\": \"KAZCMS — көптілді CMS жүйесі.\", \"zh-cn\": \"KAZCMS 是一个多语言内容管理系统。\"}', 1, 0, '{\"en-us\": \"Site is under maintenance.\", \"kz-kz\": \"Сайт техникалық қызмет көрсетуде.\", \"zh-cn\": \"网站维护中。\"}', '/assets/img/favicon.png', '/assets/img/logo.svg', '{\"en-us\": \"KAZCMS Logo\", \"kz-kz\": \"KAZCMS логотипі\", \"zh-cn\": \"KAZCMS 标志\"}', '{\"en-us\": \"© 2025 KAZCMS\", \"kz-kz\": \"© 2025 KAZCMS\", \"zh-cn\": \"© 2025 KAZCMS\"}', '<EMAIL>', '+1-800-123-4567', 'America/Montreal', 'CAD', '/assets/img/og.jpg', 'User-agent: *\nDisallow:', 'https://kazcms.com/sitemap.xml', '{\"x\": \"https://x.com/kazcms\", \"facebook\": \"https://facebook.com/kazcms\"}', '<script>console.log(\"Analytics loaded\")</script>', '2025-06-23 14:22:30', '2025-06-23 14:22:30', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_users`
--

CREATE TABLE `kazcms_users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_group_id` int NOT NULL DEFAULT '6'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_users`
--

INSERT INTO `kazcms_users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `user_group_id`) VALUES
(1, 'kz', '<EMAIL>', NULL, '$2y$12$AnbhjL4U2XpuBStWDbV4uuTj7E595K1Mo.QzDivfKdKo66/XsCv36', '218w4Rb8tqmQFpEcTG2HDfPArX5oxoyexSY697HYs5GW0MLU4bVJL3bqvd2G', '2025-06-14 22:17:10', '2025-06-14 22:17:10', 6);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_user_groups`
--

CREATE TABLE `kazcms_user_groups` (
  `id` int UNSIGNED NOT NULL COMMENT 'Primary key',
  `name` json NOT NULL COMMENT 'Group name in multiple languages, e.g. { "en": "Editor", "fr": "Éditeur" }',
  `parent_id` int UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Direct parent group ID',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Pipe-separated ancestor group IDs, e.g. |1|3|5|',
  `level` tinyint UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Depth level in the hierarchy (root is 0)',
  `is_leaf` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this is a leaf group (no children)',
  `sort_order` tinyint UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Optional custom ordering within siblings',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hierarchical user groups with multi-language support';

--
-- Dumping data for table `kazcms_user_groups`
--

INSERT INTO `kazcms_user_groups` (`id`, `name`, `parent_id`, `path`, `level`, `is_leaf`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, '{\"en\": \"Administrator\", \"kz\": \"Әкімші\", \"zh\": \"管理员\"}', 0, '|', 0, 0, 1, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(2, '{\"en\": \"Editor\", \"kz\": \"Редактор\", \"zh\": \"编辑\"}', 1, '|1|', 1, 1, 2, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(3, '{\"en\": \"Author\", \"kz\": \"Автор\", \"zh\": \"作者\"}', 1, '|1|', 1, 1, 3, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(4, '{\"en\": \"Moderator\", \"kz\": \"Модератор\", \"zh\": \"审核员\"}', 1, '|1|', 1, 1, 4, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(5, '{\"en\": \"Guest\", \"kz\": \"Қонақ\", \"zh\": \"访客\"}', 0, '|', 0, 1, 5, '2025-06-19 02:02:57', '2025-06-19 02:02:57'),
(6, '{\"en\": \"Registered\", \"kz\": \"Тіркелген қолданушы\", \"zh\": \"注册用户\"}', 0, '|', 0, 1, 6, '2025-06-19 02:03:04', '2025-06-19 02:03:04'),
(7, '{\"en\": \"VIP\", \"kz\": \"VIP қолданушы\", \"zh\": \"VIP用户\"}', 0, '|', 0, 1, 7, '2025-06-19 02:03:04', '2025-06-19 02:03:04');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_user_user_group`
--

CREATE TABLE `kazcms_user_user_group` (
  `user_id` int UNSIGNED NOT NULL,
  `group_id` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `kazcms_content_types`
--
ALTER TABLE `kazcms_content_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_slug_en` (`slug_en`),
  ADD KEY `fk_user_id` (`user_id`);

--
-- Indexes for table `kazcms_entry_fields`
--
ALTER TABLE `kazcms_entry_fields`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_entry_id` (`entry_id`),
  ADD KEY `idx_field_id` (`field_id`),
  ADD KEY `idx_content_type_id` (`content_type_id`);

--
-- Indexes for table `kazcms_entry_fields_backup`
--
ALTER TABLE `kazcms_entry_fields_backup`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_entry_id` (`entry_id`),
  ADD KEY `idx_field_id` (`field_id`),
  ADD KEY `idx_content_type_id` (`content_type_id`),
  ADD KEY `idx_updated_at` (`updated_at`);

--
-- Indexes for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `kazcms_fields_order`
--
ALTER TABLE `kazcms_fields_order`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_content_field` (`content_type_id`,`field_id`),
  ADD KEY `idx_content_type` (`content_type_id`),
  ADD KEY `idx_field_type` (`field_type_id`);

--
-- Indexes for table `kazcms_field_categories`
--
ALTER TABLE `kazcms_field_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_content_type_id` (`content_type_id`);

--
-- Indexes for table `kazcms_field_codes`
--
ALTER TABLE `kazcms_field_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_content_type_id` (`content_type_id`);

--
-- Indexes for table `kazcms_field_file`
--
ALTER TABLE `kazcms_field_file`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_field_file_content_type` (`content_type_id`);

--
-- Indexes for table `kazcms_field_options`
--
ALTER TABLE `kazcms_field_options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_content_type_id` (`content_type_id`);

--
-- Indexes for table `kazcms_field_subtype_labels`
--
ALTER TABLE `kazcms_field_subtype_labels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subtype_code` (`subtype_code`);

--
-- Indexes for table `kazcms_field_text`
--
ALTER TABLE `kazcms_field_text`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_field_textarea`
--
ALTER TABLE `kazcms_field_textarea`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_field_types`
--
ALTER TABLE `kazcms_field_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `type_code` (`type_code`);

--
-- Indexes for table `kazcms_field_visibility_rules`
--
ALTER TABLE `kazcms_field_visibility_rules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_element_field_action` (`element_id`,`field_id`,`action`);

--
-- Indexes for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_code` (`code`);

--
-- Indexes for table `kazcms_menus`
--
ALTER TABLE `kazcms_menus`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key_name` (`key_name`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_multilang_checker`
--
ALTER TABLE `kazcms_multilang_checker`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_password_reset_tokens`
--
ALTER TABLE `kazcms_password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_assignee` (`assignee_type`,`assignee_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Indexes for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_default_language_id` (`default_language_id`);

--
-- Indexes for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `kazcms_user_groups`
--
ALTER TABLE `kazcms_user_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_user_user_group`
--
ALTER TABLE `kazcms_user_user_group`
  ADD PRIMARY KEY (`user_id`,`group_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `kazcms_content_types`
--
ALTER TABLE `kazcms_content_types`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `kazcms_entry_fields`
--
ALTER TABLE `kazcms_entry_fields`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_entry_fields_backup`
--
ALTER TABLE `kazcms_entry_fields_backup`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_fields_order`
--
ALTER TABLE `kazcms_fields_order`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_categories`
--
ALTER TABLE `kazcms_field_categories`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_codes`
--
ALTER TABLE `kazcms_field_codes`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_file`
--
ALTER TABLE `kazcms_field_file`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_options`
--
ALTER TABLE `kazcms_field_options`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_field_subtype_labels`
--
ALTER TABLE `kazcms_field_subtype_labels`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `kazcms_field_text`
--
ALTER TABLE `kazcms_field_text`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_textarea`
--
ALTER TABLE `kazcms_field_textarea`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_field_types`
--
ALTER TABLE `kazcms_field_types`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `kazcms_field_visibility_rules`
--
ALTER TABLE `kazcms_field_visibility_rules`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID';

--
-- AUTO_INCREMENT for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `kazcms_menus`
--
ALTER TABLE `kazcms_menus`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `kazcms_multilang_checker`
--
ALTER TABLE `kazcms_multilang_checker`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_user_groups`
--
ALTER TABLE `kazcms_user_groups`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Primary key', AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `kazcms_content_types`
--
ALTER TABLE `kazcms_content_types`
  ADD CONSTRAINT `fk_content_types_user` FOREIGN KEY (`user_id`) REFERENCES `kazcms_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `kazcms_field_codes`
--
ALTER TABLE `kazcms_field_codes`
  ADD CONSTRAINT `fk_field_codes_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `kazcms_field_file`
--
ALTER TABLE `kazcms_field_file`
  ADD CONSTRAINT `fk_field_file_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `kazcms_field_options`
--
ALTER TABLE `kazcms_field_options`
  ADD CONSTRAINT `fk_field_options_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `kazcms_menus`
--
ALTER TABLE `kazcms_menus`
  ADD CONSTRAINT `kazcms_menus_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_menus` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `kazcms_permissions`
--
ALTER TABLE `kazcms_permissions`
  ADD CONSTRAINT `fk_permissions_parent` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `kazcms_permission_assignments`
--
ALTER TABLE `kazcms_permission_assignments`
  ADD CONSTRAINT `kazcms_permission_assignments_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
