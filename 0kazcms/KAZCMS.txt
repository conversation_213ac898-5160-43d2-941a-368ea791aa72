对权限角色的管理我是这样想的，
我会有用户分组，user group,
user group 里的用户可以继承权限，
多语言的内容可以让不同用户管理，因为语言不同，理论上一般都是不同人负责。

在后台对elements管理，只是一个name和slug的管理（管理项目不多），所以不存在要专门人管理
但是其它部分元素名我也没有考虑是不是需要多用户管理（因为管理的内容衣不多），但是添加数据
我觉的可以是单人也可以是多人管理。
网站的其它模块也也可以有不同的用户和不同权限。

还有一些我还忘记了，用户有不同分组，所以在每个元素属性都可以确定那些用户组可以查看，那些不能，
比如产品元素里的，产品标题（所有组可以看），价格（注册用户可以），介绍（所有组可以看），像这样，每个
元素的属性都可以设置那些可以查看，那些不行（同时我还有form形式的元素），我不知道我想的这些会不会对他们有影响
还有doc元素。你给我完善完善，还有什么我没有考虑的到。
