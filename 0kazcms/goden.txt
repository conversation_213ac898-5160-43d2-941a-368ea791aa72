1.可以字段级别的编辑和权限控制
2.admin list 里用户可以选择要显示的字段
3.in same content when add the second field, not show the category we add 
before



document content:
/**
 * 要添加的功能
 * 1. 目录可以拖动排序和改变分类位置
 * 2. 展开显示几级，同时点击某一分类可持续展开
   3.添加版本的时候，可能复制目录，内容（用checkbox选，要复制那些内容）
   4.分类管理添加复制整个目录的功能
 *
*/

content fields导出功能
删除normal,form,document,
在content加上data发给谁，form在那儿显示(有两人选项，form在那儿显示，它提交的data在那儿显示)
在content配置页面显示，是否需要文档目录
有些数字的只可填写一次，所以可以有个按钮直接COPY到其语言
或者添加FIELD的时候有一项，不需要多语言，比如数字类的，这样，就可以只用一种然后在所有的都用这个