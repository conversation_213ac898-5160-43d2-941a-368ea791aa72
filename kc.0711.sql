-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: kc
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `kazcms_allowed_search_fields`
--

DROP TABLE IF EXISTS `kazcms_allowed_search_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_allowed_search_fields` (
  `id` int NOT NULL AUTO_INCREMENT,
  `content_type_id` int NOT NULL COMMENT 'Entity or content type ID',
  `field_type_id` int NOT NULL COMMENT 'Field type ID',
  `field_id` int NOT NULL COMMENT 'Specific field ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_allowed_search` (`content_type_id`,`field_id`,`field_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Fields that are allowed to be used for search (only field_type_id in [1,2])';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_allowed_search_fields`
--

LOCK TABLES `kazcms_allowed_search_fields` WRITE;
/*!40000 ALTER TABLE `kazcms_allowed_search_fields` DISABLE KEYS */;
INSERT INTO `kazcms_allowed_search_fields` VALUES (4,1,2,2,'2025-06-28 13:46:41','2025-06-28 13:46:41'),(5,1,6,1,'2025-06-28 13:50:52','2025-06-28 13:50:52');
/*!40000 ALTER TABLE `kazcms_allowed_search_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_attachments`
--

DROP TABLE IF EXISTS `kazcms_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_attachments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'File storage path under public/uploads/...',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Renamed filename, e.g., hash123abc.jpg',
  `file_ext` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'File extension, e.g., png, pdf',
  `original_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Original uploaded filename',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'User-provided attachment title or description',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MIME type, e.g., image/png',
  `file_size` bigint DEFAULT '0' COMMENT 'File size in bytes',
  `user_id` bigint DEFAULT '0' COMMENT 'Uploader user ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record update timestamp',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='KazCMS attachments storage table';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_attachments`
--

LOCK TABLES `kazcms_attachments` WRITE;
/*!40000 ALTER TABLE `kazcms_attachments` DISABLE KEYS */;
INSERT INTO `kazcms_attachments` VALUES (1,'uploads/vpJHkSBCgsi0Hja3f9x6D8Fh3csAMgK9XoUUHsH7.png','vpJHkSBCgsi0Hja3f9x6D8Fh3csAMgK9XoUUHsH7.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(2,'uploads/SEF3ZV67sfygzS2tBUqX08JjSVNPgEj2jnVJMGcr.png','SEF3ZV67sfygzS2tBUqX08JjSVNPgEj2jnVJMGcr.png','png','c.png',NULL,'image/png',575030,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(3,'uploads/rt4t3Lu31FhcHU5PWhQe4eYRX415IudjMXLmi6c4.png','rt4t3Lu31FhcHU5PWhQe4eYRX415IudjMXLmi6c4.png','png','c.png',NULL,'image/png',575030,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(4,'uploads/pUnFop1Lo8iQDIIv3geoslG7MXr72hL6oO5X3mIX.png','pUnFop1Lo8iQDIIv3geoslG7MXr72hL6oO5X3mIX.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(5,'uploads/Znk8VDLaWtu6rih9uUfpjNBZQfKV1hZhc133pR8M.png','Znk8VDLaWtu6rih9uUfpjNBZQfKV1hZhc133pR8M.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:23:06','2025-07-09 22:23:06'),(6,'uploads/vfzpbR1IKeIAGvHnUEZzO4XfFYXnR14Ee8WUkP6p.png','vfzpbR1IKeIAGvHnUEZzO4XfFYXnR14Ee8WUkP6p.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:23:06','2025-07-09 22:23:06'),(7,'uploads/2025/07/09/IUEMcX2ORCk8Ve5wQSXZzZDWAemokNcYSA80G8m3.png','IUEMcX2ORCk8Ve5wQSXZzZDWAemokNcYSA80G8m3.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:24:19','2025-07-09 22:24:19'),(8,'uploads/2025/07/09/0bIXIcmf0TtmXksXPqQRgSdF7Kg6NMeRo3F7CIgO.png','0bIXIcmf0TtmXksXPqQRgSdF7Kg6NMeRo3F7CIgO.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:24:19','2025-07-09 22:24:19'),(9,'uploads/2025/07/10/mMEsby8mTIy3DN8rTlPiABimk1zxtfKMOHpa8AFt.png','mMEsby8mTIy3DN8rTlPiABimk1zxtfKMOHpa8AFt.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(10,'uploads/2025/07/10/xMoCxulQDekoE2bYjsuIitZfrlUnF10Cgh7fYzPa.png','xMoCxulQDekoE2bYjsuIitZfrlUnF10Cgh7fYzPa.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(11,'uploads/2025/07/10/HCIYPgCP6QpiILLOM59tQqlhk75JoSlGiBdXn6Vg.png','HCIYPgCP6QpiILLOM59tQqlhk75JoSlGiBdXn6Vg.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(12,'uploads/2025/07/10/8oP5K0rghqfHavmJhMatXKI6bWymR0WR40FX8jyK.png','8oP5K0rghqfHavmJhMatXKI6bWymR0WR40FX8jyK.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(13,'uploads/2025/07/10/Zafn5y1RWzNrNbx1SckcaQgyweZ1RejQNngPYofe.png','Zafn5y1RWzNrNbx1SckcaQgyweZ1RejQNngPYofe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(14,'uploads/2025/07/10/rwx0MhHMCSbk7EFDZarHZae2ycjBNuMkrPVAGGZk.png','rwx0MhHMCSbk7EFDZarHZae2ycjBNuMkrPVAGGZk.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(15,'uploads/2025/07/10/5F8UyHRqaxqjod40FQFNwaeXprjLDJlnSiF5D7Xd.png','5F8UyHRqaxqjod40FQFNwaeXprjLDJlnSiF5D7Xd.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(16,'uploads/2025/07/10/hlvMJmY4tz4TCX3E9goeoRpiKDLPT9Vhq5Jk6qNS.png','hlvMJmY4tz4TCX3E9goeoRpiKDLPT9Vhq5Jk6qNS.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(17,'uploads/2025/07/10/oAUBn60xIq7JTRNC3qeiRGRaJdpB7xFaa4maEmtm.png','oAUBn60xIq7JTRNC3qeiRGRaJdpB7xFaa4maEmtm.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(18,'uploads/2025/07/10/9KxAFJfRfu7ipqqvAo3tYcWb4prqE9grHg1zLcm6.png','9KxAFJfRfu7ipqqvAo3tYcWb4prqE9grHg1zLcm6.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(19,'uploads/2025/07/10/Rrrhq5c7Of2cPA1erCRl0OPlmTkYky5FPFRizzKl.png','Rrrhq5c7Of2cPA1erCRl0OPlmTkYky5FPFRizzKl.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(20,'uploads/2025/07/10/gYDwyXUTV0ErAFW8SGzm5OPe2NXYAX8rCldTzfgD.png','gYDwyXUTV0ErAFW8SGzm5OPe2NXYAX8rCldTzfgD.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(21,'uploads/2025/07/10/d0IpF6BNxy86RvJsWmTtwp7sx5wB3jvh2Z7vOWCn.png','d0IpF6BNxy86RvJsWmTtwp7sx5wB3jvh2Z7vOWCn.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(22,'uploads/2025/07/10/GMzhookYySdBladQjGYUeuF5foX27Wrc5lJZEOPJ.png','GMzhookYySdBladQjGYUeuF5foX27Wrc5lJZEOPJ.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(23,'uploads/2025/07/10/Ye2fTXjg1HQIN3OqXBESi4Qgb27aX7ionLVtR7kd.png','Ye2fTXjg1HQIN3OqXBESi4Qgb27aX7ionLVtR7kd.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(24,'uploads/2025/07/10/2rXClgocnfQDyQ1ILNpAHm9Y5OeLEM2gxg0swESD.png','2rXClgocnfQDyQ1ILNpAHm9Y5OeLEM2gxg0swESD.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(25,'uploads/2025/07/10/sRGyL3Jsr2SK3vJ1o2s5LVwlWsSJN8Ja1Pwv3phu.png','sRGyL3Jsr2SK3vJ1o2s5LVwlWsSJN8Ja1Pwv3phu.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(26,'uploads/2025/07/10/7R4n6qPVBnxBOfFVjSt3VvPPQr66VH4ep1OYTbKT.png','7R4n6qPVBnxBOfFVjSt3VvPPQr66VH4ep1OYTbKT.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(27,'uploads/2025/07/10/jMeMq62hOl5h18SZZtSlICeKfvd9JVxq0vxd6sd6.png','jMeMq62hOl5h18SZZtSlICeKfvd9JVxq0vxd6sd6.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(28,'uploads/2025/07/10/1Z0x3IFysiDkWa7b8y94VGCgQsv0mmOibOlvIqrv.png','1Z0x3IFysiDkWa7b8y94VGCgQsv0mmOibOlvIqrv.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(29,'uploads/2025/07/10/CcvXPvC5JagZdK1CdKitUQTQH36ytrtlLROtdJw0.png','CcvXPvC5JagZdK1CdKitUQTQH36ytrtlLROtdJw0.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(30,'uploads/2025/07/10/AZwPBOLcVn9rUR2tstwhG6nwxeaQaKINygNpEvff.png','AZwPBOLcVn9rUR2tstwhG6nwxeaQaKINygNpEvff.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(31,'uploads/2025/07/10/jtquffsvmgzpid1m1zFcKl42fvdTC711NIJMNHxV.png','jtquffsvmgzpid1m1zFcKl42fvdTC711NIJMNHxV.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(32,'uploads/2025/07/10/N5z3q0Nf98QskC5IAnv2CUVARk2vxQ58dSyVQCxw.png','N5z3q0Nf98QskC5IAnv2CUVARk2vxQ58dSyVQCxw.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(33,'uploads/2025/07/11/NcqpjPjS8EA2UPehHNWO6ZSET2ScqJajrujsmcU7.png','NcqpjPjS8EA2UPehHNWO6ZSET2ScqJajrujsmcU7.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:45:05','2025-07-11 05:45:05'),(34,'uploads/2025/07/11/H3rNMP1eFoTZXUfYilA3CxiCyBeRiTmquIQBAqSO.png','H3rNMP1eFoTZXUfYilA3CxiCyBeRiTmquIQBAqSO.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:45:05','2025-07-11 05:45:05'),(35,'uploads/2025/07/11/kS1Tys74juBErjrGvL2y97yhNFZIfBRu0E6TKZ9U.png','kS1Tys74juBErjrGvL2y97yhNFZIfBRu0E6TKZ9U.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:49:22','2025-07-11 05:49:22'),(36,'uploads/2025/07/11/FtsQPhZYOgUqcFIxiYEMsFgj9byHb5mynwxWIrRw.png','FtsQPhZYOgUqcFIxiYEMsFgj9byHb5mynwxWIrRw.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:51:18','2025-07-11 05:51:18'),(37,'uploads/2025/07/11/45Hh7C0nWtyF81lqrw5m0xbx0mgxpkkUpkvMQA0D.png','45Hh7C0nWtyF81lqrw5m0xbx0mgxpkkUpkvMQA0D.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:52:55','2025-07-11 05:52:55'),(38,'uploads/2025/07/11/vIb9jdsTQMgZjnbU5re1GsDdJ807WK55Ho2BRWUT.png','vIb9jdsTQMgZjnbU5re1GsDdJ807WK55Ho2BRWUT.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:52:57','2025-07-11 05:52:57'),(39,'uploads/2025/07/11/CkfngRGOEdgIWDHhWkNXiH52f4KRHeAHH16tL4jl.png','CkfngRGOEdgIWDHhWkNXiH52f4KRHeAHH16tL4jl.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:57:04','2025-07-11 05:57:04'),(40,'uploads/2025/07/11/7hnFEAEBUuBvERYLUTUkV45gVibAzMua5BmHYhBe.png','7hnFEAEBUuBvERYLUTUkV45gVibAzMua5BmHYhBe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:59:58','2025-07-11 05:59:58'),(41,'uploads/2025/07/11/9xLqBRn6BQ9SqoYKhmnVkUkYDW4XQVPZPiQEqaof.png','9xLqBRn6BQ9SqoYKhmnVkUkYDW4XQVPZPiQEqaof.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:06:22','2025-07-11 06:06:22'),(42,'uploads/2025/07/11/09GOPi5dQBIofe2C93vFIE2RpYiy6sQUjzgTpYhe.png','09GOPi5dQBIofe2C93vFIE2RpYiy6sQUjzgTpYhe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:17:25','2025-07-11 06:17:25'),(43,'uploads/2025/07/11/DnlLEMQGFUpoIcnf6gFlHu4lOhrFUnEgRQJNaE2l.png','DnlLEMQGFUpoIcnf6gFlHu4lOhrFUnEgRQJNaE2l.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:17:35','2025-07-11 06:17:35'),(44,'uploads/2025/07/11/VkCR5UTQnk3LkveHgpBX4eNfR7DYDHgSagoXe6si.png','VkCR5UTQnk3LkveHgpBX4eNfR7DYDHgSagoXe6si.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(45,'uploads/2025/07/11/95ku4CfIWoazW6ZLny42SeqzHTbtBstX2sm6EdTZ.png','95ku4CfIWoazW6ZLny42SeqzHTbtBstX2sm6EdTZ.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(46,'uploads/2025/07/11/WnH7K9GEm7LTUOlQAWvGtgmSwSH85PHEIa4EGuDX.png','WnH7K9GEm7LTUOlQAWvGtgmSwSH85PHEIa4EGuDX.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(47,'uploads/2025/07/11/c71yUQ1Sde4iDN4yIeNAUqbhOP9I62ciK7NVGbau.png','c71yUQ1Sde4iDN4yIeNAUqbhOP9I62ciK7NVGbau.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(48,'uploads/2025/07/11/M2YiiYyKxYIodTAWf49w2kCGBrS5Enb2IZ1wHBT4.png','M2YiiYyKxYIodTAWf49w2kCGBrS5Enb2IZ1wHBT4.png','png','c.png',NULL,'image/png',575030,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(49,'uploads/2025/07/11/Chijg8LrLxAosmNgQT3E8DKLmLzbtliVtRAhLQRV.png','Chijg8LrLxAosmNgQT3E8DKLmLzbtliVtRAhLQRV.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 20:35:46','2025-07-11 20:35:46');
/*!40000 ALTER TABLE `kazcms_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_categories`
--

DROP TABLE IF EXISTS `kazcms_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL COMMENT 'Multi-language category name',
  `slug` json NOT NULL COMMENT 'Multi-language slug/alias',
  `description` json DEFAULT NULL COMMENT 'Multi-language description',
  `category_id` int NOT NULL,
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Parent category ID, 0 for top-level',
  `parent_path_json` json DEFAULT NULL COMMENT 'Parent category path, e.g. |1||2||3|',
  `show_order` int NOT NULL DEFAULT '0' COMMENT 'Display order',
  `level` int NOT NULL DEFAULT '1' COMMENT 'Category depth level, starting from 1',
  `final_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is it a final category (no children allowed)?',
  `is_virtual` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this category a virtual group?',
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional icon or image URL',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_categories`
--

LOCK TABLES `kazcms_categories` WRITE;
/*!40000 ALTER TABLE `kazcms_categories` DISABLE KEYS */;
INSERT INTO `kazcms_categories` VALUES (1,'{\"en-us\": \"test\", \"kz-kz\": \"3\", \"zh-cn\": \"2\"}','{\"en-us\": \"4\", \"kz-kz\": \"6\", \"zh-cn\": \"5\"}','{\"en-us\": \"5\", \"kz-kz\": \"8\", \"zh-cn\": \"6\"}',0,0,NULL,0,1,1,1,NULL,'2025-06-26 18:15:58','2025-06-26 18:36:43'),(2,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": \"abc\", \"kz-kz\": \"haha\", \"zh-cn\": \"def\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-26 18:17:46','2025-06-26 18:17:46'),(3,'{\"en-us\": \"test1\", \"kz-kz\": \"test2\", \"zh-cn\": \"test2\"}','{\"en-us\": \"abc\", \"kz-kz\": \"hhaa\", \"zh-cn\": \"def\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-26 18:36:18','2025-06-26 18:36:18'),(4,'{\"en-us\": \"hahaen\", \"kz-kz\": \"hahakz\", \"zh-cn\": \"hahacn\"}','{\"en-us\": \"english\", \"kz-kz\": \"haha\", \"zh-cn\": \"chinese\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,1,'\"[\\\"1\\\"]\"',0,2,1,1,NULL,'2025-06-26 18:36:43','2025-06-30 23:31:06'),(5,'{\"en-us\": \"fff\", \"kz-kz\": \"ff\", \"zh-cn\": \"ff\"}','{\"en-us\": \"ff\", \"kz-kz\": \"ff\", \"zh-cn\": \"ff\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,4,'\"[\\\"1\\\",\\\"4\\\"]\"',0,3,0,1,NULL,'2025-06-30 23:30:23','2025-06-30 23:30:23'),(6,'{\"en-us\": \"11\", \"kz-kz\": \"11\", \"zh-cn\": \"11\"}','{\"en-us\": \"22\", \"kz-kz\": \"22\", \"zh-cn\": \"222\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-30 23:30:37','2025-06-30 23:30:37'),(7,'{\"en-us\": \"22\", \"kz-kz\": \"22\", \"zh-cn\": \"22\"}','{\"en-us\": \"33\", \"kz-kz\": \"33\", \"zh-cn\": \"33\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,4,'\"[\\\"1\\\",\\\"4\\\"]\"',0,3,0,1,NULL,'2025-06-30 23:31:06','2025-06-30 23:31:06'),(8,'{\"en-us\": \"55\", \"kz-kz\": \"55\", \"zh-cn\": \"555\"}','{\"en-us\": \"66\", \"kz-kz\": \"66\", \"zh-cn\": \"77\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-30 23:31:27','2025-06-30 23:31:27'),(9,'{\"en-us\": \"2323\", \"kz-kz\": \"fds3232\", \"zh-cn\": \"fds3232\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,1,1,NULL,'2025-07-01 00:21:35','2025-07-01 00:22:55'),(10,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"中文名\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',9,9,'\"[\\\"9\\\"]\"',0,2,0,1,NULL,'2025-07-01 00:22:01','2025-07-01 19:53:38'),(11,'{\"en-us\": \"fdsfdssfefew\", \"kz-kz\": \"fdsfdsrew\", \"zh-cn\": \"fdsfdsewrew\"}','{\"en-us\": \"fdsfdsfds2222222\", \"kz-kz\": \"dsfdsfdsfds222222222222\", \"zh-cn\": \"fdsfdsfdsf2222222222222222222222\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',9,9,'\"[\\\"9\\\"]\"',0,2,0,1,NULL,'2025-07-01 00:22:55','2025-07-01 00:22:55');
/*!40000 ALTER TABLE `kazcms_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_content_types`
--

DROP TABLE IF EXISTS `kazcms_content_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_content_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL COMMENT 'Content type name in multiple languages',
  `slug` json NOT NULL COMMENT 'Unique slug in multiple languages',
  `slug_en` varchar(120) COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`slug`,_utf8mb4'$.en'))) VIRTUAL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `display_order` int NOT NULL DEFAULT '0' COMMENT 'Order of display',
  `show_on_frontend` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether to show on frontend',
  `keywords` json DEFAULT NULL COMMENT 'SEO keywords in multiple languages',
  `description` json DEFAULT NULL COMMENT 'Description in multiple languages',
  `user_id` bigint unsigned NOT NULL COMMENT 'Owner user id',
  `has_form` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether it has associated form',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon name/class for display',
  `deleted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_slug_en` (`slug_en`),
  KEY `fk_user_id` (`user_id`),
  CONSTRAINT `fk_content_types_user` FOREIGN KEY (`user_id`) REFERENCES `kazcms_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Content types table with multi-language JSON fields';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_content_types`
--

LOCK TABLES `kazcms_content_types` WRITE;
/*!40000 ALTER TABLE `kazcms_content_types` DISABLE KEYS */;
INSERT INTO `kazcms_content_types` (`id`, `name`, `slug`, `created_at`, `display_order`, `show_on_frontend`, `keywords`, `description`, `user_id`, `has_form`, `icon`, `deleted_at`, `updated_at`) VALUES (1,'{\"en-us\": \"aa\", \"kz-kz\": \"cc\", \"zh-cn\": \"vv\"}','{\"en-us\": \"dd\", \"kz-kz\": \"ff\", \"zh-cn\": \"ee\"}','2025-06-21 21:59:55',0,0,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}','{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,0,NULL,'2025-06-21 17:59:55','2025-06-21 21:59:55'),(2,'{\"en-us\": \"aa\", \"kz-kz\": \"cc\", \"zh-cn\": \"vv\"}','{\"en-us\": \"dd\", \"kz-kz\": \"ff\", \"zh-cn\": \"ee\"}','2025-06-21 22:02:34',0,0,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}','{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,0,NULL,'2025-06-21 18:02:34','2025-06-21 22:02:34');
/*!40000 ALTER TABLE `kazcms_content_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_entry_fields`
--

DROP TABLE IF EXISTS `kazcms_entry_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_entry_fields` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `entry_id` int unsigned NOT NULL COMMENT 'ID of the content entry this field belongs to',
  `field_id` int unsigned NOT NULL COMMENT 'ID of the field definition',
  `file_type_id` int unsigned DEFAULT NULL COMMENT 'Optional: ID of the file type if this is a file field',
  `content_type_id` int unsigned NOT NULL COMMENT 'ID of the content type',
  `data` json DEFAULT NULL COMMENT 'Field value in JSON format (for multilingual, multiselect, etc.)',
  `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID of the user who submitted the data',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_entry_id` (`entry_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type_id` (`content_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_entry_fields`
--

LOCK TABLES `kazcms_entry_fields` WRITE;
/*!40000 ALTER TABLE `kazcms_entry_fields` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_entry_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_entry_fields_backup`
--

DROP TABLE IF EXISTS `kazcms_entry_fields_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_entry_fields_backup` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `entry_id` int unsigned NOT NULL,
  `field_id` int unsigned NOT NULL,
  `file_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `data` json DEFAULT NULL,
  `user_id` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_entry_id` (`entry_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type_id` (`content_type_id`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_entry_fields_backup`
--

LOCK TABLES `kazcms_entry_fields_backup` WRITE;
/*!40000 ALTER TABLE `kazcms_entry_fields_backup` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_entry_fields_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_failed_jobs`
--

DROP TABLE IF EXISTS `kazcms_failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_failed_jobs`
--

LOCK TABLES `kazcms_failed_jobs` WRITE;
/*!40000 ALTER TABLE `kazcms_failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_categories`
--

DROP TABLE IF EXISTS `kazcms_field_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `label_json` json NOT NULL COMMENT 'Field name, multi-language',
  `category_id` int NOT NULL,
  `display_style` enum('radio','checkbox') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'radio' COMMENT 'Option type: single or multiple select',
  `show_in_frontend` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Show in frontend (tree display)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is field required',
  `max_select_count` int unsigned DEFAULT NULL COMMENT 'Max selection count for multiple select, null means unlimited',
  `content_type_id` int unsigned NOT NULL COMMENT 'Related content type ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_categories`
--

LOCK TABLES `kazcms_field_categories` WRITE;
/*!40000 ALTER TABLE `kazcms_field_categories` DISABLE KEYS */;
INSERT INTO `kazcms_field_categories` VALUES (6,4,'{\"en-us\": \"english name\", \"kz-kz\": \"kazakh\", \"zh-cn\": \"中文名\"}',1,'checkbox',1,1,10,1,'2025-06-30 22:26:57','2025-07-11 19:10:47'),(7,4,'{\"en-us\": \"fdfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}',1,'checkbox',1,1,10,1,'2025-07-01 00:19:00','2025-07-11 19:09:55'),(8,4,'{\"en-us\": \"ddd\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"ffsfd\"}',9,'checkbox',1,1,23,1,'2025-07-01 00:19:37','2025-07-11 19:57:56');
/*!40000 ALTER TABLE `kazcms_field_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_codes`
--

DROP TABLE IF EXISTS `kazcms_field_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_codes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL COMMENT 'Related content type ID',
  `label_json` json NOT NULL COMMENT 'Field name (multi-language)',
  `html_code_json` json NOT NULL COMMENT 'HTML or code block (multi-language)',
  `description_json` json DEFAULT NULL COMMENT 'Code block description (multi-language)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is this code block enabled?',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`),
  CONSTRAINT `fk_field_codes_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_codes`
--

LOCK TABLES `kazcms_field_codes` WRITE;
/*!40000 ALTER TABLE `kazcms_field_codes` DISABLE KEYS */;
INSERT INTO `kazcms_field_codes` VALUES (4,6,1,'{\"en-us\": \"this is a code\", \"kz-kz\": \"balala\", \"zh-cn\": \"中文一段代码\"}','{\"en-us\": \"<h1>englihs</h1>\\r\\n<input type=\\\"text\\\">\", \"kz-kz\": \"<h1>kazakh</h1>\", \"zh-cn\": \"<h1>中文</h1>\"}','{\"en-us\": \"this is Description\", \"kz-kz\": \"aahajja\", \"zh-cn\": \"这是介绍\"}',1,'2025-07-01 21:00:45','2025-07-11 19:11:31'),(6,6,2,'{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}','{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}','{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}',1,'2025-07-11 19:03:53','2025-07-11 19:03:53');
/*!40000 ALTER TABLE `kazcms_field_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_data`
--

DROP TABLE IF EXISTS `kazcms_field_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_id` bigint NOT NULL COMMENT '属于同一内容项的ID',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '创建该内容的用户ID（用于归属和权限）',
  `content_type_id` bigint NOT NULL COMMENT '内容类型ID',
  `field_id` bigint NOT NULL COMMENT '字段ID',
  `field_type_id` int NOT NULL COMMENT '字段类型ID（如文本、富文本、图片等）',
  `data` json NOT NULL COMMENT '字段内容，支持多语言结构，如 { "en": "...", "fr": "..." }',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type` (`content_type_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段值存储表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_data`
--

LOCK TABLES `kazcms_field_data` WRITE;
/*!40000 ALTER TABLE `kazcms_field_data` DISABLE KEYS */;
INSERT INTO `kazcms_field_data` VALUES (1,0,1,1,0,0,'{}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(2,1,1,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(3,1,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(4,1,1,1,7,4,'{\"all\": [7]}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(5,1,1,1,6,4,'{\"all\": [5, 7]}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(6,1,1,1,8,4,'{\"all\": [10]}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(7,1,1,1,4,5,'{\"all\": \"32\"}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(8,1,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(9,1,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:34:33','2025-07-11 06:34:33'),(10,0,1,1,0,0,'{}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(11,10,1,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(12,10,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(13,10,1,1,7,4,'{\"all\": [7]}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(14,10,1,1,6,4,'{\"all\": [5, 7]}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(15,10,1,1,8,4,'{\"all\": [10]}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(16,10,1,1,4,5,'{\"all\": \"32\"}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(17,10,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(18,10,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:36:23','2025-07-11 06:36:23'),(19,0,1,1,0,0,'{}','2025-07-11 06:38:00','2025-07-11 06:38:00'),(20,19,1,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(21,19,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(22,19,1,1,7,4,'{\"all\": [7]}','2025-07-11 06:38:00','2025-07-11 06:38:00'),(23,19,1,1,6,4,'{\"all\": []}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(24,19,1,1,8,4,'{\"all\": []}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(25,19,1,1,4,5,'{\"all\": \"32\"}','2025-07-11 06:38:00','2025-07-11 06:38:00'),(26,19,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(27,19,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(28,19,1,1,7,3,'{\"en-us\": [49], \"kz-kz\": [], \"zh-cn\": []}','2025-07-11 06:38:00','2025-07-11 20:35:46'),(29,19,1,1,6,3,'{\"en-us\": [46, 47, 48], \"kz-kz\": [46, 47, 48], \"zh-cn\": [46, 47, 48]}','2025-07-11 06:38:00','2025-07-11 06:38:00');
/*!40000 ALTER TABLE `kazcms_field_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_data_backup`
--

DROP TABLE IF EXISTS `kazcms_field_data_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_data_backup` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_id` bigint NOT NULL COMMENT '内容 ID',
  `content_type_id` bigint NOT NULL COMMENT '内容类型 ID',
  `field_id` bigint NOT NULL COMMENT '字段 ID',
  `field_type_id` int NOT NULL COMMENT '字段类型 ID',
  `old_data` json NOT NULL COMMENT '旧的数据内容（更新前）',
  `changed_by` bigint DEFAULT NULL COMMENT '修改人 ID（user_id）',
  `changed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_changed_by` (`changed_by`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段数据的变更备份表，仅记录内容更新前的数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_data_backup`
--

LOCK TABLES `kazcms_field_data_backup` WRITE;
/*!40000 ALTER TABLE `kazcms_field_data_backup` DISABLE KEYS */;
INSERT INTO `kazcms_field_data_backup` VALUES (1,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-11 20:35:46'),(2,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-11 20:35:46'),(3,19,1,6,4,'{\"all\": [5, 7]}',1,'2025-07-11 20:35:46'),(4,19,1,8,4,'{\"all\": [10]}',1,'2025-07-11 20:35:46'),(5,19,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-11 20:35:46'),(6,19,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-11 20:35:46'),(7,19,1,7,3,'{\"en-us\": [44, 45], \"kz-kz\": [44, 45], \"zh-cn\": [44, 45]}',1,'2025-07-11 20:35:46');
/*!40000 ALTER TABLE `kazcms_field_data_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_file`
--

DROP TABLE IF EXISTS `kazcms_field_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_file` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `label_json` json NOT NULL,
  `help_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `allowed_file_types` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `max_upload_count` int unsigned DEFAULT NULL,
  `max_file_size_mb` int unsigned DEFAULT NULL,
  `enable_image_preview` tinyint(1) NOT NULL DEFAULT '0',
  `rename_on_upload` enum('original','uuid','timestamp') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'original',
  `display_as_gallery` tinyint(1) NOT NULL DEFAULT '0',
  `auto_compress_images` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_field_file_content_type` (`content_type_id`),
  CONSTRAINT `fk_field_file_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_file`
--

LOCK TABLES `kazcms_field_file` WRITE;
/*!40000 ALTER TABLE `kazcms_field_file` DISABLE KEYS */;
INSERT INTO `kazcms_field_file` VALUES (6,3,1,'{\"en-us\": \"file upload\", \"kz-kz\": \"resnam\", \"zh-cn\": \"文件上传\"}','{\"en-us\": \"please upload files\", \"kz-kz\": \"good\", \"zh-cn\": \"请上传照片\"}',1,'JPG,PNG',3,12,1,'original',1,1,'2025-06-30 02:43:22','2025-06-30 02:43:22'),(7,3,1,'{\"en-us\": \"just test\", \"kz-kz\": \"test\", \"zh-cn\": \"只是测试\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,'jpg,png,gif',21,12,1,'original',1,1,'2025-07-01 20:24:18','2025-07-11 19:10:40');
/*!40000 ALTER TABLE `kazcms_field_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_options`
--

DROP TABLE IF EXISTS `kazcms_field_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_options` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL COMMENT 'Foreign key to content type',
  `label_json` json NOT NULL COMMENT 'Field name (multi-language), e.g. {"en":"Color","zh":"颜色"}',
  `display_style` enum('select','radio','checkbox') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display style type',
  `options` json NOT NULL COMMENT 'Option list in JSON format',
  `max_select_count` int unsigned DEFAULT NULL COMMENT 'Max number of selections allowed (null = unlimited)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this field required?',
  `prefix_text_json` json DEFAULT NULL COMMENT 'Prefix text for this field',
  `suffix_text_json` json DEFAULT NULL COMMENT 'Suffix text for this field',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`),
  CONSTRAINT `fk_field_options_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_options`
--

LOCK TABLES `kazcms_field_options` WRITE;
/*!40000 ALTER TABLE `kazcms_field_options` DISABLE KEYS */;
INSERT INTO `kazcms_field_options` VALUES (4,5,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','select','[{\"label\": {\"en-us\": \"ahah\", \"kz-kz\": \"hahakz\", \"zh-cn\": \"hahachinse\"}, \"value\": \"afd\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fdsfd\", \"kz-kz\": \"fds\", \"zh-cn\": \"fdsfds\"}, \"value\": \"32\", \"enabled\": \"1\", \"sort_order\": \"1\"}]',1,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','2025-07-01 20:46:55','2025-07-01 20:46:55'),(5,5,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','checkbox','[{\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"1\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"2\"}]',2,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','2025-07-01 20:48:18','2025-07-11 19:11:16'),(6,5,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','checkbox','[{\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fdsfds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"1\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"2\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"3\"}]',2,0,'{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','2025-07-01 20:59:39','2025-07-11 19:11:26');
/*!40000 ALTER TABLE `kazcms_field_options` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_permission_rules`
--

DROP TABLE IF EXISTS `kazcms_field_permission_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_permission_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `content_type_id` int NOT NULL COMMENT '如 product',
  `field_type_id` int NOT NULL,
  `field_id` int NOT NULL COMMENT '如 price',
  `action` json NOT NULL,
  `allowed_group_ids` json NOT NULL COMMENT '允许的用户组ID数组，例如 [1,2,5]',
  `allowed_user_ids` json NOT NULL COMMENT '允许的用户ID数组，例如 [1,2,5]',
  `updated_at` timestamp NOT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段权限控制表，控制字段可见和编辑权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_permission_rules`
--

LOCK TABLES `kazcms_field_permission_rules` WRITE;
/*!40000 ALTER TABLE `kazcms_field_permission_rules` DISABLE KEYS */;
INSERT INTO `kazcms_field_permission_rules` VALUES (1,2,6,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:03:53','2025-07-11 19:03:53'),(2,1,1,19,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:37','2025-07-11 19:08:37'),(3,1,2,5,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:48','2025-07-11 19:08:48'),(4,1,3,7,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:53','2025-07-11 19:08:53'),(7,1,3,7,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:10:40','2025-07-11 19:10:40'),(8,1,4,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:10:47','2025-07-11 19:10:47'),(9,1,5,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:07','2025-07-11 19:11:07'),(10,1,5,5,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:16','2025-07-11 19:11:16'),(11,1,5,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:26','2025-07-11 19:11:26'),(12,1,6,4,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:32','2025-07-11 19:11:32'),(13,1,4,8,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:57:56','2025-07-11 19:57:56');
/*!40000 ALTER TABLE `kazcms_field_permission_rules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_subtype_labels`
--

DROP TABLE IF EXISTS `kazcms_field_subtype_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_subtype_labels` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `subtype_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `label_json` json NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subtype_code` (`subtype_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_subtype_labels`
--

LOCK TABLES `kazcms_field_subtype_labels` WRITE;
/*!40000 ALTER TABLE `kazcms_field_subtype_labels` DISABLE KEYS */;
INSERT INTO `kazcms_field_subtype_labels` VALUES (1,'text','{\"en-us\": \"Text\", \"kz-kz\": \"Мәтін\", \"zh-cn\": \"文本\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(2,'email','{\"en-us\": \"Email\", \"kz-kz\": \"Электрондық пошта\", \"zh-cn\": \"电子邮件\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(3,'url','{\"en-us\": \"URL\", \"kz-kz\": \"URL\", \"zh-cn\": \"网址\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(4,'tel','{\"en-us\": \"Telephone\", \"kz-kz\": \"Телефон\", \"zh-cn\": \"电话\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(5,'date','{\"en-us\": \"Date\", \"kz-kz\": \"Күні\", \"zh-cn\": \"日期\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(6,'datetime-local','{\"en-us\": \"Datetime\", \"kz-kz\": \"Күні мен уақыты\", \"zh-cn\": \"日期时间\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(7,'password','{\"en-us\": \"Password\", \"kz-kz\": \"Құпиясөз\", \"zh-cn\": \"密码\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(8,'number','{\"en-us\": \"Number\", \"kz-kz\": \"Сан\", \"zh-cn\": \"数字\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(9,'search','{\"en-us\": \"Search\", \"kz-kz\": \"Іздеу\", \"zh-cn\": \"搜索\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(10,'color','{\"en-us\": \"Color\", \"kz-kz\": \"Түс\", \"zh-cn\": \"颜色\"}','2025-06-23 00:07:14','2025-06-23 00:07:14');
/*!40000 ALTER TABLE `kazcms_field_subtype_labels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_text`
--

DROP TABLE IF EXISTS `kazcms_field_text`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_text` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `label_json` json NOT NULL,
  `is_seo` tinyint(1) NOT NULL DEFAULT '0',
  `seo_type` enum('title','meta_description','keywords') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subtype` enum('text','email','url','tel','date','datetime-local','password','number','search','color') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text',
  `placeholder_json` json DEFAULT NULL,
  `default_value_json` json DEFAULT NULL,
  `prefix_text_json` json DEFAULT NULL,
  `suffix_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `min_length` int unsigned DEFAULT '0',
  `max_length` int unsigned DEFAULT '0',
  `display_color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#000000',
  `help_text_json` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_text`
--

LOCK TABLES `kazcms_field_text` WRITE;
/*!40000 ALTER TABLE `kazcms_field_text` DISABLE KEYS */;
INSERT INTO `kazcms_field_text` VALUES (19,1,1,'{\"en-us\": \"title\", \"kz-kz\": \"bas\", \"zh-cn\": \"标题\"}',0,NULL,'text','{\"en-us\": \"please write title\", \"kz-kz\": \"bas jaz\", \"zh-cn\": \"请输入标题\"}','{\"en-us\": \"1\", \"kz-kz\": \"3\", \"zh-cn\": \"2\"}','{\"en-us\": \"pen\", \"kz-kz\": \"pkz\", \"zh-cn\": \"pcn\"}','{\"en-us\": \"sen\", \"kz-kz\": \"skz\", \"zh-cn\": \"scn\"}',1,20,130,'#986a44','{\"en-us\": \"this is title of website\", \"kz-kz\": \"website en bas\", \"zh-cn\": \"这是网站的标题\"}','2025-06-29 19:10:35','2025-07-11 19:08:37');
/*!40000 ALTER TABLE `kazcms_field_text` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_textarea`
--

DROP TABLE IF EXISTS `kazcms_field_textarea`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_textarea` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `label_json` json NOT NULL,
  `help_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `content_type_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_textarea`
--

LOCK TABLES `kazcms_field_textarea` WRITE;
/*!40000 ALTER TABLE `kazcms_field_textarea` DISABLE KEYS */;
INSERT INTO `kazcms_field_textarea` VALUES (5,2,'{\"en-us\": \"content\", \"kz-kz\": \"contentkz\", \"zh-cn\": \"内容\"}','{\"en-us\": \"please write content\", \"kz-kz\": \"content jaz\", \"zh-cn\": \"请填充内容\"}',1,1,'2025-06-29 21:41:24','2025-07-11 19:08:48');
/*!40000 ALTER TABLE `kazcms_field_textarea` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_types`
--

DROP TABLE IF EXISTS `kazcms_field_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_table` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type_name_json` json NOT NULL,
  `description_json` json DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `icon` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_types`
--

LOCK TABLES `kazcms_field_types` WRITE;
/*!40000 ALTER TABLE `kazcms_field_types` DISABLE KEYS */;
INSERT INTO `kazcms_field_types` VALUES (1,'text','text','{\"en-us\": \"Single-line Text\", \"kz-kz\": \"Бір жолдық мәтін\", \"zh-cn\": \"单行文本\"}','{\"en-us\": \"Single line input field\", \"kz-kz\": \"Бір жолдық енгізу өрісі\", \"zh-cn\": \"单行输入框\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:03',''),(2,'textarea','textarea','{\"en-us\": \"Multi-line Text\", \"kz-kz\": \"Көп жолды мәтін\", \"zh-cn\": \"多行文本\"}','{\"en-us\": \"Multi line textarea\", \"kz-kz\": \"Көп жолды мәтін өрісі\", \"zh-cn\": \"多行文本框\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:06',''),(3,'file','file','{\"en-us\": \"File Upload\", \"kz-kz\": \"Файл жүктеу\", \"zh-cn\": \"文件上传\"}','{\"en-us\": \"Upload file field\", \"kz-kz\": \"Файл жүктеу өрісі\", \"zh-cn\": \"上传文件字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:19',''),(4,'category','categories','{\"en-us\": \"Category\", \"kz-kz\": \"Санат\", \"zh-cn\": \"分类\"}','{\"en-us\": \"Category selection field\", \"kz-kz\": \"Санат таңдау өрісі\", \"zh-cn\": \"分类选择字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:23',''),(5,'options','options','{\"en-us\": \"Options\", \"kz-kz\": \"Опциялар\", \"zh-cn\": \"选项\"}','{\"en-us\": \"Selectable options field\", \"kz-kz\": \"Таңдауға болатын опциялар өрісі\", \"zh-cn\": \"可选择的选项字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:28',''),(6,'code','codes','{\"en-us\": \"Code\", \"kz-kz\": \"Код\", \"zh-cn\": \"代码\"}','{\"en-us\": \"Code snippet field\", \"kz-kz\": \"Код үзіндісі өрісі\", \"zh-cn\": \"代码片段字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:34','');
/*!40000 ALTER TABLE `kazcms_field_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_fields_order`
--

DROP TABLE IF EXISTS `kazcms_fields_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_fields_order` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `content_type_id` int unsigned NOT NULL COMMENT 'Content type this field order belongs to',
  `field_id` int unsigned NOT NULL COMMENT 'Field ID',
  `field_type_id` int unsigned NOT NULL COMMENT 'Field type ID',
  `sort_order` int unsigned NOT NULL COMMENT 'Order number for sorting fields',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_content_field` (`content_type_id`,`field_id`,`field_type_id`) USING BTREE,
  KEY `idx_content_type` (`content_type_id`),
  KEY `idx_field_type` (`field_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_fields_order`
--

LOCK TABLES `kazcms_fields_order` WRITE;
/*!40000 ALTER TABLE `kazcms_fields_order` DISABLE KEYS */;
INSERT INTO `kazcms_fields_order` VALUES (34,1,19,1,10,'2025-06-29 19:10:35','2025-06-29 19:10:35'),(35,1,5,2,15,'2025-06-29 21:41:24','2025-06-29 21:41:24'),(36,1,6,3,25,'2025-06-30 02:43:22','2025-07-01 20:24:34'),(37,1,6,4,25,'2025-06-30 22:26:57','2025-06-30 22:26:57'),(38,1,7,4,21,'2025-07-01 00:19:00','2025-07-01 20:24:34'),(39,1,8,4,35,'2025-07-01 00:19:37','2025-07-01 00:19:37'),(40,1,7,3,21,'2025-07-01 20:24:18','2025-07-01 20:24:34'),(41,1,4,5,40,'2025-07-01 20:46:55','2025-07-01 20:46:55'),(42,1,5,5,45,'2025-07-01 20:48:18','2025-07-01 20:48:18'),(43,1,6,5,50,'2025-07-01 20:59:39','2025-07-01 20:59:39'),(44,1,4,6,55,'2025-07-01 21:00:45','2025-07-01 21:00:45'),(45,2,6,6,5,'2025-07-11 19:03:53','2025-07-11 19:03:53');
/*!40000 ALTER TABLE `kazcms_fields_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_languages`
--

DROP TABLE IF EXISTS `kazcms_languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_languages` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language code (e.g., en, fr, zh)',
  `name` json NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is the language enabled',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'For ordering in UI',
  `is_default` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Available languages';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_languages`
--

LOCK TABLES `kazcms_languages` WRITE;
/*!40000 ALTER TABLE `kazcms_languages` DISABLE KEYS */;
INSERT INTO `kazcms_languages` VALUES (1,'en-us','{\"en-us\": \"English\", \"kz-kz\": \"ағылшын\", \"zh-cn\": \"英语\"}',1,1,1),(2,'zh-cn','{\"en-us\": \"Chinese\", \"kz-kz\": \"қытай\", \"zh-cn\": \"中文\"}',1,2,0),(4,'kz-kz','{\"en-us\": \"Kazakh\", \"kz-kz\": \"қазақ\", \"zh-cn\": \"哈萨克语\"}',1,4,0);
/*!40000 ALTER TABLE `kazcms_languages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_menus`
--

DROP TABLE IF EXISTS `kazcms_menus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_menus` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL,
  `key_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'admin',
  `position` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'header',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_index` int DEFAULT '0',
  `parent_id` int unsigned DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_name` (`key_name`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `kazcms_menus_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_menus` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_menus`
--

LOCK TABLES `kazcms_menus` WRITE;
/*!40000 ALTER TABLE `kazcms_menus` DISABLE KEYS */;
INSERT INTO `kazcms_menus` VALUES (1,'{\"en-us\": \"Admin Dashboard\", \"kz-kz\": \"Басқару тақтасы\", \"zh-cn\": \"管理面板\"}','admin_dashboard','admin','admin.left','/admin','fa-dashboard',1,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(2,'{\"en-us\": \"User Groups\", \"kz-kz\": \"Пайдаланушы топтары\", \"zh-cn\": \"用户组\"}','admin_user_groups','admin','admin.left','/admin/user-groups','fa-users',2,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(3,'{\"en-us\": \"Permission Assign\", \"kz-kz\": \"Рұқсаттарды тағайындау\", \"zh-cn\": \"权限分配\"}','admin_permissions','admin','admin.left','/admin/permissions/assign/user/1','fa-lock',3,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(4,'{\"en-us\": \"Translation Check\", \"kz-kz\": \"Аударма тексеру\", \"zh-cn\": \"翻译检查\"}','admin_multilang_check','admin','admin.left','/admin/multilang/checkers','fa-language',4,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(5,'{\"en-us\": \"Content Types\", \"kz-kz\": \"Контент түрлері\", \"zh-cn\": \"内容类型\"}','admin_content_types','admin','admin.left','/admin/content_types','fa-database',5,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(6,'{\"en-us\": \"Categories\", \"kz-kz\": \"Санаттар\", \"zh-cn\": \"分类\"}','admin_categories','admin','admin.left','/admin/categories','fa-folder',6,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(7,'{\"en-us\": \"Fields\", \"kz-kz\": \"Өрістер\", \"zh-cn\": \"字段\"}','admin_fields','admin','admin.left','/admin/fields/list/1','fa-list',7,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24');
/*!40000 ALTER TABLE `kazcms_menus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_migrations`
--

DROP TABLE IF EXISTS `kazcms_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_migrations`
--

LOCK TABLES `kazcms_migrations` WRITE;
/*!40000 ALTER TABLE `kazcms_migrations` DISABLE KEYS */;
INSERT INTO `kazcms_migrations` VALUES (1,'2014_10_12_000000_create_users_table',1),(2,'2014_10_12_100000_create_password_reset_tokens_table',1),(3,'2019_08_19_000000_create_failed_jobs_table',1),(4,'2019_12_14_000001_create_personal_access_tokens_table',1);
/*!40000 ALTER TABLE `kazcms_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_multilang_checker`
--

DROP TABLE IF EXISTS `kazcms_multilang_checker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_multilang_checker` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL,
  `json_column_name` varchar(100) NOT NULL,
  `column_description` json DEFAULT NULL COMMENT '字段解释说明，多语言 JSON 格式',
  `table_id` varchar(30) NOT NULL,
  `module` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_multilang_checker`
--

LOCK TABLES `kazcms_multilang_checker` WRITE;
/*!40000 ALTER TABLE `kazcms_multilang_checker` DISABLE KEYS */;
INSERT INTO `kazcms_multilang_checker` VALUES (1,'elements','name','{\"en-us\": \"name\", \"zh-cn\": \"元素名\"}','id','{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}','2025-06-20 02:50:33','2025-06-20 02:50:33'),(2,'elements','slug','{\"en-us\": \"slug\", \"zh-cn\": \"网址别名\"}','id','{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}','2025-06-20 02:50:33','2025-06-20 02:50:33'),(3,'user_groups','name','{\"en-us\": \"user group name\", \"zh-cn\": \"用户组名\"}','id','{\"en-us\": \"user group\", \"zh-cn\": \"用户组\"}','2025-06-20 03:20:33','2025-06-20 03:20:33');
/*!40000 ALTER TABLE `kazcms_multilang_checker` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_option_display_styles`
--

DROP TABLE IF EXISTS `kazcms_option_display_styles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_option_display_styles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` json NOT NULL,
  `description` json DEFAULT NULL,
  `icon_class` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `enabled` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_option_display_styles`
--

LOCK TABLES `kazcms_option_display_styles` WRITE;
/*!40000 ALTER TABLE `kazcms_option_display_styles` DISABLE KEYS */;
INSERT INTO `kazcms_option_display_styles` VALUES (1,'select','{\"en-us\": \"Dropdown\", \"kz-kz\": \"Ашылмалы тізім\", \"zh-cn\": \"下拉菜单\"}','{\"en-us\": \"A single choice from dropdown.\", \"kz-kz\": \"Ашылмалы тізімнен біреуін таңдаңыз.\", \"zh-cn\": \"从下拉列表中选择一个选项。\"}','fa fa-caret-down',1,1,'2025-06-23 19:38:11','2025-06-23 19:38:11'),(2,'radio','{\"en-us\": \"Radio Buttons\", \"kz-kz\": \"Радио батырмалар\", \"zh-cn\": \"单选按钮\"}','{\"en-us\": \"A group of circular options where only one can be selected.\", \"kz-kz\": \"Тек біреуін таңдауға болатын дөңгелек опциялар.\", \"zh-cn\": \"只能选择一个的圆形选项组。\"}','fa fa-dot-circle',2,1,'2025-06-23 19:38:11','2025-06-23 19:38:11'),(3,'checkbox','{\"en-us\": \"Checkboxes\", \"kz-kz\": \"Белгі қою ұяшықтары\", \"zh-cn\": \"多选框\"}','{\"en-us\": \"Allows multiple selections from listed options.\", \"kz-kz\": \"Көп опцияны таңдауға мүмкіндік береді.\", \"zh-cn\": \"可以多选的选项列表。\"}','fa fa-check-square',3,1,'2025-06-23 19:38:11','2025-06-23 19:38:11');
/*!40000 ALTER TABLE `kazcms_option_display_styles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_password_reset_tokens`
--

DROP TABLE IF EXISTS `kazcms_password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_password_reset_tokens`
--

LOCK TABLES `kazcms_password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `kazcms_password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_permission_assignments`
--

DROP TABLE IF EXISTS `kazcms_permission_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_permission_assignments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `permission_id` int unsigned NOT NULL,
  `assignee_type` enum('user','group') COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignee_id` int unsigned NOT NULL,
  `created_by` bigint unsigned NOT NULL COMMENT '添加数据的用户ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_assignee` (`assignee_type`,`assignee_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `kazcms_permission_assignments_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_permission_assignments`
--

LOCK TABLES `kazcms_permission_assignments` WRITE;
/*!40000 ALTER TABLE `kazcms_permission_assignments` DISABLE KEYS */;
INSERT INTO `kazcms_permission_assignments` VALUES (3,3,'group',1,1,'2025-06-20 02:06:09','2025-06-20 02:06:09'),(4,12,'group',1,1,'2025-06-20 02:06:09','2025-06-20 02:06:09'),(9,5,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(10,6,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(11,10,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(12,11,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(13,4,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(14,5,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(15,6,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(16,10,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(17,11,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(20,11,'group',6,1,'2025-06-20 02:10:22','2025-06-20 02:10:22'),(21,12,'group',6,1,'2025-06-20 02:10:22','2025-06-20 02:10:22');
/*!40000 ALTER TABLE `kazcms_permission_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_permissions`
--

DROP TABLE IF EXISTS `kazcms_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int unsigned DEFAULT NULL COMMENT 'Parent permission ID, NULL if top-level',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Internal name like create_post, edit_product',
  `label` json NOT NULL COMMENT 'Display name in multiple languages',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional module or feature name',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `fk_permissions_parent` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_permissions`
--

LOCK TABLES `kazcms_permissions` WRITE;
/*!40000 ALTER TABLE `kazcms_permissions` DISABLE KEYS */;
INSERT INTO `kazcms_permissions` VALUES (1,NULL,'manage_elements','{\"en\": \"Manage Elements\", \"kz\": \"Элементтерді басқару\", \"zh\": \"元素管理\"}','element'),(3,1,'show_list','{\"en\": \"Show List\", \"kz\": \"Тізімді көрсету\", \"zh\": \"查看列表\"}','element'),(4,1,'add_update_elements','{\"en\": \"Add/Update Elements\", \"kz\": \"Элементтерді қосу/жаңарту\", \"zh\": \"添加/更新元素\"}','element'),(5,1,'delete_element','{\"en\": \"Delete Element\", \"kz\": \"Элементті жою\", \"zh\": \"删除元素\"}','element'),(6,1,'show_data_list','{\"en\": \"Show Data List\", \"kz\": \"Деректер тізімін көру\", \"zh\": \"查看数据列表\"}','element'),(7,1,'export_data','{\"en\": \"Export Data\", \"kz\": \"Деректерді экспорттау\", \"zh\": \"导出数据\"}','element'),(8,1,'add_edit_templates','{\"en\": \"Add/Edit Templates\", \"kz\": \"Үлгілерді қосу/өңдеу\", \"zh\": \"添加/编辑模板\"}','element'),(9,1,'manage_properties','{\"en\": \"Manage Properties\", \"kz\": \"Қасиеттерді басқару\", \"zh\": \"管理属性\"}','element'),(10,9,'add_update_properties','{\"en\": \"Add/Update Properties\", \"kz\": \"Қасиеттерді қосу/жаңарту\", \"zh\": \"添加/更新属性\"}','element'),(11,9,'delete_properties','{\"en\": \"Delete Properties\", \"kz\": \"Қасиеттерді жою\", \"zh\": \"删除属性\"}','element'),(12,9,'field_permission','{\"en\": \"Field Permission\", \"kz\": \"Өріс рұқсаттары\", \"zh\": \"字段权限\"}','element'),(13,9,'other_manage','{\"en\": \"Other Manage\", \"kz\": \"Басқа басқару\", \"zh\": \"其他管理\"}','element');
/*!40000 ALTER TABLE `kazcms_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_personal_access_tokens`
--

DROP TABLE IF EXISTS `kazcms_personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_personal_access_tokens`
--

LOCK TABLES `kazcms_personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `kazcms_personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_site_settings`
--

DROP TABLE IF EXISTS `kazcms_site_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_site_settings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `site_name` json NOT NULL COMMENT 'Website name (multi-language)',
  `site_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Full website URL, e.g., https://example.com',
  `meta_title` json NOT NULL COMMENT 'SEO meta title (multi-language)',
  `meta_keywords` json NOT NULL COMMENT 'SEO keywords, comma-separated (multi-language support)',
  `meta_description` json NOT NULL COMMENT 'SEO meta description (multi-language)',
  `default_language_id` int unsigned NOT NULL COMMENT 'Default language ID, linked to languages table',
  `maintenance_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is maintenance mode enabled',
  `maintenance_message` json DEFAULT NULL COMMENT 'Maintenance page message (multi-language)',
  `favicon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Favicon path or URL (32x32 recommended)',
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Logo path or URL (SVG or transparent PNG recommended)',
  `logo_alt_text` json DEFAULT NULL COMMENT 'Alt text for logo (multi-language)',
  `copyright` json DEFAULT NULL COMMENT 'Footer copyright text (multi-language)',
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact email address',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact phone number',
  `timezone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'UTC' COMMENT 'Website timezone, e.g. UTC, America/Montreal',
  `default_currency` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default currency code, e.g. USD, CAD',
  `open_graph_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Open Graph image URL (recommended 1200x630)',
  `robots_txt` text COLLATE utf8mb4_unicode_ci COMMENT 'Custom robots.txt content',
  `sitemap_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'URL to sitemap file',
  `social_links` json DEFAULT NULL COMMENT 'Social media links JSON, e.g. {"facebook": "...", "x": "..."}',
  `analytics_code` text COLLATE utf8mb4_unicode_ci COMMENT 'Analytics or other custom code snippets',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_multilang` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_default_language_id` (`default_language_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_site_settings`
--

LOCK TABLES `kazcms_site_settings` WRITE;
/*!40000 ALTER TABLE `kazcms_site_settings` DISABLE KEYS */;
INSERT INTO `kazcms_site_settings` VALUES (1,'{\"en-us\": \"KAZCMS Demo\", \"kz-kz\": \"KAZCMS Демо\", \"zh-cn\": \"KAZCMS演示站\"}','https://kazcms.com','{\"en-us\": \"Best CMS for Kazakhstan\", \"kz-kz\": \"Қазақстандағы үздік CMS\", \"zh-cn\": \"最好的内容管理系统\"}','{\"en-us\": \"cms,kazakhstan,kazcms\", \"kz-kz\": \"cms,қазақстан,kazcms\", \"zh-cn\": \"cms,哈萨克斯坦,kazcms\"}','{\"en-us\": \"KAZCMS is a multilingual content management system.\", \"kz-kz\": \"KAZCMS — көптілді CMS жүйесі.\", \"zh-cn\": \"KAZCMS 是一个多语言内容管理系统。\"}',1,0,'{\"en-us\": \"Site is under maintenance.\", \"kz-kz\": \"Сайт техникалық қызмет көрсетуде.\", \"zh-cn\": \"网站维护中。\"}','/assets/img/favicon.png','/assets/img/logo.svg','{\"en-us\": \"KAZCMS Logo\", \"kz-kz\": \"KAZCMS логотипі\", \"zh-cn\": \"KAZCMS 标志\"}','{\"en-us\": \"© 2025 KAZCMS\", \"kz-kz\": \"© 2025 KAZCMS\", \"zh-cn\": \"© 2025 KAZCMS\"}','<EMAIL>','+1-800-123-4567','America/Montreal','CAD','/assets/img/og.jpg','User-agent: *\nDisallow:','https://kazcms.com/sitemap.xml','{\"x\": \"https://x.com/kazcms\", \"facebook\": \"https://facebook.com/kazcms\"}','<script>console.log(\"Analytics loaded\")</script>','2025-06-23 14:22:30','2025-06-23 14:22:30',1);
/*!40000 ALTER TABLE `kazcms_site_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_user_groups`
--

DROP TABLE IF EXISTS `kazcms_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_user_groups` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `name` json NOT NULL COMMENT 'Group name in multiple languages, e.g. { "en": "Editor", "fr": "Éditeur" }',
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Direct parent group ID',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Pipe-separated ancestor group IDs, e.g. |1|3|5|',
  `level` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Depth level in the hierarchy (root is 0)',
  `is_leaf` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this is a leaf group (no children)',
  `sort_order` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Optional custom ordering within siblings',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hierarchical user groups with multi-language support';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_user_groups`
--

LOCK TABLES `kazcms_user_groups` WRITE;
/*!40000 ALTER TABLE `kazcms_user_groups` DISABLE KEYS */;
INSERT INTO `kazcms_user_groups` VALUES (1,'{\"en\": \"Administrator\", \"kz\": \"Әкімші\", \"zh\": \"管理员\"}',0,'|',0,0,1,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(2,'{\"en\": \"Editor\", \"kz\": \"Редактор\", \"zh\": \"编辑\"}',1,'|1|',1,1,2,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(3,'{\"en\": \"Author\", \"kz\": \"Автор\", \"zh\": \"作者\"}',1,'|1|',1,1,3,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(4,'{\"en\": \"Moderator\", \"kz\": \"Модератор\", \"zh\": \"审核员\"}',1,'|1|',1,1,4,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(5,'{\"en\": \"Guest\", \"kz\": \"Қонақ\", \"zh\": \"访客\"}',0,'|',0,1,5,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(6,'{\"en\": \"Registered\", \"kz\": \"Тіркелген қолданушы\", \"zh\": \"注册用户\"}',0,'|',0,1,6,'2025-06-19 02:03:04','2025-06-19 02:03:04'),(7,'{\"en\": \"VIP\", \"kz\": \"VIP қолданушы\", \"zh\": \"VIP用户\"}',0,'|',0,1,7,'2025-06-19 02:03:04','2025-06-19 02:03:04');
/*!40000 ALTER TABLE `kazcms_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_user_user_group`
--

DROP TABLE IF EXISTS `kazcms_user_user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_user_user_group` (
  `user_id` int unsigned NOT NULL,
  `group_id` int unsigned NOT NULL,
  PRIMARY KEY (`user_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_user_user_group`
--

LOCK TABLES `kazcms_user_user_group` WRITE;
/*!40000 ALTER TABLE `kazcms_user_user_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_user_user_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_users`
--

DROP TABLE IF EXISTS `kazcms_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_group_id` int NOT NULL DEFAULT '6',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_users`
--

LOCK TABLES `kazcms_users` WRITE;
/*!40000 ALTER TABLE `kazcms_users` DISABLE KEYS */;
INSERT INTO `kazcms_users` VALUES (1,'kz','<EMAIL>',NULL,'$2y$12$AnbhjL4U2XpuBStWDbV4uuTj7E595K1Mo.QzDivfKdKo66/XsCv36','218w4Rb8tqmQFpEcTG2HDfPArX5oxoyexSY697HYs5GW0MLU4bVJL3bqvd2G','2025-06-14 22:17:10','2025-06-14 22:17:10',6);
/*!40000 ALTER TABLE `kazcms_users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-11 12:41:53
