import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                //language
                'resources/lang/lang.js',
                
                //public

                //frontend
                'resources/frontend/css/app.css',
                'resources/frontend/css/kazcms.css',
                'resources/frontend/js/app.js',

                //admin
                'resources/admin/css/admin.css',
                'resources/admin/js/admin.js',
                'resources/admin/js/permissions.js',
                'resources/admin/js/language_checker.js',
                'resources/admin/js/content-form.js',
                'resources/admin/js/validatePublish.js',
                'resources/admin/js/filters/filter.js',

                //common
                'resources/common/kazcms-form-helper.js'
            ],
            refresh: true,
        }),
    ],
});
