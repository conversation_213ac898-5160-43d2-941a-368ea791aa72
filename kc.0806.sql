-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: kc
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `kazcms_allowed_search_fields`
--

DROP TABLE IF EXISTS `kazcms_allowed_search_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_allowed_search_fields` (
  `id` int NOT NULL AUTO_INCREMENT,
  `content_type_id` int NOT NULL COMMENT 'Entity or content type ID',
  `field_type_id` int NOT NULL COMMENT 'Field type ID',
  `field_id` int NOT NULL COMMENT 'Specific field ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_allowed_search` (`content_type_id`,`field_id`,`field_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Fields that are allowed to be used for search (only field_type_id in [1,2])';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_allowed_search_fields`
--

LOCK TABLES `kazcms_allowed_search_fields` WRITE;
/*!40000 ALTER TABLE `kazcms_allowed_search_fields` DISABLE KEYS */;
INSERT INTO `kazcms_allowed_search_fields` VALUES (4,1,2,2,'2025-06-28 13:46:41','2025-06-28 13:46:41'),(5,1,6,1,'2025-06-28 13:50:52','2025-06-28 13:50:52');
/*!40000 ALTER TABLE `kazcms_allowed_search_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_attachments`
--

DROP TABLE IF EXISTS `kazcms_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_attachments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'File storage path under public/uploads/...',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Renamed filename, e.g., hash123abc.jpg',
  `file_ext` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'File extension, e.g., png, pdf',
  `original_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Original uploaded filename',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'User-provided attachment title or description',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MIME type, e.g., image/png',
  `file_size` bigint DEFAULT '0' COMMENT 'File size in bytes',
  `user_id` bigint DEFAULT '0' COMMENT 'Uploader user ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record update timestamp',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=143 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='KazCMS attachments storage table';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_attachments`
--

LOCK TABLES `kazcms_attachments` WRITE;
/*!40000 ALTER TABLE `kazcms_attachments` DISABLE KEYS */;
INSERT INTO `kazcms_attachments` VALUES (1,'uploads/vpJHkSBCgsi0Hja3f9x6D8Fh3csAMgK9XoUUHsH7.png','vpJHkSBCgsi0Hja3f9x6D8Fh3csAMgK9XoUUHsH7.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(2,'uploads/SEF3ZV67sfygzS2tBUqX08JjSVNPgEj2jnVJMGcr.png','SEF3ZV67sfygzS2tBUqX08JjSVNPgEj2jnVJMGcr.png','png','c.png',NULL,'image/png',575030,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(3,'uploads/rt4t3Lu31FhcHU5PWhQe4eYRX415IudjMXLmi6c4.png','rt4t3Lu31FhcHU5PWhQe4eYRX415IudjMXLmi6c4.png','png','c.png',NULL,'image/png',575030,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(4,'uploads/pUnFop1Lo8iQDIIv3geoslG7MXr72hL6oO5X3mIX.png','pUnFop1Lo8iQDIIv3geoslG7MXr72hL6oO5X3mIX.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:18:54','2025-07-09 22:18:54'),(5,'uploads/Znk8VDLaWtu6rih9uUfpjNBZQfKV1hZhc133pR8M.png','Znk8VDLaWtu6rih9uUfpjNBZQfKV1hZhc133pR8M.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:23:06','2025-07-09 22:23:06'),(6,'uploads/vfzpbR1IKeIAGvHnUEZzO4XfFYXnR14Ee8WUkP6p.png','vfzpbR1IKeIAGvHnUEZzO4XfFYXnR14Ee8WUkP6p.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:23:06','2025-07-09 22:23:06'),(7,'uploads/2025/07/09/IUEMcX2ORCk8Ve5wQSXZzZDWAemokNcYSA80G8m3.png','IUEMcX2ORCk8Ve5wQSXZzZDWAemokNcYSA80G8m3.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-09 22:24:19','2025-07-09 22:24:19'),(8,'uploads/2025/07/09/0bIXIcmf0TtmXksXPqQRgSdF7Kg6NMeRo3F7CIgO.png','0bIXIcmf0TtmXksXPqQRgSdF7Kg6NMeRo3F7CIgO.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-09 22:24:19','2025-07-09 22:24:19'),(9,'uploads/2025/07/10/mMEsby8mTIy3DN8rTlPiABimk1zxtfKMOHpa8AFt.png','mMEsby8mTIy3DN8rTlPiABimk1zxtfKMOHpa8AFt.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(10,'uploads/2025/07/10/xMoCxulQDekoE2bYjsuIitZfrlUnF10Cgh7fYzPa.png','xMoCxulQDekoE2bYjsuIitZfrlUnF10Cgh7fYzPa.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(11,'uploads/2025/07/10/HCIYPgCP6QpiILLOM59tQqlhk75JoSlGiBdXn6Vg.png','HCIYPgCP6QpiILLOM59tQqlhk75JoSlGiBdXn6Vg.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(12,'uploads/2025/07/10/8oP5K0rghqfHavmJhMatXKI6bWymR0WR40FX8jyK.png','8oP5K0rghqfHavmJhMatXKI6bWymR0WR40FX8jyK.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(13,'uploads/2025/07/10/Zafn5y1RWzNrNbx1SckcaQgyweZ1RejQNngPYofe.png','Zafn5y1RWzNrNbx1SckcaQgyweZ1RejQNngPYofe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(14,'uploads/2025/07/10/rwx0MhHMCSbk7EFDZarHZae2ycjBNuMkrPVAGGZk.png','rwx0MhHMCSbk7EFDZarHZae2ycjBNuMkrPVAGGZk.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:19:15','2025-07-10 06:19:15'),(15,'uploads/2025/07/10/5F8UyHRqaxqjod40FQFNwaeXprjLDJlnSiF5D7Xd.png','5F8UyHRqaxqjod40FQFNwaeXprjLDJlnSiF5D7Xd.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(16,'uploads/2025/07/10/hlvMJmY4tz4TCX3E9goeoRpiKDLPT9Vhq5Jk6qNS.png','hlvMJmY4tz4TCX3E9goeoRpiKDLPT9Vhq5Jk6qNS.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(17,'uploads/2025/07/10/oAUBn60xIq7JTRNC3qeiRGRaJdpB7xFaa4maEmtm.png','oAUBn60xIq7JTRNC3qeiRGRaJdpB7xFaa4maEmtm.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(18,'uploads/2025/07/10/9KxAFJfRfu7ipqqvAo3tYcWb4prqE9grHg1zLcm6.png','9KxAFJfRfu7ipqqvAo3tYcWb4prqE9grHg1zLcm6.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(19,'uploads/2025/07/10/Rrrhq5c7Of2cPA1erCRl0OPlmTkYky5FPFRizzKl.png','Rrrhq5c7Of2cPA1erCRl0OPlmTkYky5FPFRizzKl.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(20,'uploads/2025/07/10/gYDwyXUTV0ErAFW8SGzm5OPe2NXYAX8rCldTzfgD.png','gYDwyXUTV0ErAFW8SGzm5OPe2NXYAX8rCldTzfgD.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:31:19','2025-07-10 06:31:19'),(21,'uploads/2025/07/10/d0IpF6BNxy86RvJsWmTtwp7sx5wB3jvh2Z7vOWCn.png','d0IpF6BNxy86RvJsWmTtwp7sx5wB3jvh2Z7vOWCn.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(22,'uploads/2025/07/10/GMzhookYySdBladQjGYUeuF5foX27Wrc5lJZEOPJ.png','GMzhookYySdBladQjGYUeuF5foX27Wrc5lJZEOPJ.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(23,'uploads/2025/07/10/Ye2fTXjg1HQIN3OqXBESi4Qgb27aX7ionLVtR7kd.png','Ye2fTXjg1HQIN3OqXBESi4Qgb27aX7ionLVtR7kd.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(24,'uploads/2025/07/10/2rXClgocnfQDyQ1ILNpAHm9Y5OeLEM2gxg0swESD.png','2rXClgocnfQDyQ1ILNpAHm9Y5OeLEM2gxg0swESD.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(25,'uploads/2025/07/10/sRGyL3Jsr2SK3vJ1o2s5LVwlWsSJN8Ja1Pwv3phu.png','sRGyL3Jsr2SK3vJ1o2s5LVwlWsSJN8Ja1Pwv3phu.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(26,'uploads/2025/07/10/7R4n6qPVBnxBOfFVjSt3VvPPQr66VH4ep1OYTbKT.png','7R4n6qPVBnxBOfFVjSt3VvPPQr66VH4ep1OYTbKT.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:51:21','2025-07-10 06:51:21'),(27,'uploads/2025/07/10/jMeMq62hOl5h18SZZtSlICeKfvd9JVxq0vxd6sd6.png','jMeMq62hOl5h18SZZtSlICeKfvd9JVxq0vxd6sd6.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(28,'uploads/2025/07/10/1Z0x3IFysiDkWa7b8y94VGCgQsv0mmOibOlvIqrv.png','1Z0x3IFysiDkWa7b8y94VGCgQsv0mmOibOlvIqrv.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(29,'uploads/2025/07/10/CcvXPvC5JagZdK1CdKitUQTQH36ytrtlLROtdJw0.png','CcvXPvC5JagZdK1CdKitUQTQH36ytrtlLROtdJw0.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(30,'uploads/2025/07/10/AZwPBOLcVn9rUR2tstwhG6nwxeaQaKINygNpEvff.png','AZwPBOLcVn9rUR2tstwhG6nwxeaQaKINygNpEvff.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(31,'uploads/2025/07/10/jtquffsvmgzpid1m1zFcKl42fvdTC711NIJMNHxV.png','jtquffsvmgzpid1m1zFcKl42fvdTC711NIJMNHxV.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(32,'uploads/2025/07/10/N5z3q0Nf98QskC5IAnv2CUVARk2vxQ58dSyVQCxw.png','N5z3q0Nf98QskC5IAnv2CUVARk2vxQ58dSyVQCxw.png','png','c.png',NULL,'image/png',575030,1,'2025-07-10 06:53:40','2025-07-10 06:53:40'),(33,'uploads/2025/07/11/NcqpjPjS8EA2UPehHNWO6ZSET2ScqJajrujsmcU7.png','NcqpjPjS8EA2UPehHNWO6ZSET2ScqJajrujsmcU7.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:45:05','2025-07-11 05:45:05'),(34,'uploads/2025/07/11/H3rNMP1eFoTZXUfYilA3CxiCyBeRiTmquIQBAqSO.png','H3rNMP1eFoTZXUfYilA3CxiCyBeRiTmquIQBAqSO.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:45:05','2025-07-11 05:45:05'),(35,'uploads/2025/07/11/kS1Tys74juBErjrGvL2y97yhNFZIfBRu0E6TKZ9U.png','kS1Tys74juBErjrGvL2y97yhNFZIfBRu0E6TKZ9U.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:49:22','2025-07-11 05:49:22'),(36,'uploads/2025/07/11/FtsQPhZYOgUqcFIxiYEMsFgj9byHb5mynwxWIrRw.png','FtsQPhZYOgUqcFIxiYEMsFgj9byHb5mynwxWIrRw.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:51:18','2025-07-11 05:51:18'),(37,'uploads/2025/07/11/45Hh7C0nWtyF81lqrw5m0xbx0mgxpkkUpkvMQA0D.png','45Hh7C0nWtyF81lqrw5m0xbx0mgxpkkUpkvMQA0D.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:52:55','2025-07-11 05:52:55'),(38,'uploads/2025/07/11/vIb9jdsTQMgZjnbU5re1GsDdJ807WK55Ho2BRWUT.png','vIb9jdsTQMgZjnbU5re1GsDdJ807WK55Ho2BRWUT.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:52:57','2025-07-11 05:52:57'),(39,'uploads/2025/07/11/CkfngRGOEdgIWDHhWkNXiH52f4KRHeAHH16tL4jl.png','CkfngRGOEdgIWDHhWkNXiH52f4KRHeAHH16tL4jl.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:57:04','2025-07-11 05:57:04'),(40,'uploads/2025/07/11/7hnFEAEBUuBvERYLUTUkV45gVibAzMua5BmHYhBe.png','7hnFEAEBUuBvERYLUTUkV45gVibAzMua5BmHYhBe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 05:59:58','2025-07-11 05:59:58'),(41,'uploads/2025/07/11/9xLqBRn6BQ9SqoYKhmnVkUkYDW4XQVPZPiQEqaof.png','9xLqBRn6BQ9SqoYKhmnVkUkYDW4XQVPZPiQEqaof.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:06:22','2025-07-11 06:06:22'),(42,'uploads/2025/07/11/09GOPi5dQBIofe2C93vFIE2RpYiy6sQUjzgTpYhe.png','09GOPi5dQBIofe2C93vFIE2RpYiy6sQUjzgTpYhe.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:17:25','2025-07-11 06:17:25'),(43,'uploads/2025/07/11/DnlLEMQGFUpoIcnf6gFlHu4lOhrFUnEgRQJNaE2l.png','DnlLEMQGFUpoIcnf6gFlHu4lOhrFUnEgRQJNaE2l.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:17:35','2025-07-11 06:17:35'),(44,'uploads/2025/07/11/VkCR5UTQnk3LkveHgpBX4eNfR7DYDHgSagoXe6si.png','VkCR5UTQnk3LkveHgpBX4eNfR7DYDHgSagoXe6si.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(45,'uploads/2025/07/11/95ku4CfIWoazW6ZLny42SeqzHTbtBstX2sm6EdTZ.png','95ku4CfIWoazW6ZLny42SeqzHTbtBstX2sm6EdTZ.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(46,'uploads/2025/07/11/WnH7K9GEm7LTUOlQAWvGtgmSwSH85PHEIa4EGuDX.png','WnH7K9GEm7LTUOlQAWvGtgmSwSH85PHEIa4EGuDX.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(47,'uploads/2025/07/11/c71yUQ1Sde4iDN4yIeNAUqbhOP9I62ciK7NVGbau.png','c71yUQ1Sde4iDN4yIeNAUqbhOP9I62ciK7NVGbau.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(48,'uploads/2025/07/11/M2YiiYyKxYIodTAWf49w2kCGBrS5Enb2IZ1wHBT4.png','M2YiiYyKxYIodTAWf49w2kCGBrS5Enb2IZ1wHBT4.png','png','c.png',NULL,'image/png',575030,1,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(49,'uploads/2025/07/11/Chijg8LrLxAosmNgQT3E8DKLmLzbtliVtRAhLQRV.png','Chijg8LrLxAosmNgQT3E8DKLmLzbtliVtRAhLQRV.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-11 20:35:46','2025-07-11 20:35:46'),(50,'uploads/2025/07/12/vpWg2MbC4BT6ceLYFoRNAdwos8gQpIUspM1bNkdp.png','vpWg2MbC4BT6ceLYFoRNAdwos8gQpIUspM1bNkdp.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 05:19:56','2025-07-12 05:19:56'),(51,'uploads/2025/07/12/lEerteZ5ct2kjPs53u3F1otZEq7gOfnWKWmBdAY4.png','lEerteZ5ct2kjPs53u3F1otZEq7gOfnWKWmBdAY4.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 05:39:36','2025-07-12 05:39:36'),(52,'uploads/2025/07/12/KkhKvYzK4Yf7yja5qGOlOnF9RnnpxUo0Q5BH5Tsv.png','KkhKvYzK4Yf7yja5qGOlOnF9RnnpxUo0Q5BH5Tsv.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:01:04','2025-07-12 06:01:04'),(53,'uploads/2025/07/12/6h5PXuu5kLTpDvzCreuDb7MPl2SY2D6xt7UIRuaH.png','6h5PXuu5kLTpDvzCreuDb7MPl2SY2D6xt7UIRuaH.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-12 06:01:04','2025-07-12 06:01:04'),(54,'uploads/2025/07/12/SncMCC7sCRyMs0JnF9qxoPSEd2rrr7Cho4PeYK8P.png','SncMCC7sCRyMs0JnF9qxoPSEd2rrr7Cho4PeYK8P.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:08:53','2025-07-12 06:08:53'),(55,'uploads/2025/07/12/7o4CaLJgtj3Tsd5LYskmrnotkZ4sWaRmtsklFzTe.png','7o4CaLJgtj3Tsd5LYskmrnotkZ4sWaRmtsklFzTe.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:08:53','2025-07-12 06:08:53'),(56,'uploads/2025/07/12/H2lanaXl1m6DBxUwP5BdEkp3vuhlcwVgt4iSrZQP.png','H2lanaXl1m6DBxUwP5BdEkp3vuhlcwVgt4iSrZQP.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:09:33','2025-07-12 06:09:33'),(57,'uploads/2025/07/12/z31RK6NzhWCzucr26wFHJ72h6IDZ0E9QdQZ5scQl.png','z31RK6NzhWCzucr26wFHJ72h6IDZ0E9QdQZ5scQl.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:09:33','2025-07-12 06:09:33'),(58,'uploads/2025/07/12/quUy9fZIBfQ0rvjnFcZ19UFzmJNJKLxY1THNgzt5.png','quUy9fZIBfQ0rvjnFcZ19UFzmJNJKLxY1THNgzt5.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:09:56','2025-07-12 06:09:56'),(59,'uploads/2025/07/12/TlQhALml49uzX8z9tCKMNN13T2a4XLAwjKmFCt2j.png','TlQhALml49uzX8z9tCKMNN13T2a4XLAwjKmFCt2j.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:09:56','2025-07-12 06:09:56'),(60,'uploads/2025/07/12/PsH6Pt9AAfBdyaSzbnHjd2j9F5aBnn22x58KCTmK.png','PsH6Pt9AAfBdyaSzbnHjd2j9F5aBnn22x58KCTmK.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:10:21','2025-07-12 06:10:21'),(61,'uploads/2025/07/12/OqboHyKICHWTTDNO0mVgf93JhYj282dMhYJzq2oh.png','OqboHyKICHWTTDNO0mVgf93JhYj282dMhYJzq2oh.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:10:21','2025-07-12 06:10:21'),(62,'uploads/2025/07/12/gPcth7VWRRPCEAxrHT9irYplcsusgjKMPNiGo9r5.png','gPcth7VWRRPCEAxrHT9irYplcsusgjKMPNiGo9r5.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:20:56','2025-07-12 06:20:56'),(63,'uploads/2025/07/12/96SZsikiWqU99pidDzYAIfBb8P0cntbwmU4SgIxM.png','96SZsikiWqU99pidDzYAIfBb8P0cntbwmU4SgIxM.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:20:56','2025-07-12 06:20:56'),(64,'uploads/2025/07/12/xsIoO3xylGG1YZ2O9735cN4nr3tYcheXlIt0ZJj0.png','xsIoO3xylGG1YZ2O9735cN4nr3tYcheXlIt0ZJj0.png','png','existing_1.png',NULL,'image/png',1169028,1,'2025-07-12 06:23:56','2025-07-12 06:23:56'),(65,'uploads/2025/07/12/E1wBBptSlgT07VSTAfQsgEwwwp31POcFOkIDNKVj.png','E1wBBptSlgT07VSTAfQsgEwwwp31POcFOkIDNKVj.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:23:56','2025-07-12 06:23:56'),(66,'uploads/2025/07/12/y4yA4XYANXGoIltYN1V2q0qAtYtO84B20C9nYpHg.png','y4yA4XYANXGoIltYN1V2q0qAtYtO84B20C9nYpHg.png','png','33dbcb0d-ed62-4e82-8268-bb87214a596a.png',NULL,'image/png',361501,1,'2025-07-12 06:34:04','2025-07-12 06:34:04'),(67,'uploads/2025/07/12/KV5l6ZRFTkTE8EdgWmr8PQx3D0zodM6IqLA4clp9.png','KV5l6ZRFTkTE8EdgWmr8PQx3D0zodM6IqLA4clp9.png','png','existing_2.png',NULL,'image/png',1089157,1,'2025-07-12 06:34:04','2025-07-12 06:34:04'),(68,'uploads/2025/07/12/gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png','png','ab38d81e-dc90-48d6-91fe-0963cd3f0503.png',NULL,'image/png',169950,1,'2025-07-12 06:34:49','2025-07-12 06:34:49'),(69,'uploads/2025/07/12/rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png','png','existing_1.png',NULL,'image/png',169950,1,'2025-07-12 20:26:01','2025-07-12 20:26:01'),(70,'uploads/2025/07/12/gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png','png','3c5b71bd-cb5f-474b-9b6e-96b8b7caf417.png',NULL,'image/png',229279,1,'2025-07-12 20:26:01','2025-07-12 20:26:01'),(71,'uploads/2025/07/13/CiJmXrpyIX1CqrlBYThVIZzUC4l4EUExgLGGg17P.png','CiJmXrpyIX1CqrlBYThVIZzUC4l4EUExgLGGg17P.png','png','a.png',NULL,'image/png',1169028,1,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(72,'uploads/2025/07/13/gKGoGSKeENDwilPJwaDzOBmoqw2EPfcaDEmgxITy.png','gKGoGSKeENDwilPJwaDzOBmoqw2EPfcaDEmgxITy.png','png','b.png',NULL,'image/png',1089157,1,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(73,'uploads/2025/07/13/mfrapCypN4ntn7rLkr8lbOYJDUCyt6C9uv8oIF6s.png','mfrapCypN4ntn7rLkr8lbOYJDUCyt6C9uv8oIF6s.png','png','c.png',NULL,'image/png',575030,1,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(74,'uploads/2025/07/16/PZnTLDfPVsbZ3zSnv9mZ8A0sovOsCpfvwHS6DcMx.png','PZnTLDfPVsbZ3zSnv9mZ8A0sovOsCpfvwHS6DcMx.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 20:20:50','2025-07-16 20:20:50'),(75,'uploads/2025/07/16/bsdV8STeIEixkMERSrse5Ct2No5eFz6a4JRKXk4W.png','bsdV8STeIEixkMERSrse5Ct2No5eFz6a4JRKXk4W.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 20:20:50','2025-07-16 20:20:50'),(76,'uploads/2025/07/16/RktpjdVwWuF9jHeNcuoet2Cexgz2obRPipxPr0Mt.png','RktpjdVwWuF9jHeNcuoet2Cexgz2obRPipxPr0Mt.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 20:20:50','2025-07-16 20:20:50'),(77,'uploads/2025/07/16/cQ4ULsadZN5mSd8dTVRyABfBfr8xN31mYZpZQYGs.png','cQ4ULsadZN5mSd8dTVRyABfBfr8xN31mYZpZQYGs.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 20:44:41','2025-07-16 20:44:41'),(78,'uploads/2025/07/16/l8J578ICvIFt39OWBHqqIKguUCWIliu9YfBDLRyQ.png','l8J578ICvIFt39OWBHqqIKguUCWIliu9YfBDLRyQ.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 20:44:41','2025-07-16 20:44:41'),(79,'uploads/2025/07/16/8nCUkue7w6frN0nLb4POwzIXZW1EzxyqHBOop2YF.png','8nCUkue7w6frN0nLb4POwzIXZW1EzxyqHBOop2YF.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 20:44:41','2025-07-16 20:44:41'),(80,'uploads/2025/07/16/1t37P00cGPs6qUlQxacX0D0Mg3zdvXMwPPm8tnb0.png','1t37P00cGPs6qUlQxacX0D0Mg3zdvXMwPPm8tnb0.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 20:46:25','2025-07-16 20:46:25'),(81,'uploads/2025/07/16/t8AS1fWw4yMCuf1LRIIsAVlYvc1nBv55RuDuyoTa.png','t8AS1fWw4yMCuf1LRIIsAVlYvc1nBv55RuDuyoTa.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 20:46:25','2025-07-16 20:46:25'),(82,'uploads/2025/07/16/FT5NY4arKHNt0KRhbjceWYt6UEpSvxDBkqXTSUwX.png','FT5NY4arKHNt0KRhbjceWYt6UEpSvxDBkqXTSUwX.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 20:46:25','2025-07-16 20:46:25'),(83,'uploads/2025/07/16/y2z0SGQAzGiK5AwuVm8rYHGqKPTf4yGd4LgLKB1u.png','y2z0SGQAzGiK5AwuVm8rYHGqKPTf4yGd4LgLKB1u.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 20:53:38','2025-07-16 20:53:38'),(84,'uploads/2025/07/16/M5HWFmBSX91YXAohXVSunnUw2irAlqlKoMuNVtvK.png','M5HWFmBSX91YXAohXVSunnUw2irAlqlKoMuNVtvK.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 20:53:38','2025-07-16 20:53:38'),(85,'uploads/2025/07/16/UbTD2QRzc6xYzUM5qUUToQUS5b9DoqvTW8kJj5lS.png','UbTD2QRzc6xYzUM5qUUToQUS5b9DoqvTW8kJj5lS.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 20:53:38','2025-07-16 20:53:38'),(86,'uploads/2025/07/16/LlBhgqmCS9hOvmpGSY26DaFNyby45LZtz8xB9CWX.png','LlBhgqmCS9hOvmpGSY26DaFNyby45LZtz8xB9CWX.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 21:00:19','2025-07-16 21:00:19'),(87,'uploads/2025/07/16/IlQOgfFWnbT7JwJwvpNxdlB9FgeFK0j4gjOuH4av.png','IlQOgfFWnbT7JwJwvpNxdlB9FgeFK0j4gjOuH4av.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 21:00:19','2025-07-16 21:00:19'),(88,'uploads/2025/07/16/EX0K0gjbYRMhia9OyjvhtAm9Vbs2vhkALL1rG3bW.png','EX0K0gjbYRMhia9OyjvhtAm9Vbs2vhkALL1rG3bW.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 21:00:19','2025-07-16 21:00:19'),(89,'uploads/2025/07/16/YsgJtVHsSsEjTIFFppZr4TfxESjkvZ9tNreD7zNt.png','YsgJtVHsSsEjTIFFppZr4TfxESjkvZ9tNreD7zNt.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 21:02:28','2025-07-16 21:02:28'),(90,'uploads/2025/07/16/o9gGmEGxHoK60Y3I2En6i1XTyutXvcttKSdJL9ak.png','o9gGmEGxHoK60Y3I2En6i1XTyutXvcttKSdJL9ak.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 21:02:28','2025-07-16 21:02:28'),(91,'uploads/2025/07/16/MlNq8UQUiT3kZyp1rI5okCWzUkzNdMzACM4AAKR3.png','MlNq8UQUiT3kZyp1rI5okCWzUkzNdMzACM4AAKR3.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 21:02:28','2025-07-16 21:02:28'),(92,'uploads/2025/07/16/sMiFfNyy2Xf7tWtohoxmKTxsNxk0lQCcJXbKbROq.png','sMiFfNyy2Xf7tWtohoxmKTxsNxk0lQCcJXbKbROq.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 21:03:14','2025-07-16 21:03:14'),(93,'uploads/2025/07/16/zF9CpGksNodczhsYCoxGlO61dmeqXXmjarfLikOV.png','zF9CpGksNodczhsYCoxGlO61dmeqXXmjarfLikOV.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 21:03:14','2025-07-16 21:03:14'),(94,'uploads/2025/07/16/xkUmGIeAXFIWUPyux0oWpznK0mLopEqj4IwCUv5s.png','xkUmGIeAXFIWUPyux0oWpznK0mLopEqj4IwCUv5s.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 21:03:14','2025-07-16 21:03:14'),(95,'uploads/2025/07/16/fhuyHheEzCKoZrXye4XKKVbpaLDlMNwO6hHVwdyI.png','fhuyHheEzCKoZrXye4XKKVbpaLDlMNwO6hHVwdyI.png','png','gHwA7THkK2XeAtultLSM7pRMvCqbVPSQgjtHS4In.png',NULL,'image/png',169950,1,'2025-07-16 21:04:26','2025-07-16 21:04:26'),(96,'uploads/2025/07/16/iCvT9IBn1iBAUvnlafCjFWO88IgmfYMsdjar3Kuu.png','iCvT9IBn1iBAUvnlafCjFWO88IgmfYMsdjar3Kuu.png','png','rMmKYIMEDcDJBBFsuMzP2MrBFxi70akXKXWrtsbT.png',NULL,'image/png',169950,1,'2025-07-16 21:04:26','2025-07-16 21:04:26'),(97,'uploads/2025/07/16/dCpIO6x38SPKLkgKobYNVBd4wH0aJmlGJUvFlx7u.png','dCpIO6x38SPKLkgKobYNVBd4wH0aJmlGJUvFlx7u.png','png','gveIWG0d8rZiKnAWtTNbFiIL7vEDGFTsGKhiHE5U.png',NULL,'image/png',229279,1,'2025-07-16 21:04:26','2025-07-16 21:04:26'),(98,'uploads/2025/07/16/g6CXSXxo8Q8QoQJZQoQjEGsDijwj171IY1B7WKGe.JPG','g6CXSXxo8Q8QoQJZQoQjEGsDijwj171IY1B7WKGe.JPG','JPG','6.JPG',NULL,'image/jpeg',52026,1,'2025-07-16 21:43:56','2025-07-16 21:43:56'),(99,'uploads/2025/07/16/Emr9YQTbIpnNCyK2xICKb4FeIvh11XdbzK3ksaqt.jpg','Emr9YQTbIpnNCyK2xICKb4FeIvh11XdbzK3ksaqt.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-16 21:43:56','2025-07-16 21:43:56'),(100,'uploads/2025/07/16/gF03S1MPKUE9PyHB0Evps0ou1Zrrd6JhVAjA3FA1.png','gF03S1MPKUE9PyHB0Evps0ou1Zrrd6JhVAjA3FA1.png','png','694cdf1f-9ae5-4bc8-a5cc-ab50daba3dd6.png',NULL,'image/png',80241,1,'2025-07-16 21:44:38','2025-07-16 21:44:38'),(101,'uploads/2025/07/16/rSCb1GM4zpzQ1RHEpLgpQEhl5z4Ri6X4IQWMObR6.png','rSCb1GM4zpzQ1RHEpLgpQEhl5z4Ri6X4IQWMObR6.png','png','32ba4302-d476-43e6-b77f-de074dbd773b.png',NULL,'image/png',164740,1,'2025-07-16 21:45:33','2025-07-16 21:45:33'),(102,'uploads/2025/07/17/7rgqnqRoafxrLMJei4ZDt64qD0230K3Et4gB0wOT.jpg','7rgqnqRoafxrLMJei4ZDt64qD0230K3Et4gB0wOT.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:38:41','2025-07-17 23:38:41'),(103,'uploads/2025/07/17/mNpkIjVe7Ik5VIQefcArhLIFq02LZ9iFSdxj0hFu.jpg','mNpkIjVe7Ik5VIQefcArhLIFq02LZ9iFSdxj0hFu.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:38:41','2025-07-17 23:38:41'),(104,'uploads/2025/07/17/TRplM4Eg4aFGt3NIQBmbkhzcewWWpuOfxSg731Aj.jpg','TRplM4Eg4aFGt3NIQBmbkhzcewWWpuOfxSg731Aj.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:38:41','2025-07-17 23:38:41'),(105,'uploads/2025/07/17/emf3cXowPp92BRFVwZAYarXrQMhwOyFAZfhmlebX.jpg','emf3cXowPp92BRFVwZAYarXrQMhwOyFAZfhmlebX.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:40:11','2025-07-17 23:40:11'),(106,'uploads/2025/07/17/JPLbo99jI0a6JTxC6DDo3TYelnHlaLuiyBIneZL0.jpg','JPLbo99jI0a6JTxC6DDo3TYelnHlaLuiyBIneZL0.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:40:11','2025-07-17 23:40:11'),(107,'uploads/2025/07/17/8CtxbpuQLHcmaNBC80E968XfOR5REXTqxB52skzb.jpg','8CtxbpuQLHcmaNBC80E968XfOR5REXTqxB52skzb.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:40:11','2025-07-17 23:40:11'),(108,'uploads/2025/07/17/PfzOqQUoZ5bo27LQ0MqWibhJUcYuZcNhPmU3PXsp.jpg','PfzOqQUoZ5bo27LQ0MqWibhJUcYuZcNhPmU3PXsp.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:45:18','2025-07-17 23:45:18'),(109,'uploads/2025/07/17/7TdSJcUPFUeiIVn8CqFKoFjlV13Rn488FoNOMAgB.jpg','7TdSJcUPFUeiIVn8CqFKoFjlV13Rn488FoNOMAgB.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:45:18','2025-07-17 23:45:18'),(110,'uploads/2025/07/17/8uXLMesUIwYAtkjV0UqHQy7l1y8MOenZq1oiJyRE.jpg','8uXLMesUIwYAtkjV0UqHQy7l1y8MOenZq1oiJyRE.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:45:18','2025-07-17 23:45:18'),(111,'uploads/2025/07/17/VxGdJgisI65UdECyilBkSi3Mwt7B2rFUnCp4GVQo.jpg','VxGdJgisI65UdECyilBkSi3Mwt7B2rFUnCp4GVQo.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:48:24','2025-07-17 23:48:24'),(112,'uploads/2025/07/17/Hk4LXAwyOZ0zk3bZKqnV62PvoiW06bsFnIcSlAAp.jpg','Hk4LXAwyOZ0zk3bZKqnV62PvoiW06bsFnIcSlAAp.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:48:24','2025-07-17 23:48:24'),(113,'uploads/2025/07/17/5EfTSXRSXWGBGiTqBFAIJI6HMwyWlBdTSb9AJYKo.jpg','5EfTSXRSXWGBGiTqBFAIJI6HMwyWlBdTSb9AJYKo.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:48:24','2025-07-17 23:48:24'),(114,'uploads/2025/07/17/5yMm7kstZdc9jLmTIDIaXtcP21ipCQGLrBvoRmq6.jpg','5yMm7kstZdc9jLmTIDIaXtcP21ipCQGLrBvoRmq6.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(115,'uploads/2025/07/17/47ZWjHuxPeFC9tWP5AKmMPY5SImEShzNFudD8Sjl.jpg','47ZWjHuxPeFC9tWP5AKmMPY5SImEShzNFudD8Sjl.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(116,'uploads/2025/07/17/9WnaBsRPyMeUk05pt4YGTFii5JKT91Ilb57jxxD4.jpg','9WnaBsRPyMeUk05pt4YGTFii5JKT91Ilb57jxxD4.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(117,'uploads/2025/07/17/2EzYGtouxcc6PRy4SdjpnyQh8Wr93riia4RSPift.jpg','2EzYGtouxcc6PRy4SdjpnyQh8Wr93riia4RSPift.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(118,'uploads/2025/07/17/9hydCC1bpTkozigqL5LZ4huC8z37VX9Vk37JK8lA.jpg','9hydCC1bpTkozigqL5LZ4huC8z37VX9Vk37JK8lA.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(119,'uploads/2025/07/17/8AK8DOZ8jFKZBhzM2mXctSIh1dXkwKqe4BWwUHLJ.jpg','8AK8DOZ8jFKZBhzM2mXctSIh1dXkwKqe4BWwUHLJ.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(120,'uploads/2025/07/17/oOE2ByrxPBRiBJfOl5fUf1ymO8xGa0m2jTWOyFGu.jpg','oOE2ByrxPBRiBJfOl5fUf1ymO8xGa0m2jTWOyFGu.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(121,'uploads/2025/07/17/uR6VT4PmtfutuQ7J63jOR2plqRxDXb0xa7RDUToV.jpg','uR6VT4PmtfutuQ7J63jOR2plqRxDXb0xa7RDUToV.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(122,'uploads/2025/07/17/b5TVlw48oZE3CELvWQiA4wCsbwgzUUZUiMosDLMd.jpg','b5TVlw48oZE3CELvWQiA4wCsbwgzUUZUiMosDLMd.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(123,'uploads/2025/07/17/aR107TyPeDsBRWKW3UpfSg8TXLul0pGCFatuPfTU.jpg','aR107TyPeDsBRWKW3UpfSg8TXLul0pGCFatuPfTU.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(124,'uploads/2025/07/17/tUvUqFX9URLqwGMhuTkzCcZ8bnP6IE6l9Ri1GyZm.jpg','tUvUqFX9URLqwGMhuTkzCcZ8bnP6IE6l9Ri1GyZm.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(125,'uploads/2025/07/17/HPvstDpJAWYCZasQnCpF8QbuJiEHbzVa4knThpZ5.jpg','HPvstDpJAWYCZasQnCpF8QbuJiEHbzVa4knThpZ5.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(126,'uploads/2025/07/17/eup9ugbZYHTOBydOOaEJLUeQIvTROzVoasJm5g7j.jpg','eup9ugbZYHTOBydOOaEJLUeQIvTROzVoasJm5g7j.jpg','jpg','existing_1.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:57:56','2025-07-17 23:57:56'),(127,'uploads/2025/07/17/YWL2goBp6QoFl2yystGBTfwz1fATODELblYJAiwN.jpg','YWL2goBp6QoFl2yystGBTfwz1fATODELblYJAiwN.jpg','jpg','existing_2.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:57:56','2025-07-17 23:57:56'),(128,'uploads/2025/07/17/s7frmNWjsHu2L4RUE5kj9tVv5w71ikph7hT1saHb.jpg','s7frmNWjsHu2L4RUE5kj9tVv5w71ikph7hT1saHb.jpg','jpg','existing_3.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:57:56','2025-07-17 23:57:56'),(129,'uploads/2025/07/17/pTPgxstEgFPFD9AKhtMIhMCjI13dhgf0T2V6AWMU.jpg','pTPgxstEgFPFD9AKhtMIhMCjI13dhgf0T2V6AWMU.jpg','jpg','existing_1.jpg',NULL,'image/jpeg',87797,1,'2025-07-17 23:58:24','2025-07-17 23:58:24'),(130,'uploads/2025/07/17/OUNLCHW2k2mjafBwKYI650WH0AjlZDZLec9lC6LQ.jpg','OUNLCHW2k2mjafBwKYI650WH0AjlZDZLec9lC6LQ.jpg','jpg','existing_2.jpg',NULL,'image/jpeg',58444,1,'2025-07-17 23:58:24','2025-07-17 23:58:24'),(131,'uploads/2025/07/17/ctyhvVZrSVYPICfD1CYHO06HRrXuP4SVTnqjZdrT.jpg','ctyhvVZrSVYPICfD1CYHO06HRrXuP4SVTnqjZdrT.jpg','jpg','existing_3.jpg',NULL,'image/jpeg',39310,1,'2025-07-17 23:58:24','2025-07-17 23:58:24'),(132,'uploads/2025/07/19/YDpOnP0q7oTFN6xFC7QMRUM5w2qP9ZTXNs20tzre.jpg','YDpOnP0q7oTFN6xFC7QMRUM5w2qP9ZTXNs20tzre.jpg','jpg','a.jpg',NULL,'image/jpeg',87797,1,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(133,'uploads/2025/07/19/RRs9r5FM20fi6p3NW1EzKCfTZrOWoaZ7g3v0x6Hm.jpg','RRs9r5FM20fi6p3NW1EzKCfTZrOWoaZ7g3v0x6Hm.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(134,'uploads/2025/07/19/c1N9Pc5nasXt7zNS00ZpdGsHDPxs1iEnRYr0EBeJ.jpg','c1N9Pc5nasXt7zNS00ZpdGsHDPxs1iEnRYr0EBeJ.jpg','jpg','c.jpg',NULL,'image/jpeg',39310,1,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(135,'uploads/2025/07/19/dTOnv1b93jZIcoIg2U8maHQODzjaRwwGBU5tAVP3.jpg','dTOnv1b93jZIcoIg2U8maHQODzjaRwwGBU5tAVP3.jpg','jpg','existing_1.jpg',NULL,'image/jpeg',87797,1,'2025-07-19 05:14:23','2025-07-19 05:14:23'),(136,'uploads/2025/07/19/E0rnvdOYD73eki2g9d0wJsH9hGOtb34kkH1xydO9.jpg','E0rnvdOYD73eki2g9d0wJsH9hGOtb34kkH1xydO9.jpg','jpg','existing_2.jpg',NULL,'image/jpeg',58444,1,'2025-07-19 05:14:23','2025-07-19 05:14:23'),(137,'uploads/2025/07/19/ar6L316HIUWKQvEeGcNXg5Vz416mtoIdlSqtDM1j.jpg','ar6L316HIUWKQvEeGcNXg5Vz416mtoIdlSqtDM1j.jpg','jpg','existing_3.jpg',NULL,'image/jpeg',39310,1,'2025-07-19 05:14:23','2025-07-19 05:14:23'),(138,'uploads/2025/07/19/z3U9uy2TK5lr9IzAevI0zB7vTYPbHgSz5lQwggkY.jpg','z3U9uy2TK5lr9IzAevI0zB7vTYPbHgSz5lQwggkY.jpg','jpg','existing_1.jpg',NULL,'image/jpeg',87797,1,'2025-07-19 05:21:23','2025-07-19 05:21:23'),(139,'uploads/2025/07/19/Pw0RDohZi3JFh481YfXNas0Mqef62m2BbrWGQvVj.jpg','Pw0RDohZi3JFh481YfXNas0Mqef62m2BbrWGQvVj.jpg','jpg','existing_2.jpg',NULL,'image/jpeg',58444,1,'2025-07-19 05:21:23','2025-07-19 05:21:23'),(140,'uploads/2025/07/19/flofgzW9nZDj969MXq4Ehz9bHzi0BGhf5nyP7ZkN.jpg','flofgzW9nZDj969MXq4Ehz9bHzi0BGhf5nyP7ZkN.jpg','jpg','existing_3.jpg',NULL,'image/jpeg',39310,1,'2025-07-19 05:21:23','2025-07-19 05:21:23'),(141,'uploads/2025/07/30/X9HBdbbLmMjIaNDURW8ZoM3KgFffObGwFKsX7Trl.jpg','X9HBdbbLmMjIaNDURW8ZoM3KgFffObGwFKsX7Trl.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(142,'uploads/2025/08/01/pVcF7cT2z0k4jmT9qEZb1hPFVieDFrGG4ALrde8l.jpg','pVcF7cT2z0k4jmT9qEZb1hPFVieDFrGG4ALrde8l.jpg','jpg','b.jpg',NULL,'image/jpeg',58444,1,'2025-08-01 15:54:29','2025-08-01 15:54:29');
/*!40000 ALTER TABLE `kazcms_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_categories`
--

DROP TABLE IF EXISTS `kazcms_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL COMMENT 'Multi-language category name',
  `slug` json NOT NULL COMMENT 'Multi-language slug/alias',
  `description` json DEFAULT NULL COMMENT 'Multi-language description',
  `category_id` int NOT NULL,
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Parent category ID, 0 for top-level',
  `parent_path_json` json DEFAULT NULL COMMENT 'Parent category path, e.g. |1||2||3|',
  `show_order` int NOT NULL DEFAULT '0' COMMENT 'Display order',
  `level` int NOT NULL DEFAULT '1' COMMENT 'Category depth level, starting from 1',
  `final_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is it a final category (no children allowed)?',
  `is_virtual` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this category a virtual group?',
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional icon or image URL',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_categories`
--

LOCK TABLES `kazcms_categories` WRITE;
/*!40000 ALTER TABLE `kazcms_categories` DISABLE KEYS */;
INSERT INTO `kazcms_categories` VALUES (1,'{\"en-us\": \"test\", \"kz-kz\": \"3\", \"zh-cn\": \"2\"}','{\"en-us\": \"4\", \"kz-kz\": \"6\", \"zh-cn\": \"5\"}','{\"en-us\": \"5\", \"kz-kz\": \"8\", \"zh-cn\": \"6\"}',0,0,NULL,0,1,1,1,NULL,'2025-06-26 18:15:58','2025-06-26 18:36:43'),(2,'{\"en-us\": \"fds\", \"kz-kz\": \"kz\", \"zh-cn\": \"中文\"}','{\"en-us\": \"abc\", \"kz-kz\": \"haha\", \"zh-cn\": \"def\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-26 18:17:46','2025-07-25 02:05:55'),(3,'{\"en-us\": \"test1\", \"kz-kz\": \"test2\", \"zh-cn\": \"test2\"}','{\"en-us\": \"abc\", \"kz-kz\": \"hhaa\", \"zh-cn\": \"def\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-26 18:36:18','2025-06-26 18:36:18'),(4,'{\"en-us\": \"hahaen\", \"kz-kz\": \"hahakz\", \"zh-cn\": \"hahacn\"}','{\"en-us\": \"english\", \"kz-kz\": \"haha\", \"zh-cn\": \"chinese\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,1,'\"[\\\"1\\\"]\"',0,2,1,1,NULL,'2025-06-26 18:36:43','2025-06-30 23:31:06'),(5,'{\"en-us\": \"fff\", \"kz-kz\": \"ff\", \"zh-cn\": \"ff\"}','{\"en-us\": \"ff\", \"kz-kz\": \"ff\", \"zh-cn\": \"ff\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,4,'\"[\\\"1\\\",\\\"4\\\"]\"',0,3,0,1,NULL,'2025-06-30 23:30:23','2025-06-30 23:30:23'),(6,'{\"en-us\": \"11\", \"kz-kz\": \"11\", \"zh-cn\": \"11\"}','{\"en-us\": \"22\", \"kz-kz\": \"22\", \"zh-cn\": \"222\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-30 23:30:37','2025-06-30 23:30:37'),(7,'{\"en-us\": \"22\", \"kz-kz\": \"22\", \"zh-cn\": \"22\"}','{\"en-us\": \"33\", \"kz-kz\": \"33\", \"zh-cn\": \"33\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,4,'\"[\\\"1\\\",\\\"4\\\"]\"',0,3,0,1,NULL,'2025-06-30 23:31:06','2025-06-30 23:31:06'),(8,'{\"en-us\": \"55\", \"kz-kz\": \"55\", \"zh-cn\": \"555\"}','{\"en-us\": \"66\", \"kz-kz\": \"66\", \"zh-cn\": \"77\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,0,1,NULL,'2025-06-30 23:31:27','2025-06-30 23:31:27'),(9,'{\"en-us\": \"2323\", \"kz-kz\": \"fds3232\", \"zh-cn\": \"fds3232\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,1,1,NULL,'2025-07-01 00:21:35','2025-07-01 00:22:55'),(10,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"中文名\"}','{\"en-us\": \"en\", \"kz-kz\": \"kz\", \"zh-cn\": \"cn\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',9,9,'\"[\\\"9\\\"]\"',0,2,1,1,NULL,'2025-07-01 00:22:01','2025-07-25 02:06:48'),(11,'{\"en-us\": \"fdsfdssfefew\", \"kz-kz\": \"fdsfdsrew\", \"zh-cn\": \"fdsfdsewrew\"}','{\"en-us\": \"fdsfdsfds2222222\", \"kz-kz\": \"dsfdsfdsfds222222222222\", \"zh-cn\": \"fdsfdsfdsf2222222222222222222222\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',9,9,'\"[\\\"9\\\"]\"',0,2,0,1,NULL,'2025-07-01 00:22:55','2025-07-01 00:22:55'),(12,'{\"en-us\": \"11\", \"kz-kz\": \"33\", \"zh-cn\": \"22\"}','{\"en-us\": \"33\", \"kz-kz\": \"55\", \"zh-cn\": \"44\"}','{\"en-us\": \"fdsfds\", \"kz-kz\": \"dsfds\", \"zh-cn\": \"fdsf\"}',9,10,'\"[\\\"9\\\",\\\"10\\\"]\"',0,3,1,1,NULL,'2025-07-24 18:53:06','2025-07-24 19:57:38'),(13,'{\"en-us\": \"bbbbb\", \"kz-kz\": \"bbb\", \"zh-cn\": \"bbb\"}','{\"en-us\": \"bbb\", \"kz-kz\": \"bbbbbbbbbb\", \"zh-cn\": \"bbbbbbbbbbbbbbbbb\"}','{\"en-us\": \"bbbbbbbbbbbbbb\", \"kz-kz\": \"bbbbbbbbbbbbbbbbbbb\", \"zh-cn\": \"bbbbbbbbbbbbbb\"}',9,10,'\"[\\\"9\\\",\\\"10\\\"]\"',0,3,0,1,NULL,'2025-07-24 19:50:47','2025-07-24 19:50:47'),(14,'{\"en-us\": \"book\", \"kz-kz\": \"ktab\", \"zh-cn\": \"书\"}','{\"en-us\": \"book\", \"kz-kz\": \"ktab\", \"zh-cn\": \"书\"}','{\"en-us\": \"c\", \"kz-kz\": \"ccccccccccccccccccccccccccc\", \"zh-cn\": \"cccccccccccccccc\"}',9,12,'\"[\\\"9\\\",\\\"10\\\",\\\"12\\\"]\"',0,4,0,1,NULL,'2025-07-24 19:57:38','2025-07-25 02:10:48'),(15,'{\"en-us\": \"bookcategories\", \"kz-kz\": \"ketap category\", \"zh-cn\": \"shu fenlei\"}','{\"en-us\": \"bookscategoires\", \"kz-kz\": \"ketap-fenlei\", \"zh-cn\": \"shu-fenlei\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,0,NULL,0,1,1,1,NULL,'2025-08-04 19:50:42','2025-08-04 19:51:52'),(16,'{\"en-us\": \"title\", \"kz-kz\": \"bas\", \"zh-cn\": \"mulu\"}','{\"en-us\": \"title\", \"kz-kz\": \"bask\", \"zh-cn\": \"mulu\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',15,15,'\"[\\\"15\\\"]\"',0,2,1,1,NULL,'2025-08-04 19:51:52','2025-08-04 19:52:29'),(17,'{\"en-us\": \"level2\", \"kz-kz\": \"level2\", \"zh-cn\": \"fenlei2\"}','{\"en-us\": \"level2\", \"kz-kz\": \"level2\", \"zh-cn\": \"fenlei2\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',15,16,'\"[\\\"15\\\",\\\"16\\\"]\"',0,3,0,1,NULL,'2025-08-04 19:52:29','2025-08-04 19:52:29');
/*!40000 ALTER TABLE `kazcms_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_content_form_settings`
--

DROP TABLE IF EXISTS `kazcms_content_form_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_content_form_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `content_type_id` int unsigned NOT NULL,
  `form_open_at` datetime DEFAULT NULL,
  `form_close_at` datetime DEFAULT NULL,
  `allow_edit_after_submit` tinyint(1) DEFAULT '0',
  `edit_start_at` datetime DEFAULT NULL,
  `edit_end_at` datetime DEFAULT NULL,
  `require_login` tinyint(1) DEFAULT '0',
  `anti_spam_protection` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `content_type_id` (`content_type_id`),
  CONSTRAINT `kazcms_content_form_settings_ibfk_1` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_content_form_settings`
--

LOCK TABLES `kazcms_content_form_settings` WRITE;
/*!40000 ALTER TABLE `kazcms_content_form_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_content_form_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_content_types`
--

DROP TABLE IF EXISTS `kazcms_content_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_content_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL COMMENT 'Content type name in multiple languages',
  `slug` json NOT NULL COMMENT 'Unique slug in multiple languages',
  `slug_en` varchar(120) COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`slug`,_utf8mb4'$.en'))) VIRTUAL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `display_order` int NOT NULL DEFAULT '0' COMMENT 'Order of display',
  `show_on_frontend` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether to show on frontend',
  `keywords` json DEFAULT NULL COMMENT 'SEO keywords in multiple languages',
  `description` json DEFAULT NULL COMMENT 'Description in multiple languages',
  `user_id` bigint unsigned NOT NULL COMMENT 'Owner user id',
  `render_type` enum('normal','form','document','') COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon name/class for display',
  `deleted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_slug_en` (`slug_en`),
  KEY `fk_user_id` (`user_id`),
  CONSTRAINT `fk_content_types_user` FOREIGN KEY (`user_id`) REFERENCES `kazcms_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Content types table with multi-language JSON fields';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_content_types`
--

LOCK TABLES `kazcms_content_types` WRITE;
/*!40000 ALTER TABLE `kazcms_content_types` DISABLE KEYS */;
INSERT INTO `kazcms_content_types` (`id`, `name`, `slug`, `created_at`, `display_order`, `show_on_frontend`, `keywords`, `description`, `user_id`, `render_type`, `icon`, `deleted_at`, `updated_at`) VALUES (1,'{\"en-us\": \"Blog\", \"kz-kz\": \"blog\", \"zh-cn\": \"博客\"}','{\"en-us\": \"blog\", \"kz-kz\": \"blog\", \"zh-cn\": \"boke\"}','2025-06-21 21:59:55',0,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}','{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'normal',NULL,'2025-06-21 17:59:55','2025-06-21 21:59:55'),(2,'{\"en-us\": \"aa\", \"kz-kz\": \"cc\", \"zh-cn\": \"vv\"}','{\"en-us\": \"dd\", \"kz-kz\": \"ff\", \"zh-cn\": \"ee\"}','2025-06-21 22:02:34',0,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}','{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'form',NULL,'2025-06-21 18:02:34','2025-06-21 22:02:34'),(3,'{\"en-us\": \"books\", \"kz-kz\": \"ktap\", \"zh-cn\": \"shu\"}','{\"en-us\": \"books\", \"kz-kz\": \"ktap\", \"zh-cn\": \"shu\"}','2025-08-04 19:48:43',0,0,'{\"en-us\": \"books\", \"kz-kz\": \"books\", \"zh-cn\": \"books\"}','{\"en-us\": \"books\", \"kz-kz\": \"ktap\", \"zh-cn\": \"shu\"}',1,'normal',NULL,'2025-08-04 15:48:43','2025-08-04 19:48:43');
/*!40000 ALTER TABLE `kazcms_content_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_document_content_versions`
--

DROP TABLE IF EXISTS `kazcms_document_content_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_document_content_versions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `content_type_id` int NOT NULL,
  `data_id` int NOT NULL,
  `version` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT '0',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_version` (`content_type_id`,`data_id`,`version`),
  KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_document_content_versions`
--

LOCK TABLES `kazcms_document_content_versions` WRITE;
/*!40000 ALTER TABLE `kazcms_document_content_versions` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_document_content_versions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_document_contents`
--

DROP TABLE IF EXISTS `kazcms_document_contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_document_contents` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL COMMENT 'Multi-language category/chapter name',
  `slug` json NOT NULL COMMENT 'Multi-language slug/alias',
  `data_id` int NOT NULL COMMENT 'Belongs to which book or field',
  `content_type_id` int NOT NULL,
  `content` json DEFAULT NULL COMMENT 'Multi-language chapter content body',
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Parent chapter ID, 0 for top-level',
  `parent_path_json` json DEFAULT NULL COMMENT 'Parent path as JSON array, e.g. [1,2,3]',
  `show_order` int NOT NULL DEFAULT '0' COMMENT 'Display order within siblings',
  `level` int NOT NULL DEFAULT '1' COMMENT 'Depth level, starting from 1',
  `final_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 is no children,1 has children',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_draft` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_document_contents`
--

LOCK TABLES `kazcms_document_contents` WRITE;
/*!40000 ALTER TABLE `kazcms_document_contents` DISABLE KEYS */;
INSERT INTO `kazcms_document_contents` VALUES (1,'{\"en-us\": \"1\", \"kz-kz\": \"5\", \"zh-cn\": \"3\"}','{\"en-us\": \"2\", \"kz-kz\": \"6\", \"zh-cn\": \"4\"}',118,0,NULL,0,'[]',0,1,0,'2025-08-05 19:09:22','2025-08-05 19:09:22',0),(2,'{\"en-us\": \"1\", \"kz-kz\": \"3\", \"zh-cn\": \"5\"}','{\"en-us\": \"2\", \"kz-kz\": \"4\", \"zh-cn\": \"6\"}',118,3,NULL,0,'[]',0,1,1,'2025-08-05 22:16:03','2025-08-05 22:16:03',0),(3,'{\"en-us\": \"w\", \"kz-kz\": \"d\", \"zh-cn\": \"z\"}','{\"en-us\": \"w\", \"kz-kz\": \"d\", \"zh-cn\": \"z\"}',118,3,NULL,0,'[]',0,1,1,'2025-08-05 22:16:42','2025-08-05 22:16:42',0),(4,'{\"en-us\": \"a\", \"kz-kz\": \"e\", \"zh-cn\": \"c\"}','{\"en-us\": \"b\", \"kz-kz\": \"f\", \"zh-cn\": \"d\"}',118,3,NULL,0,'[]',0,1,1,'2025-08-05 22:17:45','2025-08-05 22:17:45',0),(5,'{\"en-us\": \"c\", \"kz-kz\": \"f\", \"zh-cn\": \"e\"}','{\"en-us\": \"c\", \"kz-kz\": \"f\", \"zh-cn\": \"d\"}',118,3,NULL,1,'[1]',0,2,1,'2025-08-05 22:19:13','2025-08-05 22:19:13',0),(6,'{\"en-us\": \"cc\", \"kz-kz\": \"ff\", \"zh-cn\": \"dd\"}','{\"en-us\": \"cc\", \"kz-kz\": \"ff\", \"zh-cn\": \"dd\"}',118,3,NULL,2,'[2]',0,2,0,'2025-08-05 22:57:27','2025-08-05 22:57:27',0),(7,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"gd\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fds\"}',118,3,NULL,4,'[4]',0,2,0,'2025-08-05 22:57:41','2025-08-05 22:57:41',0),(8,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfdsfds\", \"zh-cn\": \"fdsfds\"}',118,3,NULL,6,'[2, 6]',0,3,0,'2025-08-05 22:57:53','2025-08-05 22:57:53',0);
/*!40000 ALTER TABLE `kazcms_document_contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_entry_fields`
--

DROP TABLE IF EXISTS `kazcms_entry_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_entry_fields` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `entry_id` int unsigned NOT NULL COMMENT 'ID of the content entry this field belongs to',
  `field_id` int unsigned NOT NULL COMMENT 'ID of the field definition',
  `file_type_id` int unsigned DEFAULT NULL COMMENT 'Optional: ID of the file type if this is a file field',
  `content_type_id` int unsigned NOT NULL COMMENT 'ID of the content type',
  `data` json DEFAULT NULL COMMENT 'Field value in JSON format (for multilingual, multiselect, etc.)',
  `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID of the user who submitted the data',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_entry_id` (`entry_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type_id` (`content_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_entry_fields`
--

LOCK TABLES `kazcms_entry_fields` WRITE;
/*!40000 ALTER TABLE `kazcms_entry_fields` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_entry_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_entry_fields_backup`
--

DROP TABLE IF EXISTS `kazcms_entry_fields_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_entry_fields_backup` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `entry_id` int unsigned NOT NULL,
  `field_id` int unsigned NOT NULL,
  `file_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `data` json DEFAULT NULL,
  `user_id` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_entry_id` (`entry_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type_id` (`content_type_id`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_entry_fields_backup`
--

LOCK TABLES `kazcms_entry_fields_backup` WRITE;
/*!40000 ALTER TABLE `kazcms_entry_fields_backup` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_entry_fields_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_failed_jobs`
--

DROP TABLE IF EXISTS `kazcms_failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_failed_jobs`
--

LOCK TABLES `kazcms_failed_jobs` WRITE;
/*!40000 ALTER TABLE `kazcms_failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_categories`
--

DROP TABLE IF EXISTS `kazcms_field_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `label_json` json NOT NULL COMMENT 'Field name, multi-language',
  `label_slug_json` json NOT NULL,
  `category_id` int NOT NULL,
  `display_style` enum('radio','checkbox') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'radio' COMMENT 'Option type: single or multiple select',
  `show_in_frontend` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Show in frontend (tree display)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is field required',
  `max_select_count` int unsigned DEFAULT NULL COMMENT 'Max selection count for multiple select, null means unlimited',
  `content_type_id` int unsigned NOT NULL COMMENT 'Related content type ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_categories`
--

LOCK TABLES `kazcms_field_categories` WRITE;
/*!40000 ALTER TABLE `kazcms_field_categories` DISABLE KEYS */;
INSERT INTO `kazcms_field_categories` VALUES (6,4,'{\"en-us\": \"english name\", \"kz-kz\": \"kazakh\", \"zh-cn\": \"中文名\"}','{\"en-us\": \"english-name\", \"kz-kz\": \"kazakh\", \"zh-cn\": \"zhongwen\"}',1,'checkbox',1,1,10,1,'2025-06-30 22:26:57','2025-07-25 16:20:40'),(7,4,'{\"en-us\": \"fdfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','{\"en-us\": \"fdfds\", \"kz-kz\": \"fdsfdskz\", \"zh-cn\": \"fdsfds\"}',1,'checkbox',1,1,10,1,'2025-07-01 00:19:00','2025-07-26 16:02:02'),(8,4,'{\"en-us\": \"ddd\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"ffsfd\"}','{\"en-us\": \"ddd\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"ffsfd\"}',9,'checkbox',1,1,23,1,'2025-07-01 00:19:37','2025-07-25 01:48:07'),(9,4,'{\"en-us\": \"muluen\", \"kz-kz\": \"mulukz\", \"zh-cn\": \"mulucn\"}','{\"en-us\": \"muluen\", \"kz-kz\": \"mulukz\", \"zh-cn\": \"mulucn\"}',1,'radio',1,0,NULL,3,'2025-08-04 20:00:34','2025-08-04 20:00:34');
/*!40000 ALTER TABLE `kazcms_field_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_codes`
--

DROP TABLE IF EXISTS `kazcms_field_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_codes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL COMMENT 'Related content type ID',
  `label_json` json NOT NULL COMMENT 'Field name (multi-language)',
  `html_code_json` json NOT NULL COMMENT 'HTML or code block (multi-language)',
  `description_json` json DEFAULT NULL COMMENT 'Code block description (multi-language)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is this code block enabled?',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`),
  CONSTRAINT `fk_field_codes_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_codes`
--

LOCK TABLES `kazcms_field_codes` WRITE;
/*!40000 ALTER TABLE `kazcms_field_codes` DISABLE KEYS */;
INSERT INTO `kazcms_field_codes` VALUES (4,6,1,'{\"en-us\": \"this is a code\", \"kz-kz\": \"balala\", \"zh-cn\": \"中文一段代码\"}','{\"en-us\": \"<h1>englihs</h1>\\r\\n<input type=\\\"text\\\">\", \"kz-kz\": \"<h1>kazakh</h1>\", \"zh-cn\": \"<h1>中文</h1>\"}','{\"en-us\": \"this is Description\", \"kz-kz\": \"aahajja\", \"zh-cn\": \"这是介绍\"}',1,'2025-07-01 21:00:45','2025-07-11 19:11:31'),(6,6,2,'{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}','{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}','{\"en-us\": \"ddddddddddddddddd\", \"kz-kz\": \"ddddddddddddddddd\", \"zh-cn\": \"ddddddddddddddddd\"}',1,'2025-07-11 19:03:53','2025-07-11 19:03:53');
/*!40000 ALTER TABLE `kazcms_field_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_data`
--

DROP TABLE IF EXISTS `kazcms_field_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_id` bigint NOT NULL COMMENT '属于同一内容项的ID',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '创建该内容的用户ID（用于归属和权限）',
  `content_type_id` bigint NOT NULL COMMENT '内容类型ID',
  `field_id` bigint NOT NULL COMMENT '字段ID',
  `field_type_id` int NOT NULL COMMENT '字段类型ID（如文本、富文本、图片等）',
  `data` json NOT NULL COMMENT '字段内容，支持多语言结构，如 { "en": "...", "fr": "..." }',
  `extra_config` json DEFAULT NULL COMMENT 'User config JSON, e.g. { "default_expanded": true }',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_content_type` (`content_type_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段值存储表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_data`
--

LOCK TABLES `kazcms_field_data` WRITE;
/*!40000 ALTER TABLE `kazcms_field_data` DISABLE KEYS */;
INSERT INTO `kazcms_field_data` VALUES (1,0,1,1,0,0,'{}',NULL,'2025-07-11 06:34:33','2025-07-11 06:34:33'),(10,0,1,1,0,0,'{}',NULL,'2025-07-11 06:36:23','2025-07-11 06:36:23'),(19,0,1,1,0,0,'{}',NULL,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(20,19,1,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(21,19,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(22,19,1,1,7,4,'{\"all\": [7]}',NULL,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(23,19,1,1,6,4,'{\"all\": [5]}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(24,19,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(25,19,1,1,4,5,'{\"all\": \"32\"}',NULL,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(26,19,1,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(27,19,1,1,6,5,'{\"all\": [\"fds\"]}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(28,19,1,1,7,3,'{\"en-us\": [69, 70], \"kz-kz\": [], \"zh-cn\": []}',NULL,'2025-07-11 06:38:00','2025-07-12 20:26:01'),(29,19,1,1,6,3,'{\"en-us\": [46, 47, 48], \"kz-kz\": [46, 47, 48], \"zh-cn\": [46, 47, 48]}',NULL,'2025-07-11 06:38:00','2025-07-11 06:38:00'),(30,0,1,1,0,0,'{}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(31,30,1,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"zh-cn\": \"dddddddddddddddddddddddddddddddddddddddddddd\"}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(32,30,1,1,5,2,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"zh-cn\": \"dddddddddddddddddddddddddddddddddddddddddddd\"}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(33,30,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(34,30,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(35,30,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(36,30,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(37,30,1,1,7,3,'{\"en-us\": [71, 72, 73], \"kz-kz\": [71, 72, 73], \"zh-cn\": [71, 72, 73]}',NULL,'2025-07-14 02:36:32','2025-07-14 02:36:32'),(38,0,1,1,0,0,'{}',NULL,'2025-07-15 23:18:07','2025-07-15 23:18:07'),(39,38,1,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',NULL,'2025-07-15 23:18:07','2025-07-15 23:18:07'),(40,38,1,1,8,4,'{\"all\": [10, 11]}',NULL,'2025-07-15 23:18:07','2025-07-15 23:18:07'),(41,38,1,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',NULL,'2025-07-15 23:18:07','2025-07-15 23:18:07'),(42,38,1,1,6,4,'{\"all\": [5]}',NULL,'2025-07-15 23:18:07','2025-07-15 23:18:07'),(47,0,1,1,0,0,'{}',NULL,'2025-07-16 21:00:19','2025-07-16 21:00:19'),(48,0,1,1,0,0,'{}',NULL,'2025-07-16 21:02:28','2025-07-16 21:02:28'),(49,0,1,1,0,0,'{}',NULL,'2025-07-16 21:03:14','2025-07-16 21:03:14'),(50,0,1,1,0,0,'{}',NULL,'2025-07-16 21:04:25','2025-07-16 21:04:25'),(51,0,1,1,0,0,'{}',NULL,'2025-07-16 21:40:44','2025-07-16 21:40:44'),(52,0,1,1,0,0,'{}',NULL,'2025-07-16 21:43:56','2025-07-16 21:43:56'),(53,0,1,1,0,0,'{}',NULL,'2025-07-16 21:44:38','2025-07-16 21:44:38'),(54,0,1,1,0,0,'{}',NULL,'2025-07-16 21:45:33','2025-07-16 21:45:33'),(55,0,1,1,0,0,'{}',NULL,'2025-07-16 21:56:23','2025-07-16 21:56:23'),(56,0,1,1,0,0,'{}',NULL,'2025-07-16 22:09:11','2025-07-16 22:09:11'),(57,0,1,1,0,0,'{}',NULL,'2025-07-17 23:38:41','2025-07-17 23:38:41'),(58,0,1,1,0,0,'{}',NULL,'2025-07-17 23:40:11','2025-07-17 23:40:11'),(59,0,1,1,0,0,'{}',NULL,'2025-07-17 23:45:18','2025-07-17 23:45:18'),(60,0,1,1,0,0,'{}',NULL,'2025-07-17 23:48:24','2025-07-17 23:48:24'),(61,0,1,1,0,0,'{}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(62,61,1,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(63,61,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(64,61,1,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(65,61,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(66,61,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(67,61,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(68,61,1,1,7,3,'{\"en-us\": [114, 115, 116], \"kz-kz\": [114, 115, 116], \"zh-cn\": [114, 115, 116]}',NULL,'2025-07-17 23:48:35','2025-07-17 23:48:35'),(69,0,1,1,0,0,'{}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(70,69,1,1,19,1,'{\"en-us\": \"i-want\", \"kz-kz\": \"test\", \"zh-cn\": \"woceshi\"}',NULL,'2025-07-17 23:52:32','2025-08-01 19:51:07'),(71,69,1,1,8,4,'{\"all\": [14]}',NULL,'2025-07-17 23:52:32','2025-07-30 22:27:25'),(72,69,1,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(73,69,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(74,69,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(75,69,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(76,69,1,1,7,3,'{\"en-us\": [117, 118, 119], \"kz-kz\": [117, 118, 119], \"zh-cn\": [117, 118, 119]}',NULL,'2025-07-17 23:52:32','2025-07-17 23:52:32'),(77,0,1,1,0,0,'{}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(78,77,1,1,19,1,'{\"en-us\": \"new-test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',NULL,'2025-07-17 23:53:28','2025-08-01 20:17:29'),(79,77,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(80,77,1,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(81,77,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(82,77,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(83,77,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(84,77,1,1,7,3,'{\"en-us\": [120, 121, 122], \"kz-kz\": [120, 121, 122], \"zh-cn\": [120, 121, 122]}',NULL,'2025-07-17 23:53:28','2025-07-17 23:53:28'),(85,0,1,1,0,0,'{}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(86,85,1,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(87,85,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(88,85,1,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(89,85,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(90,85,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(91,85,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(92,85,1,1,7,3,'{\"en-us\": [123, 124, 125], \"kz-kz\": [123, 124, 125], \"zh-cn\": [123, 124, 125]}',NULL,'2025-07-17 23:55:02','2025-07-17 23:55:02'),(93,0,1,1,0,0,'{}',NULL,'2025-07-19 05:06:06','2025-07-19 05:06:06'),(94,0,1,1,0,0,'{}',NULL,'2025-07-19 05:11:36','2025-07-19 05:11:36'),(95,0,1,1,0,0,'{}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(96,95,1,1,19,1,'{\"en-us\": \"在线上测试一次 dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',NULL,'2025-07-19 05:12:54','2025-07-19 01:15:18'),(97,95,1,1,8,4,'{\"all\": [10]}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(98,95,1,1,5,2,'{\"en-us\": \"FDFSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(99,95,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(100,95,1,1,6,4,'{\"all\": [5]}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(101,95,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(102,95,1,1,7,3,'{\"en-us\": [132, 133, 134], \"kz-kz\": [], \"zh-cn\": []}',NULL,'2025-07-19 05:12:54','2025-07-19 05:12:54'),(103,1,1,1,19,1,'{\"en-us\": \"地地地地地地地你地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(104,1,1,1,8,4,'{\"all\": [13]}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(105,1,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(106,1,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(107,1,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(108,1,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(109,1,1,1,7,3,'{\"en-us\": [141], \"kz-kz\": [], \"zh-cn\": []}',NULL,'2025-07-30 21:14:20','2025-07-30 21:14:20'),(110,10,1,1,20,1,'{\"en-us\": \"this is a title\", \"kz-kz\": \"请输入标题\", \"zh-cn\": \"请输入标题\"}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(111,10,1,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地好地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(112,10,1,1,8,4,'{\"all\": [10]}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(113,10,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(114,10,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(115,10,1,1,6,4,'{\"all\": [5, 7]}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(116,10,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(117,10,1,1,7,3,'{\"en-us\": [142], \"kz-kz\": [142], \"zh-cn\": [142]}',NULL,'2025-08-01 15:54:29','2025-08-01 15:54:29'),(118,0,1,3,0,0,'{}','{\"expand_level\": 3}','2025-08-05 05:44:10','2025-08-05 18:58:48'),(119,118,1,3,21,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsrewfds\", \"zh-cn\": \"vcxvcx\"}',NULL,'2025-08-05 05:44:10','2025-08-05 01:44:52'),(120,118,1,3,9,4,'{\"all\": [5]}',NULL,'2025-08-05 05:44:10','2025-08-05 05:44:10');
/*!40000 ALTER TABLE `kazcms_field_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_data_v`
--

DROP TABLE IF EXISTS `kazcms_field_data_v`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_data_v` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_id` bigint NOT NULL COMMENT '内容 ID',
  `content_type_id` bigint NOT NULL COMMENT '内容类型 ID',
  `field_id` bigint NOT NULL COMMENT '字段 ID',
  `field_type_id` int NOT NULL COMMENT '字段类型 ID',
  `data` json NOT NULL COMMENT '字段值（新版值）',
  `version` int NOT NULL COMMENT '版本号，从1递增',
  `status` enum('draft','published','archived','deleted') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft' COMMENT '状态',
  `is_current` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否当前线上版本',
  `changed_by` bigint DEFAULT NULL COMMENT '修改人 ID（user_id）',
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段数据版本表，支持草稿、历史、发布版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_data_v`
--

LOCK TABLES `kazcms_field_data_v` WRITE;
/*!40000 ALTER TABLE `kazcms_field_data_v` DISABLE KEYS */;
INSERT INTO `kazcms_field_data_v` VALUES (3,49,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:03:14'),(5,50,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:04:26'),(6,50,1,8,4,'{\"all\": [10]}',1,'draft',0,1,'2025-07-16 21:04:26'),(7,50,1,5,2,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:04:26'),(8,50,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-16 21:04:26'),(9,50,1,6,4,'{\"all\": [5]}',1,'draft',0,1,'2025-07-16 21:04:26'),(10,50,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-16 21:04:26'),(11,50,1,7,3,'{\"en-us\": [95, 96, 97], \"kz-kz\": [], \"zh-cn\": []}',1,'draft',0,1,'2025-07-16 21:04:26'),(12,51,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:40:44'),(13,51,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:40:44'),(14,51,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:40:44'),(15,51,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:40:44'),(16,52,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:43:56'),(17,52,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:43:56'),(18,52,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:43:56'),(19,52,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:43:56'),(20,52,1,7,3,'{\"en-us\": [98, 99], \"kz-kz\": [], \"zh-cn\": []}',1,'draft',0,1,'2025-07-16 21:43:56'),(21,53,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:44:38'),(22,53,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:44:38'),(23,53,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:44:38'),(24,53,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:44:38'),(25,53,1,7,3,'{\"en-us\": [100], \"kz-kz\": [], \"zh-cn\": []}',1,'draft',0,1,'2025-07-16 21:44:38'),(26,54,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:45:33'),(27,54,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:45:33'),(28,54,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:45:33'),(29,54,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:45:33'),(30,54,1,7,3,'{\"en-us\": [101], \"kz-kz\": [], \"zh-cn\": []}',1,'draft',0,1,'2025-07-16 21:45:33'),(31,55,1,19,1,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:56:23'),(32,55,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:56:23'),(33,55,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 21:56:23'),(34,55,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 21:56:23'),(35,56,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 22:09:11'),(36,56,1,8,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 22:09:11'),(37,56,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-16 22:09:11'),(38,56,1,6,4,'{\"all\": []}',1,'draft',0,1,'2025-07-16 22:09:11'),(39,1,1,19,1,'{\"en-us\": \"[草稿]地地地地地地地你地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'draft',0,1,'2025-07-17 22:36:09'),(40,1,1,8,4,'{\"all\": [10]}',1,'draft',0,1,'2025-07-17 22:36:09'),(41,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'draft',0,1,'2025-07-17 22:36:09'),(42,1,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 22:36:09'),(43,1,1,6,4,'{\"all\": [5, 7]}',1,'draft',0,1,'2025-07-17 22:36:09'),(44,1,1,6,5,'{\"all\": [\"fds\", \"fds\", \"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 22:36:09'),(45,57,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-17 23:38:41'),(46,57,1,8,4,'{\"all\": [10]}',1,'draft',0,1,'2025-07-17 23:38:41'),(47,57,1,5,2,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',0,1,'2025-07-17 23:38:41'),(48,57,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 23:38:41'),(49,57,1,6,4,'{\"all\": [5, 7]}',1,'draft',0,1,'2025-07-17 23:38:41'),(50,57,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 23:38:41'),(51,57,1,7,3,'{\"en-us\": [102, 103, 104], \"kz-kz\": [], \"zh-cn\": []}',1,'draft',0,1,'2025-07-17 23:38:41'),(52,58,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'draft',0,1,'2025-07-17 23:40:11'),(53,58,1,8,4,'{\"all\": [10]}',1,'draft',0,1,'2025-07-17 23:40:11'),(54,58,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'draft',0,1,'2025-07-17 23:40:11'),(55,58,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 23:40:11'),(56,58,1,6,4,'{\"all\": [5, 7]}',1,'draft',0,1,'2025-07-17 23:40:11'),(57,58,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',0,1,'2025-07-17 23:40:11'),(58,58,1,7,3,'{\"en-us\": [105, 106, 107], \"kz-kz\": [105, 106, 107], \"zh-cn\": [105, 106, 107]}',1,'draft',0,1,'2025-07-17 23:40:11'),(59,59,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'draft',1,1,'2025-07-17 23:45:18'),(60,59,1,8,4,'{\"all\": [10]}',1,'draft',1,1,'2025-07-17 23:45:18'),(61,59,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'draft',1,1,'2025-07-17 23:45:18'),(62,59,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',1,1,'2025-07-17 23:45:18'),(63,59,1,6,4,'{\"all\": [5, 7]}',1,'draft',1,1,'2025-07-17 23:45:18'),(64,59,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',1,1,'2025-07-17 23:45:18'),(65,59,1,7,3,'{\"en-us\": [108, 109, 110], \"kz-kz\": [108, 109, 110], \"zh-cn\": [108, 109, 110]}',1,'draft',1,1,'2025-07-17 23:45:18'),(66,60,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'draft',1,1,'2025-07-17 23:48:24'),(67,60,1,8,4,'{\"all\": [10]}',1,'draft',1,1,'2025-07-17 23:48:24'),(68,60,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'draft',1,1,'2025-07-17 23:48:24'),(69,60,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',1,1,'2025-07-17 23:48:24'),(70,60,1,6,4,'{\"all\": [5, 7]}',1,'draft',1,1,'2025-07-17 23:48:24'),(71,60,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'draft',1,1,'2025-07-17 23:48:24'),(72,60,1,7,3,'{\"en-us\": [111, 112, 113], \"kz-kz\": [111, 112, 113], \"zh-cn\": [111, 112, 113]}',1,'draft',1,1,'2025-07-17 23:48:24'),(73,61,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'published',1,1,'2025-07-17 23:48:35'),(74,61,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-07-17 23:48:35'),(75,61,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'published',1,1,'2025-07-17 23:48:35'),(76,61,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:48:35'),(77,61,1,6,4,'{\"all\": [5, 7]}',1,'published',1,1,'2025-07-17 23:48:35'),(78,61,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:48:35'),(79,61,1,7,3,'{\"en-us\": [114, 115, 116], \"kz-kz\": [114, 115, 116], \"zh-cn\": [114, 115, 116]}',1,'published',1,1,'2025-07-17 23:48:35'),(80,69,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'published',1,1,'2025-07-17 23:52:32'),(81,69,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-07-17 23:52:32'),(82,69,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'published',1,1,'2025-07-17 23:52:32'),(83,69,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:52:32'),(84,69,1,6,4,'{\"all\": [5, 7]}',1,'published',1,1,'2025-07-17 23:52:32'),(85,69,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:52:32'),(86,69,1,7,3,'{\"en-us\": [117, 118, 119], \"kz-kz\": [117, 118, 119], \"zh-cn\": [117, 118, 119]}',1,'published',1,1,'2025-07-17 23:52:32'),(87,77,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'published',1,1,'2025-07-17 23:53:28'),(88,77,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-07-17 23:53:28'),(89,77,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'published',1,1,'2025-07-17 23:53:28'),(90,77,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:53:28'),(91,77,1,6,4,'{\"all\": [5, 7]}',1,'published',1,1,'2025-07-17 23:53:28'),(92,77,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:53:28'),(93,77,1,7,3,'{\"en-us\": [120, 121, 122], \"kz-kz\": [120, 121, 122], \"zh-cn\": [120, 121, 122]}',1,'published',1,1,'2025-07-17 23:53:28'),(94,85,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',1,'published',1,1,'2025-07-17 23:55:02'),(95,85,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-07-17 23:55:02'),(96,85,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',1,'published',1,1,'2025-07-17 23:55:02'),(97,85,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:55:02'),(98,85,1,6,4,'{\"all\": [5, 7]}',1,'published',1,1,'2025-07-17 23:55:02'),(99,85,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-17 23:55:02'),(100,85,1,7,3,'{\"en-us\": [123, 124, 125], \"kz-kz\": [123, 124, 125], \"zh-cn\": [123, 124, 125]}',1,'published',1,1,'2025-07-17 23:55:02'),(101,61,1,19,1,'{\"en-us\": \"i want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',2,'draft',1,1,'2025-07-17 23:57:56'),(102,61,1,8,4,'{\"all\": [10]}',2,'draft',1,1,'2025-07-17 23:57:56'),(103,61,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',2,'draft',1,1,'2025-07-17 23:57:56'),(104,61,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',2,'draft',1,1,'2025-07-17 23:57:56'),(105,61,1,6,4,'{\"all\": [5, 7]}',2,'draft',1,1,'2025-07-17 23:57:56'),(106,61,1,6,5,'{\"all\": [\"fds\", \"fds\", \"fds\", \"fds\"]}',2,'draft',1,1,'2025-07-17 23:57:56'),(107,61,1,7,3,'{\"en-us\": [{}, {}, {}], \"kz-kz\": [{}, {}, {}], \"zh-cn\": [126, 127, 128]}',2,'draft',1,1,'2025-07-17 23:57:56'),(108,61,1,19,1,'{\"en-us\": \"id_61 的草稿  want have a test i want have a test i want have a test\", \"kz-kz\": \"testi want have a test i want have a test i want have a test\", \"zh-cn\": \"我要测试一下 i want have a test i want have a test\"}',3,'draft',1,1,'2025-07-17 23:58:24'),(109,61,1,8,4,'{\"all\": [10]}',3,'draft',1,1,'2025-07-17 23:58:24'),(110,61,1,5,2,'{\"en-us\": \"this is content\", \"kz-kz\": \"\", \"zh-cn\": \"这是内容\"}',3,'draft',1,1,'2025-07-17 23:58:24'),(111,61,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',3,'draft',1,1,'2025-07-17 23:58:24'),(112,61,1,6,4,'{\"all\": [5, 7]}',3,'draft',1,1,'2025-07-17 23:58:24'),(113,61,1,6,5,'{\"all\": [\"fds\", \"fds\", \"fds\", \"fds\"]}',3,'draft',1,1,'2025-07-17 23:58:24'),(114,61,1,7,3,'{\"en-us\": [{}, {}, {}], \"kz-kz\": [{}, {}, {}], \"zh-cn\": [129, 130, 131]}',3,'draft',1,1,'2025-07-17 23:58:24'),(115,93,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',1,1,'2025-07-19 05:06:06'),(116,93,1,8,4,'{\"all\": []}',1,'draft',1,1,'2025-07-19 05:06:06'),(117,93,1,5,2,'{\"en-us\": \"\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',1,1,'2025-07-19 05:06:06'),(118,93,1,6,4,'{\"all\": []}',1,'draft',1,1,'2025-07-19 05:06:06'),(119,94,1,19,1,'{\"en-us\": \"dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'draft',1,1,'2025-07-19 05:11:36'),(120,95,1,19,1,'{\"en-us\": \"测试一次 dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'published',1,1,'2025-07-19 05:12:54'),(121,95,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-07-19 05:12:54'),(122,95,1,5,2,'{\"en-us\": \"FDFSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'published',1,1,'2025-07-19 05:12:54'),(123,95,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-19 05:12:54'),(124,95,1,6,4,'{\"all\": [5]}',1,'published',1,1,'2025-07-19 05:12:54'),(125,95,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-07-19 05:12:54'),(126,95,1,7,3,'{\"en-us\": [132, 133, 134], \"kz-kz\": [], \"zh-cn\": []}',1,'published',1,1,'2025-07-19 05:12:54'),(127,95,1,19,1,'{\"en-us\": \"95测试一次 dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',2,'draft',1,1,'2025-07-19 05:14:23'),(128,95,1,8,4,'{\"all\": [10]}',2,'draft',1,1,'2025-07-19 05:14:23'),(129,95,1,5,2,'{\"en-us\": \"FDFSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',2,'draft',1,1,'2025-07-19 05:14:23'),(130,95,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',2,'draft',1,1,'2025-07-19 05:14:23'),(131,95,1,6,4,'{\"all\": [5]}',2,'draft',1,1,'2025-07-19 05:14:23'),(132,95,1,6,5,'{\"all\": [\"fds\", \"fds\", \"fds\", \"fds\"]}',2,'draft',1,1,'2025-07-19 05:14:23'),(133,95,1,7,3,'{\"en-us\": [135, 136, 137], \"kz-kz\": [], \"zh-cn\": []}',2,'draft',1,1,'2025-07-19 05:14:23'),(134,95,1,19,1,'{\"en-us\": \"[重新编辑一次]95测试一次 dddddddddddddddddddddddddddddddddddddddddddd\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',3,'deleted',1,1,'2025-07-19 05:21:23'),(135,95,1,8,4,'{\"all\": [10]}',3,'deleted',1,1,'2025-07-19 05:21:23'),(136,95,1,5,2,'{\"en-us\": \"FDFSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',3,'deleted',1,1,'2025-07-19 05:21:23'),(137,95,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',3,'deleted',1,1,'2025-07-19 05:21:23'),(138,95,1,6,4,'{\"all\": [5]}',3,'deleted',1,1,'2025-07-19 05:21:23'),(139,95,1,6,5,'{\"all\": [\"fds\", \"fds\", \"fds\", \"fds\"]}',3,'deleted',1,1,'2025-07-19 05:21:23'),(140,95,1,7,3,'{\"en-us\": [138, 139, 140], \"kz-kz\": [], \"zh-cn\": []}',3,'deleted',1,1,'2025-07-19 05:21:23'),(141,1,1,19,1,'{\"en-us\": \"地地地地地地地你地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',2,'published',1,1,'2025-07-30 21:14:20'),(142,1,1,8,4,'{\"all\": [13]}',2,'published',1,1,'2025-07-30 21:14:20'),(143,1,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',2,'published',1,1,'2025-07-30 21:14:20'),(144,1,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',2,'published',1,1,'2025-07-30 21:14:20'),(145,1,1,6,4,'{\"all\": [5, 7]}',2,'published',1,1,'2025-07-30 21:14:20'),(146,1,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',2,'published',1,1,'2025-07-30 21:14:20'),(147,1,1,7,3,'{\"en-us\": [141], \"kz-kz\": [], \"zh-cn\": []}',2,'published',1,1,'2025-07-30 21:14:20'),(148,10,1,20,1,'{\"en-us\": \"this is a title\", \"kz-kz\": \"请输入标题\", \"zh-cn\": \"请输入标题\"}',1,'published',1,1,'2025-08-01 15:54:29'),(149,10,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地好地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'published',1,1,'2025-08-01 15:54:29'),(150,10,1,8,4,'{\"all\": [10]}',1,'published',1,1,'2025-08-01 15:54:29'),(151,10,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'published',1,1,'2025-08-01 15:54:29'),(152,10,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-08-01 15:54:29'),(153,10,1,6,4,'{\"all\": [5, 7]}',1,'published',1,1,'2025-08-01 15:54:29'),(154,10,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'published',1,1,'2025-08-01 15:54:29'),(155,10,1,7,3,'{\"en-us\": [142], \"kz-kz\": [142], \"zh-cn\": [142]}',1,'published',1,1,'2025-08-01 15:54:29'),(156,118,3,21,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"\", \"zh-cn\": \"\"}',1,'published',1,1,'2025-08-05 05:44:10'),(157,118,3,9,4,'{\"all\": [5]}',1,'published',1,1,'2025-08-05 05:44:10');
/*!40000 ALTER TABLE `kazcms_field_data_v` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_data_v_old`
--

DROP TABLE IF EXISTS `kazcms_field_data_v_old`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_data_v_old` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `data_id` bigint NOT NULL COMMENT '内容 ID',
  `content_type_id` bigint NOT NULL COMMENT '内容类型 ID',
  `field_id` bigint NOT NULL COMMENT '字段 ID',
  `field_type_id` int NOT NULL COMMENT '字段类型 ID',
  `old_data` json NOT NULL COMMENT '旧的数据内容（更新前）',
  `changed_by` bigint DEFAULT NULL COMMENT '修改人 ID（user_id）',
  `changed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_changed_by` (`changed_by`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段数据的变更备份表，仅记录内容更新前的数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_data_v_old`
--

LOCK TABLES `kazcms_field_data_v_old` WRITE;
/*!40000 ALTER TABLE `kazcms_field_data_v_old` DISABLE KEYS */;
INSERT INTO `kazcms_field_data_v_old` VALUES (1,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-11 20:35:46'),(2,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-11 20:35:46'),(3,19,1,6,4,'{\"all\": [5, 7]}',1,'2025-07-11 20:35:46'),(4,19,1,8,4,'{\"all\": [10]}',1,'2025-07-11 20:35:46'),(5,19,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-11 20:35:46'),(6,19,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-11 20:35:46'),(7,19,1,7,3,'{\"en-us\": [44, 45], \"kz-kz\": [44, 45], \"zh-cn\": [44, 45]}',1,'2025-07-11 20:35:46'),(8,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 05:19:56'),(9,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 05:19:56'),(10,19,1,6,4,'{\"all\": [5, 7]}',1,'2025-07-12 05:19:56'),(11,19,1,8,4,'{\"all\": []}',1,'2025-07-12 05:19:56'),(12,19,1,7,3,'{\"en-us\": [49], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 05:19:56'),(13,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 05:39:36'),(14,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 05:39:36'),(15,19,1,6,4,'{\"all\": [5, 7]}',1,'2025-07-12 05:39:36'),(16,19,1,8,4,'{\"all\": [5, 7]}',1,'2025-07-12 05:39:36'),(17,19,1,7,3,'{\"en-us\": [50], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 05:39:36'),(18,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:01:04'),(19,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:01:04'),(20,19,1,6,4,'{\"all\": [5, 7]}',1,'2025-07-12 06:01:04'),(21,19,1,8,4,'{\"all\": [5, 7]}',1,'2025-07-12 06:01:04'),(22,19,1,5,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-12 06:01:04'),(23,19,1,6,5,'{\"all\": [\"fds\", \"fds\"]}',1,'2025-07-12 06:01:04'),(24,19,1,7,3,'{\"en-us\": [51], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:01:04'),(25,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:08:53'),(26,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:08:53'),(27,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:08:53'),(28,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:08:53'),(29,19,1,5,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:08:53'),(30,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:08:53'),(31,19,1,7,3,'{\"en-us\": [52, 53], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:08:53'),(32,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:09:33'),(33,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:09:33'),(34,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:09:33'),(35,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:09:33'),(36,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:09:33'),(37,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:09:33'),(38,19,1,7,3,'{\"en-us\": [54, 55], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:09:33'),(39,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:09:56'),(40,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:09:56'),(41,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:09:56'),(42,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:09:56'),(43,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:09:56'),(44,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:09:56'),(45,19,1,7,3,'{\"en-us\": [56, 57], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:09:56'),(46,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:10:21'),(47,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:10:21'),(48,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:10:21'),(49,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:10:21'),(50,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:10:21'),(51,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:10:21'),(52,19,1,7,3,'{\"en-us\": [58, 59], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:10:21'),(53,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:20:56'),(54,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:20:56'),(55,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:20:56'),(56,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:20:56'),(57,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:20:56'),(58,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:20:56'),(59,19,1,7,3,'{\"en-us\": [60, 61], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:20:56'),(60,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:23:56'),(61,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:23:56'),(62,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:23:56'),(63,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:23:56'),(64,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:23:56'),(65,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:23:56'),(66,19,1,7,3,'{\"en-us\": [62, 63], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:23:56'),(67,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:34:04'),(68,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:34:04'),(69,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:34:04'),(70,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:34:04'),(71,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:34:04'),(72,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:34:04'),(73,19,1,7,3,'{\"en-us\": [64, 65], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:34:04'),(74,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 06:34:49'),(75,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 06:34:49'),(76,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 06:34:49'),(77,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 06:34:49'),(78,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 06:34:49'),(79,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 06:34:49'),(80,19,1,7,3,'{\"en-us\": [66, 67], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 06:34:49'),(81,19,1,19,1,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地en\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地kz\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地cn\"}',1,'2025-07-12 20:26:01'),(82,19,1,5,2,'{\"en-us\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"kz-kz\": \"地地地地地地地地地地地地地地地地地地地地地地地\", \"zh-cn\": \"地地地地地地地地地地地地地地地地地地地地地地地\"}',1,'2025-07-12 20:26:01'),(83,19,1,6,4,'{\"all\": [5]}',1,'2025-07-12 20:26:01'),(84,19,1,8,4,'{\"all\": [10]}',1,'2025-07-12 20:26:01'),(85,19,1,5,5,'{\"all\": [\"fds\", \"fds\", \"fds\"]}',1,'2025-07-12 20:26:01'),(86,19,1,6,5,'{\"all\": [\"fds\"]}',1,'2025-07-12 20:26:01'),(87,19,1,7,3,'{\"en-us\": [68], \"kz-kz\": [], \"zh-cn\": []}',1,'2025-07-12 20:26:01');
/*!40000 ALTER TABLE `kazcms_field_data_v_old` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_file`
--

DROP TABLE IF EXISTS `kazcms_field_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_file` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `label_json` json NOT NULL,
  `help_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `allowed_file_types` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `max_upload_count` int unsigned DEFAULT NULL,
  `max_file_size_mb` int unsigned DEFAULT NULL,
  `enable_image_preview` tinyint(1) NOT NULL DEFAULT '0',
  `rename_on_upload` enum('original','uuid','timestamp') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'original',
  `display_as_gallery` tinyint(1) NOT NULL DEFAULT '0',
  `auto_compress_images` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_field_file_content_type` (`content_type_id`),
  CONSTRAINT `fk_field_file_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_file`
--

LOCK TABLES `kazcms_field_file` WRITE;
/*!40000 ALTER TABLE `kazcms_field_file` DISABLE KEYS */;
INSERT INTO `kazcms_field_file` VALUES (6,3,1,'{\"en-us\": \"file upload\", \"kz-kz\": \"resnam\", \"zh-cn\": \"文件上传\"}','{\"en-us\": \"please upload files\", \"kz-kz\": \"good\", \"zh-cn\": \"请上传照片\"}',1,'JPG,PNG',3,12,1,'original',1,1,'2025-06-30 02:43:22','2025-06-30 02:43:22'),(7,3,1,'{\"en-us\": \"just test\", \"kz-kz\": \"test\", \"zh-cn\": \"只是测试\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',1,'jpg,png,gif',21,12,1,'original',1,1,'2025-07-01 20:24:18','2025-07-11 19:10:40');
/*!40000 ALTER TABLE `kazcms_field_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_options`
--

DROP TABLE IF EXISTS `kazcms_field_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_options` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL COMMENT 'Foreign key to content type',
  `label_json` json NOT NULL COMMENT 'Field name (multi-language), e.g. {"en":"Color","zh":"颜色"}',
  `display_style` enum('select','radio','checkbox') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display style type',
  `options` json NOT NULL COMMENT 'Option list in JSON format',
  `max_select_count` int unsigned DEFAULT NULL COMMENT 'Max number of selections allowed (null = unlimited)',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this field required?',
  `prefix_text_json` json DEFAULT NULL COMMENT 'Prefix text for this field',
  `suffix_text_json` json DEFAULT NULL COMMENT 'Suffix text for this field',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_content_type_id` (`content_type_id`),
  CONSTRAINT `fk_field_options_content_type` FOREIGN KEY (`content_type_id`) REFERENCES `kazcms_content_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_options`
--

LOCK TABLES `kazcms_field_options` WRITE;
/*!40000 ALTER TABLE `kazcms_field_options` DISABLE KEYS */;
INSERT INTO `kazcms_field_options` VALUES (4,5,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','select','[{\"label\": {\"en-us\": \"ahah\", \"kz-kz\": \"hahakz\", \"zh-cn\": \"hahachinse\"}, \"value\": \"afd\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fdsfd\", \"kz-kz\": \"fds\", \"zh-cn\": \"fdsfds\"}, \"value\": \"32\", \"enabled\": \"1\", \"sort_order\": \"1\"}]',1,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','2025-07-01 20:46:55','2025-07-01 20:46:55'),(5,5,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','checkbox','[{\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"1\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"2\"}]',2,1,'{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','{\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}','2025-07-01 20:48:18','2025-07-11 19:11:16'),(6,5,1,'{\"en-us\": \"fdsfds\", \"kz-kz\": \"fdsfds\", \"zh-cn\": \"fdsfds\"}','checkbox','[{\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"0\"}, {\"label\": {\"en-us\": \"fdsfds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"1\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"2\"}, {\"label\": {\"en-us\": \"fds\", \"kz-kz\": \"fds\", \"zh-cn\": \"fds\"}, \"value\": \"fds\", \"enabled\": \"1\", \"sort_order\": \"3\"}]',2,0,'{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','2025-07-01 20:59:39','2025-07-11 19:11:26');
/*!40000 ALTER TABLE `kazcms_field_options` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_permission_rules`
--

DROP TABLE IF EXISTS `kazcms_field_permission_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_permission_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `content_type_id` int NOT NULL COMMENT '如 product',
  `field_type_id` int NOT NULL,
  `field_id` int NOT NULL COMMENT '如 price',
  `action` json NOT NULL,
  `allowed_group_ids` json NOT NULL COMMENT '允许的用户组ID数组，例如 [1,2,5]',
  `allowed_user_ids` json NOT NULL COMMENT '允许的用户ID数组，例如 [1,2,5]',
  `updated_at` timestamp NOT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段权限控制表，控制字段可见和编辑权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_permission_rules`
--

LOCK TABLES `kazcms_field_permission_rules` WRITE;
/*!40000 ALTER TABLE `kazcms_field_permission_rules` DISABLE KEYS */;
INSERT INTO `kazcms_field_permission_rules` VALUES (1,2,6,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:03:53','2025-07-11 19:03:53'),(2,1,1,19,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:37','2025-07-11 19:08:37'),(3,1,2,5,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:48','2025-07-11 19:08:48'),(4,1,3,7,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:08:53','2025-07-11 19:08:53'),(7,1,3,7,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:10:40','2025-07-11 19:10:40'),(8,1,4,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:10:47','2025-07-11 19:10:47'),(9,1,5,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:07','2025-07-11 19:11:07'),(10,1,5,5,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:16','2025-07-11 19:11:16'),(11,1,5,6,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:26','2025-07-11 19:11:26'),(12,1,6,4,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:11:32','2025-07-11 19:11:32'),(13,1,4,8,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-11 19:57:56','2025-07-11 19:57:56'),(14,1,1,19,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-31 03:05:30','2025-07-31 03:05:30'),(15,1,1,19,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-07-31 03:46:13','2025-07-31 03:46:13'),(16,1,1,20,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-08-01 15:53:10','2025-08-01 15:53:10'),(17,3,1,21,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-08-04 19:49:49','2025-08-04 19:49:49'),(18,3,4,9,'{\"data\": [\"create\", \"view\", \"update\", \"delete\"], \"schema\": [\"create\", \"view\", \"update\", \"delete\"]}','[]','[1]','2025-08-04 20:00:34','2025-08-04 20:00:34');
/*!40000 ALTER TABLE `kazcms_field_permission_rules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_subtype_labels`
--

DROP TABLE IF EXISTS `kazcms_field_subtype_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_subtype_labels` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `subtype_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `label_json` json NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subtype_code` (`subtype_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_subtype_labels`
--

LOCK TABLES `kazcms_field_subtype_labels` WRITE;
/*!40000 ALTER TABLE `kazcms_field_subtype_labels` DISABLE KEYS */;
INSERT INTO `kazcms_field_subtype_labels` VALUES (1,'text','{\"en-us\": \"Text\", \"kz-kz\": \"Мәтін\", \"zh-cn\": \"文本\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(2,'email','{\"en-us\": \"Email\", \"kz-kz\": \"Электрондық пошта\", \"zh-cn\": \"电子邮件\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(3,'url','{\"en-us\": \"URL\", \"kz-kz\": \"URL\", \"zh-cn\": \"网址\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(4,'tel','{\"en-us\": \"Telephone\", \"kz-kz\": \"Телефон\", \"zh-cn\": \"电话\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(5,'date','{\"en-us\": \"Date\", \"kz-kz\": \"Күні\", \"zh-cn\": \"日期\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(6,'datetime-local','{\"en-us\": \"Datetime\", \"kz-kz\": \"Күні мен уақыты\", \"zh-cn\": \"日期时间\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(7,'password','{\"en-us\": \"Password\", \"kz-kz\": \"Құпиясөз\", \"zh-cn\": \"密码\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(8,'number','{\"en-us\": \"Number\", \"kz-kz\": \"Сан\", \"zh-cn\": \"数字\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(9,'search','{\"en-us\": \"Search\", \"kz-kz\": \"Іздеу\", \"zh-cn\": \"搜索\"}','2025-06-23 00:07:14','2025-06-23 00:07:14'),(10,'color','{\"en-us\": \"Color\", \"kz-kz\": \"Түс\", \"zh-cn\": \"颜色\"}','2025-06-23 00:07:14','2025-06-23 00:07:14');
/*!40000 ALTER TABLE `kazcms_field_subtype_labels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_text`
--

DROP TABLE IF EXISTS `kazcms_field_text`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_text` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `content_type_id` int unsigned NOT NULL,
  `label_json` json NOT NULL,
  `is_seo` tinyint(1) NOT NULL DEFAULT '0',
  `seo_type` enum('title','meta_description','keywords','slug') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subtype` enum('text','email','url','tel','date','datetime-local','password','number','search','color') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text',
  `placeholder_json` json DEFAULT NULL,
  `default_value_json` json DEFAULT NULL,
  `prefix_text_json` json DEFAULT NULL,
  `suffix_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `min_length` int unsigned DEFAULT '0',
  `max_length` int unsigned DEFAULT '0',
  `display_color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#000000',
  `help_text_json` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_text`
--

LOCK TABLES `kazcms_field_text` WRITE;
/*!40000 ALTER TABLE `kazcms_field_text` DISABLE KEYS */;
INSERT INTO `kazcms_field_text` VALUES (19,1,1,'{\"en-us\": \"title11\", \"kz-kz\": \"bas33\", \"zh-cn\": \"标题22\"}',1,'slug','text','{\"en-us\": \"please write title\", \"kz-kz\": \"bas jaz\", \"zh-cn\": \"请输入标题\"}','{\"en-us\": \"1\", \"kz-kz\": \"3\", \"zh-cn\": \"2\"}','{\"en-us\": \"pen\", \"kz-kz\": \"pkz\", \"zh-cn\": \"pcn\"}','{\"en-us\": \"sen\", \"kz-kz\": \"skz\", \"zh-cn\": \"scn\"}',1,20,130,'#986a44','{\"en-us\": \"this is title of website\", \"kz-kz\": \"website en bas\", \"zh-cn\": \"这是网站的标题\"}','2025-06-29 19:10:35','2025-07-31 03:46:13'),(20,1,1,'{\"en-us\": \"title\", \"kz-kz\": \"bas\", \"zh-cn\": \"标题\"}',0,NULL,'text','{\"en-us\": \"请输入标题\", \"kz-kz\": \"bas kergez\", \"zh-cn\": \"Please input title\"}','{\"en-us\": \"hello\", \"kz-kz\": \"jahse\", \"zh-cn\": \"你好\"}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,NULL,NULL,'#3b82f6','{\"en-us\": \"please input title\", \"kz-kz\": \"input\", \"zh-cn\": \"请输入标题\"}','2025-08-01 15:53:10','2025-08-01 15:53:10'),(21,1,3,'{\"en-us\": \"title\", \"kz-kz\": \"title\", \"zh-cn\": \"biaoti\"}',0,NULL,'text','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}',0,NULL,NULL,'#3b82f6','{\"en-us\": null, \"kz-kz\": null, \"zh-cn\": null}','2025-08-04 19:49:49','2025-08-04 19:49:49');
/*!40000 ALTER TABLE `kazcms_field_text` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_textarea`
--

DROP TABLE IF EXISTS `kazcms_field_textarea`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_textarea` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `field_type_id` int unsigned DEFAULT NULL,
  `label_json` json NOT NULL,
  `help_text_json` json DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `content_type_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_textarea`
--

LOCK TABLES `kazcms_field_textarea` WRITE;
/*!40000 ALTER TABLE `kazcms_field_textarea` DISABLE KEYS */;
INSERT INTO `kazcms_field_textarea` VALUES (5,2,'{\"en-us\": \"content\", \"kz-kz\": \"contentkz\", \"zh-cn\": \"内容\"}','{\"en-us\": \"please write content\", \"kz-kz\": \"content jaz\", \"zh-cn\": \"请填充内容\"}',1,1,'2025-06-29 21:41:24','2025-07-11 19:08:48');
/*!40000 ALTER TABLE `kazcms_field_textarea` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_field_types`
--

DROP TABLE IF EXISTS `kazcms_field_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_field_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_table` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type_name_json` json NOT NULL,
  `description_json` json DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `icon` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_field_types`
--

LOCK TABLES `kazcms_field_types` WRITE;
/*!40000 ALTER TABLE `kazcms_field_types` DISABLE KEYS */;
INSERT INTO `kazcms_field_types` VALUES (1,'text','text','{\"en-us\": \"Single-line Text\", \"kz-kz\": \"Бір жолдық мәтін\", \"zh-cn\": \"单行文本\"}','{\"en-us\": \"Single line input field\", \"kz-kz\": \"Бір жолдық енгізу өрісі\", \"zh-cn\": \"单行输入框\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:03',''),(2,'textarea','textarea','{\"en-us\": \"Multi-line Text\", \"kz-kz\": \"Көп жолды мәтін\", \"zh-cn\": \"多行文本\"}','{\"en-us\": \"Multi line textarea\", \"kz-kz\": \"Көп жолды мәтін өрісі\", \"zh-cn\": \"多行文本框\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:06',''),(3,'file','file','{\"en-us\": \"File Upload\", \"kz-kz\": \"Файл жүктеу\", \"zh-cn\": \"文件上传\"}','{\"en-us\": \"Upload file field\", \"kz-kz\": \"Файл жүктеу өрісі\", \"zh-cn\": \"上传文件字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:19',''),(4,'category','categories','{\"en-us\": \"Category\", \"kz-kz\": \"Санат\", \"zh-cn\": \"分类\"}','{\"en-us\": \"Category selection field\", \"kz-kz\": \"Санат таңдау өрісі\", \"zh-cn\": \"分类选择字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:23',''),(5,'options','options','{\"en-us\": \"Options\", \"kz-kz\": \"Опциялар\", \"zh-cn\": \"选项\"}','{\"en-us\": \"Selectable options field\", \"kz-kz\": \"Таңдауға болатын опциялар өрісі\", \"zh-cn\": \"可选择的选项字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:28',''),(6,'code','codes','{\"en-us\": \"Code\", \"kz-kz\": \"Код\", \"zh-cn\": \"代码\"}','{\"en-us\": \"Code snippet field\", \"kz-kz\": \"Код үзіндісі өрісі\", \"zh-cn\": \"代码片段字段\"}',1,'2025-06-23 00:00:37','2025-06-23 15:07:34','');
/*!40000 ALTER TABLE `kazcms_field_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_fields_order`
--

DROP TABLE IF EXISTS `kazcms_fields_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_fields_order` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `content_type_id` int unsigned NOT NULL COMMENT 'Content type this field order belongs to',
  `field_id` int unsigned NOT NULL COMMENT 'Field ID',
  `field_type_id` int unsigned NOT NULL COMMENT 'Field type ID',
  `sort_order` int unsigned NOT NULL COMMENT 'Order number for sorting fields',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_content_field` (`content_type_id`,`field_id`,`field_type_id`) USING BTREE,
  KEY `idx_content_type` (`content_type_id`),
  KEY `idx_field_type` (`field_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_fields_order`
--

LOCK TABLES `kazcms_fields_order` WRITE;
/*!40000 ALTER TABLE `kazcms_fields_order` DISABLE KEYS */;
INSERT INTO `kazcms_fields_order` VALUES (34,1,19,1,10,'2025-06-29 19:10:35','2025-06-29 19:10:35'),(35,1,5,2,45,'2025-06-29 21:41:24','2025-07-14 22:43:55'),(36,1,6,3,25,'2025-06-30 02:43:22','2025-07-01 20:24:34'),(37,1,6,4,50,'2025-06-30 22:26:57','2025-07-14 22:43:55'),(38,1,7,4,21,'2025-07-01 00:19:00','2025-07-01 20:24:34'),(39,1,8,4,35,'2025-07-01 00:19:37','2025-07-01 00:19:37'),(40,1,7,3,21,'2025-07-01 20:24:18','2025-07-01 20:24:34'),(41,1,4,5,40,'2025-07-01 20:46:55','2025-07-01 20:46:55'),(42,1,5,5,45,'2025-07-01 20:48:18','2025-07-01 20:48:18'),(43,1,6,5,50,'2025-07-01 20:59:39','2025-07-01 20:59:39'),(44,1,4,6,55,'2025-07-01 21:00:45','2025-07-01 21:00:45'),(45,2,6,6,5,'2025-07-11 19:03:53','2025-07-11 19:03:53'),(46,1,20,1,5,'2025-08-01 15:53:10','2025-08-01 15:53:17'),(47,3,21,1,5,'2025-08-04 19:49:49','2025-08-04 19:49:49'),(48,3,9,4,10,'2025-08-04 20:00:34','2025-08-04 20:00:34');
/*!40000 ALTER TABLE `kazcms_fields_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_languages`
--

DROP TABLE IF EXISTS `kazcms_languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_languages` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language code (e.g., en, fr, zh)',
  `name` json NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is the language enabled',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'For ordering in UI',
  `is_default` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Available languages';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_languages`
--

LOCK TABLES `kazcms_languages` WRITE;
/*!40000 ALTER TABLE `kazcms_languages` DISABLE KEYS */;
INSERT INTO `kazcms_languages` VALUES (1,'en-us','{\"en-us\": \"English\", \"kz-kz\": \"ағылшын\", \"zh-cn\": \"英语\"}',1,1,1),(2,'zh-cn','{\"en-us\": \"Chinese\", \"kz-kz\": \"қытай\", \"zh-cn\": \"中文\"}',1,2,0),(4,'kz-kz','{\"en-us\": \"Kazakh\", \"kz-kz\": \"қазақ\", \"zh-cn\": \"哈萨克语\"}',1,4,0);
/*!40000 ALTER TABLE `kazcms_languages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_menus`
--

DROP TABLE IF EXISTS `kazcms_menus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_menus` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` json NOT NULL,
  `key_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'admin',
  `position` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'header',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_index` int DEFAULT '0',
  `parent_id` int unsigned DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_name` (`key_name`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `kazcms_menus_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_menus` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_menus`
--

LOCK TABLES `kazcms_menus` WRITE;
/*!40000 ALTER TABLE `kazcms_menus` DISABLE KEYS */;
INSERT INTO `kazcms_menus` VALUES (1,'{\"en-us\": \"Admin Dashboard\", \"kz-kz\": \"Басқару тақтасы\", \"zh-cn\": \"管理面板\"}','admin_dashboard','admin','admin.left','/admin','fa-dashboard',1,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(2,'{\"en-us\": \"User Groups\", \"kz-kz\": \"Пайдаланушы топтары\", \"zh-cn\": \"用户组\"}','admin_user_groups','admin','admin.left','/admin/user-groups','fa-users',2,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(3,'{\"en-us\": \"Permission Assign\", \"kz-kz\": \"Рұқсаттарды тағайындау\", \"zh-cn\": \"权限分配\"}','admin_permissions','admin','admin.left','/admin/permissions/assign/user/1','fa-lock',3,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(4,'{\"en-us\": \"Translation Check\", \"kz-kz\": \"Аударма тексеру\", \"zh-cn\": \"翻译检查\"}','admin_multilang_check','admin','admin.left','/admin/multilang/checkers','fa-language',4,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(5,'{\"en-us\": \"Content Types\", \"kz-kz\": \"Контент түрлері\", \"zh-cn\": \"内容类型\"}','admin_content_types','admin','admin.left','/admin/content_types','fa-database',5,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(6,'{\"en-us\": \"Categories\", \"kz-kz\": \"Санаттар\", \"zh-cn\": \"分类\"}','admin_categories','admin','admin.left','/admin/categories','fa-folder',6,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24'),(7,'{\"en-us\": \"Fields\", \"kz-kz\": \"Өрістер\", \"zh-cn\": \"字段\"}','admin_fields','admin','admin.left','/admin/fields/list/1','fa-list',7,NULL,1,'2025-06-27 02:23:24','2025-06-27 02:23:24');
/*!40000 ALTER TABLE `kazcms_menus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_migrations`
--

DROP TABLE IF EXISTS `kazcms_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_migrations`
--

LOCK TABLES `kazcms_migrations` WRITE;
/*!40000 ALTER TABLE `kazcms_migrations` DISABLE KEYS */;
INSERT INTO `kazcms_migrations` VALUES (1,'2014_10_12_000000_create_users_table',1),(2,'2014_10_12_100000_create_password_reset_tokens_table',1),(3,'2019_08_19_000000_create_failed_jobs_table',1),(4,'2019_12_14_000001_create_personal_access_tokens_table',1);
/*!40000 ALTER TABLE `kazcms_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_multilang_checker`
--

DROP TABLE IF EXISTS `kazcms_multilang_checker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_multilang_checker` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL,
  `json_column_name` varchar(100) NOT NULL,
  `column_description` json DEFAULT NULL COMMENT '字段解释说明，多语言 JSON 格式',
  `table_id` varchar(30) NOT NULL,
  `module` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_multilang_checker`
--

LOCK TABLES `kazcms_multilang_checker` WRITE;
/*!40000 ALTER TABLE `kazcms_multilang_checker` DISABLE KEYS */;
INSERT INTO `kazcms_multilang_checker` VALUES (1,'elements','name','{\"en-us\": \"name\", \"zh-cn\": \"元素名\"}','id','{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}','2025-06-20 02:50:33','2025-06-20 02:50:33'),(2,'elements','slug','{\"en-us\": \"slug\", \"zh-cn\": \"网址别名\"}','id','{\"en-us\": \"elements\", \"zh-cn\": \"元素\"}','2025-06-20 02:50:33','2025-06-20 02:50:33'),(3,'user_groups','name','{\"en-us\": \"user group name\", \"zh-cn\": \"用户组名\"}','id','{\"en-us\": \"user group\", \"zh-cn\": \"用户组\"}','2025-06-20 03:20:33','2025-06-20 03:20:33');
/*!40000 ALTER TABLE `kazcms_multilang_checker` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_option_display_styles`
--

DROP TABLE IF EXISTS `kazcms_option_display_styles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_option_display_styles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` json NOT NULL,
  `description` json DEFAULT NULL,
  `icon_class` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `enabled` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_option_display_styles`
--

LOCK TABLES `kazcms_option_display_styles` WRITE;
/*!40000 ALTER TABLE `kazcms_option_display_styles` DISABLE KEYS */;
INSERT INTO `kazcms_option_display_styles` VALUES (1,'select','{\"en-us\": \"Dropdown\", \"kz-kz\": \"Ашылмалы тізім\", \"zh-cn\": \"下拉菜单\"}','{\"en-us\": \"A single choice from dropdown.\", \"kz-kz\": \"Ашылмалы тізімнен біреуін таңдаңыз.\", \"zh-cn\": \"从下拉列表中选择一个选项。\"}','fa fa-caret-down',1,1,'2025-06-23 19:38:11','2025-06-23 19:38:11'),(2,'radio','{\"en-us\": \"Radio Buttons\", \"kz-kz\": \"Радио батырмалар\", \"zh-cn\": \"单选按钮\"}','{\"en-us\": \"A group of circular options where only one can be selected.\", \"kz-kz\": \"Тек біреуін таңдауға болатын дөңгелек опциялар.\", \"zh-cn\": \"只能选择一个的圆形选项组。\"}','fa fa-dot-circle',2,1,'2025-06-23 19:38:11','2025-06-23 19:38:11'),(3,'checkbox','{\"en-us\": \"Checkboxes\", \"kz-kz\": \"Белгі қою ұяшықтары\", \"zh-cn\": \"多选框\"}','{\"en-us\": \"Allows multiple selections from listed options.\", \"kz-kz\": \"Көп опцияны таңдауға мүмкіндік береді.\", \"zh-cn\": \"可以多选的选项列表。\"}','fa fa-check-square',3,1,'2025-06-23 19:38:11','2025-06-23 19:38:11');
/*!40000 ALTER TABLE `kazcms_option_display_styles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_password_reset_tokens`
--

DROP TABLE IF EXISTS `kazcms_password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_password_reset_tokens`
--

LOCK TABLES `kazcms_password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `kazcms_password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_permission_assignments`
--

DROP TABLE IF EXISTS `kazcms_permission_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_permission_assignments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `permission_id` int unsigned NOT NULL,
  `assignee_type` enum('user','group') COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignee_id` int unsigned NOT NULL,
  `created_by` bigint unsigned NOT NULL COMMENT '添加数据的用户ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_assignee` (`assignee_type`,`assignee_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `kazcms_permission_assignments_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_permission_assignments`
--

LOCK TABLES `kazcms_permission_assignments` WRITE;
/*!40000 ALTER TABLE `kazcms_permission_assignments` DISABLE KEYS */;
INSERT INTO `kazcms_permission_assignments` VALUES (3,3,'group',1,1,'2025-06-20 02:06:09','2025-06-20 02:06:09'),(4,12,'group',1,1,'2025-06-20 02:06:09','2025-06-20 02:06:09'),(9,5,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(10,6,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(11,10,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(12,11,'group',4,1,'2025-06-20 02:06:24','2025-06-20 02:06:24'),(13,4,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(14,5,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(15,6,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(16,10,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(17,11,'group',3,1,'2025-06-20 02:08:56','2025-06-20 02:08:56'),(20,11,'group',6,1,'2025-06-20 02:10:22','2025-06-20 02:10:22'),(21,12,'group',6,1,'2025-06-20 02:10:22','2025-06-20 02:10:22');
/*!40000 ALTER TABLE `kazcms_permission_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_permissions`
--

DROP TABLE IF EXISTS `kazcms_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int unsigned DEFAULT NULL COMMENT 'Parent permission ID, NULL if top-level',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Internal name like create_post, edit_product',
  `label` json NOT NULL COMMENT 'Display name in multiple languages',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional module or feature name',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `fk_permissions_parent` FOREIGN KEY (`parent_id`) REFERENCES `kazcms_permissions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_permissions`
--

LOCK TABLES `kazcms_permissions` WRITE;
/*!40000 ALTER TABLE `kazcms_permissions` DISABLE KEYS */;
INSERT INTO `kazcms_permissions` VALUES (1,NULL,'manage_elements','{\"en\": \"Manage Elements\", \"kz\": \"Элементтерді басқару\", \"zh\": \"元素管理\"}','element'),(3,1,'show_list','{\"en\": \"Show List\", \"kz\": \"Тізімді көрсету\", \"zh\": \"查看列表\"}','element'),(4,1,'add_update_elements','{\"en\": \"Add/Update Elements\", \"kz\": \"Элементтерді қосу/жаңарту\", \"zh\": \"添加/更新元素\"}','element'),(5,1,'delete_element','{\"en\": \"Delete Element\", \"kz\": \"Элементті жою\", \"zh\": \"删除元素\"}','element'),(6,1,'show_data_list','{\"en\": \"Show Data List\", \"kz\": \"Деректер тізімін көру\", \"zh\": \"查看数据列表\"}','element'),(7,1,'export_data','{\"en\": \"Export Data\", \"kz\": \"Деректерді экспорттау\", \"zh\": \"导出数据\"}','element'),(8,1,'add_edit_templates','{\"en\": \"Add/Edit Templates\", \"kz\": \"Үлгілерді қосу/өңдеу\", \"zh\": \"添加/编辑模板\"}','element'),(9,1,'manage_properties','{\"en\": \"Manage Properties\", \"kz\": \"Қасиеттерді басқару\", \"zh\": \"管理属性\"}','element'),(10,9,'add_update_properties','{\"en\": \"Add/Update Properties\", \"kz\": \"Қасиеттерді қосу/жаңарту\", \"zh\": \"添加/更新属性\"}','element'),(11,9,'delete_properties','{\"en\": \"Delete Properties\", \"kz\": \"Қасиеттерді жою\", \"zh\": \"删除属性\"}','element'),(12,9,'field_permission','{\"en\": \"Field Permission\", \"kz\": \"Өріс рұқсаттары\", \"zh\": \"字段权限\"}','element'),(13,9,'other_manage','{\"en\": \"Other Manage\", \"kz\": \"Басқа басқару\", \"zh\": \"其他管理\"}','element');
/*!40000 ALTER TABLE `kazcms_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_personal_access_tokens`
--

DROP TABLE IF EXISTS `kazcms_personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_personal_access_tokens`
--

LOCK TABLES `kazcms_personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `kazcms_personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_site_settings`
--

DROP TABLE IF EXISTS `kazcms_site_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_site_settings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `site_name` json NOT NULL COMMENT 'Website name (multi-language)',
  `site_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Full website URL, e.g., https://example.com',
  `meta_title` json NOT NULL COMMENT 'SEO meta title (multi-language)',
  `meta_keywords` json NOT NULL COMMENT 'SEO keywords, comma-separated (multi-language support)',
  `meta_description` json NOT NULL COMMENT 'SEO meta description (multi-language)',
  `default_language_id` int unsigned NOT NULL COMMENT 'Default language ID, linked to languages table',
  `maintenance_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is maintenance mode enabled',
  `maintenance_message` json DEFAULT NULL COMMENT 'Maintenance page message (multi-language)',
  `favicon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Favicon path or URL (32x32 recommended)',
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Logo path or URL (SVG or transparent PNG recommended)',
  `logo_alt_text` json DEFAULT NULL COMMENT 'Alt text for logo (multi-language)',
  `copyright` json DEFAULT NULL COMMENT 'Footer copyright text (multi-language)',
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact email address',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Contact phone number',
  `timezone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'UTC' COMMENT 'Website timezone, e.g. UTC, America/Montreal',
  `default_currency` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default currency code, e.g. USD, CAD',
  `open_graph_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Open Graph image URL (recommended 1200x630)',
  `robots_txt` text COLLATE utf8mb4_unicode_ci COMMENT 'Custom robots.txt content',
  `sitemap_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'URL to sitemap file',
  `social_links` json DEFAULT NULL COMMENT 'Social media links JSON, e.g. {"facebook": "...", "x": "..."}',
  `analytics_code` text COLLATE utf8mb4_unicode_ci COMMENT 'Analytics or other custom code snippets',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_multilang` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_default_language_id` (`default_language_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_site_settings`
--

LOCK TABLES `kazcms_site_settings` WRITE;
/*!40000 ALTER TABLE `kazcms_site_settings` DISABLE KEYS */;
INSERT INTO `kazcms_site_settings` VALUES (1,'{\"en-us\": \"KAZCMS Demo\", \"kz-kz\": \"KAZCMS Демо\", \"zh-cn\": \"KAZCMS演示站\"}','https://kazcms.com','{\"en-us\": \"Best CMS for Kazakhstan\", \"kz-kz\": \"Қазақстандағы үздік CMS\", \"zh-cn\": \"最好的内容管理系统\"}','{\"en-us\": \"cms,kazakhstan,kazcms\", \"kz-kz\": \"cms,қазақстан,kazcms\", \"zh-cn\": \"cms,哈萨克斯坦,kazcms\"}','{\"en-us\": \"KAZCMS is a multilingual content management system.\", \"kz-kz\": \"KAZCMS — көптілді CMS жүйесі.\", \"zh-cn\": \"KAZCMS 是一个多语言内容管理系统。\"}',1,0,'{\"en-us\": \"Site is under maintenance.\", \"kz-kz\": \"Сайт техникалық қызмет көрсетуде.\", \"zh-cn\": \"网站维护中。\"}','/assets/img/favicon.png','/assets/img/logo.svg','{\"en-us\": \"KAZCMS Logo\", \"kz-kz\": \"KAZCMS логотипі\", \"zh-cn\": \"KAZCMS 标志\"}','{\"en-us\": \"© 2025 KAZCMS\", \"kz-kz\": \"© 2025 KAZCMS\", \"zh-cn\": \"© 2025 KAZCMS\"}','<EMAIL>','+1-800-123-4567','America/Montreal','CAD','/assets/img/og.jpg','User-agent: *\nDisallow:','https://kazcms.com/sitemap.xml','{\"x\": \"https://x.com/kazcms\", \"facebook\": \"https://facebook.com/kazcms\"}','<script>console.log(\"Analytics loaded\")</script>','2025-06-23 14:22:30','2025-06-23 14:22:30',1);
/*!40000 ALTER TABLE `kazcms_site_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_user_groups`
--

DROP TABLE IF EXISTS `kazcms_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_user_groups` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `name` json NOT NULL COMMENT 'Group name in multiple languages, e.g. { "en": "Editor", "fr": "Éditeur" }',
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Direct parent group ID',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Pipe-separated ancestor group IDs, e.g. |1|3|5|',
  `level` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Depth level in the hierarchy (root is 0)',
  `is_leaf` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this is a leaf group (no children)',
  `sort_order` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Optional custom ordering within siblings',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hierarchical user groups with multi-language support';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_user_groups`
--

LOCK TABLES `kazcms_user_groups` WRITE;
/*!40000 ALTER TABLE `kazcms_user_groups` DISABLE KEYS */;
INSERT INTO `kazcms_user_groups` VALUES (1,'{\"en\": \"Administrator\", \"kz\": \"Әкімші\", \"zh\": \"管理员\"}',0,'|',0,0,1,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(2,'{\"en\": \"Editor\", \"kz\": \"Редактор\", \"zh\": \"编辑\"}',1,'|1|',1,1,2,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(3,'{\"en\": \"Author\", \"kz\": \"Автор\", \"zh\": \"作者\"}',1,'|1|',1,1,3,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(4,'{\"en\": \"Moderator\", \"kz\": \"Модератор\", \"zh\": \"审核员\"}',1,'|1|',1,1,4,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(5,'{\"en\": \"Guest\", \"kz\": \"Қонақ\", \"zh\": \"访客\"}',0,'|',0,1,5,'2025-06-19 02:02:57','2025-06-19 02:02:57'),(6,'{\"en\": \"Registered\", \"kz\": \"Тіркелген қолданушы\", \"zh\": \"注册用户\"}',0,'|',0,1,6,'2025-06-19 02:03:04','2025-06-19 02:03:04'),(7,'{\"en\": \"VIP\", \"kz\": \"VIP қолданушы\", \"zh\": \"VIP用户\"}',0,'|',0,1,7,'2025-06-19 02:03:04','2025-06-19 02:03:04');
/*!40000 ALTER TABLE `kazcms_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_user_user_group`
--

DROP TABLE IF EXISTS `kazcms_user_user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_user_user_group` (
  `user_id` int unsigned NOT NULL,
  `group_id` int unsigned NOT NULL,
  PRIMARY KEY (`user_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_user_user_group`
--

LOCK TABLES `kazcms_user_user_group` WRITE;
/*!40000 ALTER TABLE `kazcms_user_user_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `kazcms_user_user_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kazcms_users`
--

DROP TABLE IF EXISTS `kazcms_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kazcms_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_group_id` int NOT NULL DEFAULT '6',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kazcms_users`
--

LOCK TABLES `kazcms_users` WRITE;
/*!40000 ALTER TABLE `kazcms_users` DISABLE KEYS */;
INSERT INTO `kazcms_users` VALUES (1,'kz','<EMAIL>',NULL,'$2y$12$AnbhjL4U2XpuBStWDbV4uuTj7E595K1Mo.QzDivfKdKo66/XsCv36','218w4Rb8tqmQFpEcTG2HDfPArX5oxoyexSY697HYs5GW0MLU4bVJL3bqvd2G','2025-06-14 22:17:10','2025-06-14 22:17:10',6);
/*!40000 ALTER TABLE `kazcms_users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-06 12:16:38
