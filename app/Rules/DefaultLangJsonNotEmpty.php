<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Lang;

class DefaultLangJsonNotEmpty implements ValidationRule
{
    protected string $defaultLang;

    public function __construct(string $defaultLang = null)
    {
        $this->defaultLang = $defaultLang ?? config('app.fallback_locale', 'en-us');
        
    }

    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function validate(string $attribute, mixed $data, Closure $fail): void
    {
        //$data = json_decode($value, true);

        if (!is_array($data)) {
            $fail("The $attribute must be a valid JSON.");
            return;
        }

        if (empty($data[$this->defaultLang]) || !is_string($data[$this->defaultLang]) || trim($data[$this->defaultLang]) === '') {
            $fail(Lang::get('validation.default_lang_json_not_empty', [
                'attribute' => $attribute,
                'lang' => $this->defaultLang,
            ]));
        }
    }
}
