<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SiteSetting extends Model
{
    protected $table = 'site_settings';

    protected $fillable = [
        'site_name',
        'site_url',
        'meta_title',
        'meta_keywords',
        'meta_description',
        'default_language_id',
        'maintenance_mode',
        'maintenance_message',
        'favicon',
        'logo',
        'logo_alt_text',
        'copyright',
        'contact_email',
        'contact_phone',
        'timezone',
        'default_currency',
        'open_graph_image',
        'robots_txt',
        'sitemap_url',
        'social_links',
        'analytics_code',
    ];

    protected $casts = [
        'site_name' => 'array',
        'meta_title' => 'array',
        'meta_keywords' => 'array',
        'meta_description' => 'array',
        'maintenance_message' => 'array',
        'logo_alt_text' => 'array',
        'copyright' => 'array',
        'social_links' => 'array',
        'maintenance_mode' => 'boolean',
    ];

    /**
     * 关联默认语言 (假设有 Language 模型和 languages 表)
     */
    public function defaultLanguage()
    {
        return $this->belongsTo(Language::class, 'default_language_id');
    }
}
