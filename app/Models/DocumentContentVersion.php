<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocumentContentVersion extends Model
{
    protected $table = 'document_content_versions';

    protected $fillable = [
        'content_type_id',
        'data_id',
        'version',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'version' => 'decimal:2',
    ];

    public $timestamps = true;

    // 可选：添加创建者关联（假设你有 User 模型）
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }


    public function store(Request $request, $content_type_id, $data_id)
{
    $validated = $request->validate([
        'version' => 'required|numeric|min:0',
        'is_active' => 'required|numeric|in:0,1',
    ]);

    // 检查是否已存在该版本号
    $exists = DocumentContentVersion::where('content_type_id', $content_type_id)
        ->where('data_id', $data_id)
        ->where('version', $validated['version'])
        ->exists();

    if ($exists) {
        return response()->json([
            'success' => false,
            'message' => 'This version number already exists.'
        ], 409);
    }

    $version = DocumentContentVersion::create([
        'content_type_id' => $content_type_id,
        'data_id' => $data_id,
        'version' => $validated['version'],
        'is_active' => $validated['is_active'],
        'created_by' => auth()->id(),
    ]);

    return response()->json([
        'success' => true,
        'version' => $version,
    ]);
}
 /**
     * Get the latest active version number for a given content type and data ID
     *
     * @param  int  $contentTypeId
     * @param  int  $dataId
     * @return int|null
     */
    public static function getLastActiveVersionNumber($contentTypeId, $dataId)
    {
        
        return self::where('content_type_id', $contentTypeId)
            ->where('data_id', $dataId)
            ->where('is_active', 1)
            ->orderByDesc('version')
            ->first();
    }

    public function scopeActiveVersions($query, $contentTypeId, $dataId)
    {
        return $query->select('id', 'version', 'is_active')
            ->where('content_type_id', $contentTypeId)
            ->where('data_id', $dataId)
            ->where('is_active', 1)
            ->orderByDesc('version');
    }
}
