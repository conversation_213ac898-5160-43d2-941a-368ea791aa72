<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Page extends Model
{
    // 表名
    protected $table = 'pages';

    // 主键类型
    protected $keyType = 'int';
    public $incrementing = true;

    // 批量赋值字段
    protected $fillable = [
        'slug',
        'slug_all_languages',

        'title',
        'title_all_languages',

        'content',
        'content_all_languages',

        'meta',
        'meta_all_languages',

        'content_type_id',
        'content_type',
        'enable',
    ];

    // JSON 字段自动 cast
    protected $casts = [
        'slug' => 'array',
        'title' => 'array',
        'content' => 'array',
        'meta' => 'array',
        'title_all_languages' => 'boolean',
        'content_all_languages' => 'boolean',
        'meta_all_languages' => 'boolean',
        'slug_all_languages' => 'boolean',
        'content_type_id' => 'integer',
        'content_type' => 'string',
        'enable' => 'boolean',
    ];

    // 如果你想自动维护时间戳
    public $timestamps = true;

    /**
     * Get list of pages filtered by optional parameters.
     *
     * @param int|null $contentTypeId Optional content type ID
     * @param string|null $contentType Optional content type ('static', 'list', 'detail')
     * @param bool|null $enabled Optional enable status (true=enabled, false=disabled, null=all)
     * @param int|null $limit Optional number of rows to fetch
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getPages(
        ?int $contentTypeId = null,
        ?string $contentType = null,
        ?bool $enabled = true,
        ?int $limit = null,
        ?bool $isMain = true
    ) {
        $query = self::query();

        // Filter by content_type_id if provided
        if (!is_null($contentTypeId)) {
            $query->where('content_type_id', $contentTypeId);
        }

        // Filter by content_type if provided
        if (!is_null($contentType)) {
            $query->where('content_type', $contentType);
        }

        // Filter by enable status if provided
        if (!is_null($enabled)) {
            $query->where('enable', $enabled ? 1 : 0);
        }

        // Limit the number of results if provided
        if (!is_null($limit)) {
            $query->limit($limit);
        }

        if (!is_null($isMain)) {
            $query->where('is_main', $isMain ? 1 : 0);
        }

        return $query->get();
    }

}
