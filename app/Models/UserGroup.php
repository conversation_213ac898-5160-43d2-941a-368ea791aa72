<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserGroup extends Model
{
    // 表名
    protected $table = 'user_groups';

    // 主键类型为整数，自增默认即可
    protected $primaryKey = 'id';

    // 允许批量赋值字段
    protected $fillable = [
        'name',        // 多语言 JSON
        'parent_id',   // 父组ID
        'path',        // 父路径
        'level',       // 层级
        'sort_order',  // 排序
        'is_leaf',     // 是否叶子节点
    ];

    // name是json格式，需要自动转成数组
    protected $casts = [
        'name' => 'array',
    ];

    /**
     * 父组关联
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(UserGroup::class, 'parent_id');
    }

    /**
     * 子组关联
     */
    public function children(): HasMany
    {
        return $this->hasMany(UserGroup::class, 'parent_id');
    }

    /**
     * 关联用户
     * （假设用户表叫 users，且有 user_group_id 字段）
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'user_group_id');
    }

    /**
     * 获取指定语言的名称，默认 fallback 到 en
     */
    public function getName(string $lang = 'en'): string
    {
        if (is_array($this->name) && isset($this->name[$lang])) {
            return $this->name[$lang];
        }
        // fallback
        return $this->name['en'] ?? '';
    }
}
