<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class KazView extends Model
{
    protected $table = 'views'; // 数据库表名保持不变

    protected $fillable = [
        'user_id',
        'name',
        'template_type',
        'secret_token',
        'content_type_id',
        'config',
        'permissions',
        'status',
    ];

    protected $casts = [
        'config' => 'array',
        'permissions' => 'array',
        'status' => 'boolean',
    ];
}
