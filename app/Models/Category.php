<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $table = 'categories';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'parent_path_json',
        'show_order',
        'level',
        'final_category',
        'is_virtual',
        'icon',
        'category_id',
    ];

    protected $casts = [
        'name' => 'array',
        'slug' => 'array',
        'parent_path_json' => 'array',
        'category_id' => 'boolean',
        'description' => 'array',
        'final_category' => 'boolean',
        'is_virtual' => 'boolean',
    ];

    /**
     * 父分类关联
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * 子分类关联
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }
}
