<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class KazcmsLanguage extends Model
{
    protected $table = 'languages';

    protected $fillable = [
        'code',
        'name',
        'enabled',
        'is_default',
        'sort_order',
    ];

    public $timestamps = false;

    /**
     * Cast 'enabled' attribute to boolean.
     */
    public function getEnabledAttribute($value): bool
    {
        return (bool) $value;
    }

    /**
     * Cast 'is_default' attribute to boolean.
     */
    public function getIsDefaultAttribute($value): bool
    {
        return (bool) $value;
    }
}
