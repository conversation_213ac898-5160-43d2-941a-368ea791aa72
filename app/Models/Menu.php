<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Menu extends Model
{
    use HasFactory;

    protected $table = 'menus';

    protected $fillable = [
        'name',
        'key_name',
        'type',
        'url',
        'icon',
        'order_index',
        'parent_id',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array', // Automatically casts JSON column to array
        'is_active' => 'boolean',
    ];

    /**
     * Get the parent menu (if any).
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get child menus.
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->orderBy('order_index');
    }

    /**
     * Accessor: Localized name.
     */
    public function getDisplayNameAttribute()
    {
        $locale = app()->getLocale();
        return $this->name[$locale] ?? collect($this->name)->first();
    }

    /**
     * Scope: Only active menus.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    /**
     * Scope: By type (user/admin).
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }
}
