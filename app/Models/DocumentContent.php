<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocumentContent extends Model
{
    protected $table = 'document_contents';

    protected $fillable = [
        'name',
        'slug',
        'content',
        'data_id',
        'parent_id',
        'parent_path_json',
        'show_order',
        'level',
        'final_category',
    ];

    protected $casts = [
        'name' => 'array',           // 多语言 JSON 自动转数组
        'slug' => 'array',
        'content' => 'array',
        'parent_path_json' => 'array',
        'final_category' => 'boolean',
    ];

    /**
     * 章节的父章节
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * 章节的子章节
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->orderBy('show_order')->orderBy('id');
    }

    /**
     * 递归加载所有子章节，构造章节树
     */
    public function childrenRecursive()
    {
        return $this->children()->with('childrenRecursive');
    }

    /**
     * 判断是否是叶子章节
     */
    public function isLeaf()
    {
        return $this->final_category;
    }

    /**
     * 根据 data_id 获取指定文档的所有章节（树形）
     */
// DocumentContent.php
public static function getDocumentTreeFlat(
    int $dataId,
    int $expandLevel = 1,
    $versionId,
    array $selectFields = ['id', 'name', 'slug', 'parent_id', 'level', 'show_order'],
    array $conditions = [] // extra conditions
) {
    // Build query with base conditions
    $query = self::select($selectFields)
        ->where('data_id', $dataId)
        ->where('level', '<=', $expandLevel)
        ->where('version_id', $versionId);

    // Apply dynamic extra conditions if provided
    foreach ($conditions as $condition) {
        if (is_array($condition) && count($condition) === 3) {
            $query->where($condition[0], $condition[1], $condition[2]);
        }
    }

    // Get all matching records
    $all = $query->orderBy('show_order')->get();

    // Re-index by id
    $items = $all->keyBy('id');

    // Prepare children collections
    foreach ($items as $item) {
        $item->children = collect();
    }

    // Build tree
    $tree = collect();
    foreach ($items as $item) {
        if ($item->parent_id && $items->has($item->parent_id)) {
            $items[$item->parent_id]->children->push($item);
        } else {
            $tree->push($item);
        }
    }

    return $tree;
}


}
