<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ContentType extends Model
{
    protected $table = 'content_types';

    protected $fillable = [
        'name',
        'slug',
        'display_order',
        'show_on_frontend',
        'keywords',
        'description',
        'user_id',
        'render_type',
        'config',
    ];

    protected $casts = [
        'name' => 'array',
        'slug' => 'array',
        'keywords' => 'array',
        'description' => 'array',
        'config' => 'array',
        'show_on_frontend' => 'boolean',
    ];

    // 这里假设关联用户表
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
