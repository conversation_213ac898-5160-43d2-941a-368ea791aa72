<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldType extends Model
{
    // 指定表名
    protected $table = 'field_types';

    // 主键
    protected $primaryKey = 'id';

    // 允许批量赋值的字段
    protected $fillable = [
        'type_code',
        'related_table',
        'type_name_json',
        'description_json',
        'is_enabled',
        'created_at',
        'updated_at',
    ];

    // 时间戳自动管理
    public $timestamps = true;

    // 将JSON字段自动转换成数组
    protected $casts = [
        'type_name_json' => 'array',
        'description_json' => 'array',
        'is_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取多语言的类型名称
     *
     * @param string|null $locale 默认系统语言，例: 'en-us'
     * @return string|null
     */
    public function getTypeName(string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();
        return $this->type_name_json[$locale] ?? null;
    }

    /**
     * 获取多语言的描述
     *
     * @param string|null $locale 默认系统语言，例: 'en-us'
     * @return string|null
     */
    public function getDescription(string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();
        return $this->description_json[$locale] ?? null;
    }
}
