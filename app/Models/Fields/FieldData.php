<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Models\ContentType;
use App\Models\Fields\FieldType;
use App\Models\User;

class FieldData extends Model
{
    /**
     * 绑定的数据表
     *
     * @var string
     */
    protected $table = 'field_data';

    /**
     * 主键类型
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * 主键是否自增
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * 可以批量赋值的字段
     *
     * @var array
     */
    protected $fillable = [
        'data_id',
        'user_id',
        'content_type_id',
        'field_id',
        'field_type_id',
        'data',
    ];

    /**
     * 字段类型转换
     *
     * @var array
     */
    protected $casts = [
        'id'              => 'int',
        'data_id'         => 'int',
        'user_id'         => 'int',
        'content_type_id' => 'int',
        'field_id'        => 'int',
        'field_type_id'   => 'int',
        'data'            => 'array',   // 自动将 JSON → 数组/集合
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];

    /* -----------------------------------------------------------------
     |  关系定义
     |------------------------------------------------------------------*/

    // 内容类型
    public function contentType()
    {
        return $this->belongsTo(ContentType::class, 'content_type_id');
    }

    // 字段定义
    public function field()
    {
        return $this->belongsTo(FieldType::class, 'field_id');
    }

    // 创建者
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /* -----------------------------------------------------------------
     |  自定义访问器 / 修改器（可选）
     |------------------------------------------------------------------*/

    /**
     * 为了方便，提供一个 all 语言的快捷访问器
     */
    protected function value(): Attribute
    {
        return Attribute::get(fn () => $this->data['all'] ?? null);
    }
}
