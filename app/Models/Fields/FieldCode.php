<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldCode extends Model
{
    protected $table = 'field_codes';

    protected $fillable = [
        'content_type_id',
        'label_json',
        'html_code_json',
        'description_json',
        'is_enabled',
        'field_type_id',
    ];

    protected $casts = [
        'label_json' => 'array',
        'html_code_json' => 'array',
        'description_json' => 'array',
        'is_enabled' => 'boolean',
    ];
}
