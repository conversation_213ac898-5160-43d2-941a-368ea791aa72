<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FieldTextarea extends Model
{
    use HasFactory;

    protected $table = 'field_textarea';

    protected $fillable = [
        'field_type_id',
        'label_json',
        'help_text_json',
        'is_required',
        'content_type_id',
    ];

    protected $casts = [
        'label_json' => 'array',
        'help_text_json' => 'array',
        'is_required' => 'boolean',
    ];
}
