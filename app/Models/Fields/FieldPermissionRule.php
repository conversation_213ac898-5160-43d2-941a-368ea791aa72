<?php
// app/Models/FieldPermissionRule.php
namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldPermissionRule extends Model
{
    protected $table = 'field_permission_rules';

    protected $fillable = [
        'content_type_id',
        'field_type_id',
        'field_id',
        'action',
        'allowed_group_ids',
        'allowed_user_ids',
    ];

    protected $casts = [
        'action'             => 'array',
        'allowed_group_ids'  => 'array',
        'allowed_user_ids'   => 'array',
    ];
}
