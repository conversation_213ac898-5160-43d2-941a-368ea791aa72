<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldCategory extends Model
{
    protected $table = 'field_categories';

    protected $fillable = [
        'label_json',
        'label_slug_json',
        'display_style',
        'show_in_frontend',
        'is_required',
        'max_select_count',
        'content_type_id',
        'field_type_id',
        'category_id',
    ];

    protected $casts = [
        'label_json' => 'array',
        'label_slug_json' => 'array',
        'show_in_frontend' => 'boolean',
        'is_required' => 'boolean',
        'max_select_count' => 'integer',
        'content_type_id' => 'integer',
        'category_id' => 'integer',
    ];
    
}
