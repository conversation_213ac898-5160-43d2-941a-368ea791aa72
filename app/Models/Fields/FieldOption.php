<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldOption extends Model
{
    protected $table = 'field_options';

    protected $fillable = [
        'label_json',
        'display_style',
        'is_multiple',
        'options',
        'max_select_count',
        'is_required',
        'prefix_text_json',
        'suffix_text_json',
        'content_type_id',
        'field_type_id',
    ];

    protected $casts = [
        'label_json' => 'array',
        'display_style' => 'string',
        'prefix_text_json'=>'array',
        'suffix_text_json'=>'array',
        'options' => 'array',
        'is_multiple' => 'boolean',
        'is_required' => 'boolean',
    ];
}
