<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;
use App\Models\ContentType;

class FieldsOrder extends Model
{
    // Specify the table name (if it doesn't follow <PERSON><PERSON>'s pluralization convention)
    protected $table = 'fields_order';

    // Primary key
    protected $primaryKey = 'id';

    // Enable Laravel's automatic timestamps (created_at, updated_at)
    public $timestamps = true;

    // Mass assignable attributes
    protected $fillable = [
        'content_type_id',
        'field_id',
        'field_type_id',
        'sort_order',
    ];

    // Relationship to ContentType model
    public function contentType()
    {
        return $this->belongsTo(ContentType::class, 'content_type_id');
    }
}
