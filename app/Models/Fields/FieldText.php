<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldText extends Model
{
    protected $table = 'field_text';

    protected $fillable = [
        'field_type_id',
        'content_type_id',
        'label_json',
        'is_seo',
        'seo_type',
        'subtype',
        'placeholder_json',
        'default_value_json',
        'prefix_text_json',
        'suffix_text_json',
        'is_required',
        'min_length',
        'max_length',
        'display_color',
        'help_text_json',
    ];

    protected $casts = [
        'label_json' => 'array',
        'placeholder_json' => 'array',
        'default_value_json' => 'array',
        'prefix_text_json' => 'array',
        'suffix_text_json' => 'array',
        'help_text_json' => 'array',
        'is_seo' => 'boolean',
        'is_required' => 'boolean',
        'min_length' => 'integer',
        'max_length' => 'integer',
    ];
}
