<?php

namespace App\Models\Fields;

use Illuminate\Database\Eloquent\Model;

class FieldFile extends Model
{
    protected $table = 'field_file';

    protected $fillable = [
        'content_type_id',
        'field_type_id',
        'label_json',
        'help_text_json',
        'is_required',
        'allowed_file_types',
        'max_upload_count',
        'max_file_size_mb',
        'enable_image_preview',
        'rename_on_upload',
        'display_as_gallery',
        'auto_compress_images',
    ];

    protected $casts = [
        'label_json' => 'array',
        'help_text_json' => 'array',
        'is_required' => 'boolean',
        'enable_image_preview' => 'boolean',
        'display_as_gallery' => 'boolean',
        'auto_compress_images' => 'boolean',
    ];

   
}
