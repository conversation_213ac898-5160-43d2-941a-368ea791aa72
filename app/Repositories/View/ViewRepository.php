<?php

namespace App\Repositories\View;

use App\Repositories\Contracts\ViewRepositoryInterface;
use App\Models\KazView;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ViewRepository implements ViewRepositoryInterface
{
    public function all(?int $contentTypeId = null): Collection
    {
        $query = KazView::query();

        if ($contentTypeId) {
            $query->where('content_type_id', $contentTypeId);
        }

        return $query->get();
    }

    public function find(int $id): ?KazView
    {
        return KazView::find($id);
    }

    public function create(array $data): KazView
    {
        return KazView::create($data);
    }

    public function update(int $id, array $data): bool
    {
        $view = $this->find($id);

        if (!$view) {
            return false;
        }

        return $view->update($data);
    }

    public function delete(int $id): bool
    {
        $view = $this->find($id);

        if (!$view) {
            return false;
        }

        return (bool) $view->delete();
    }

    public function saveFields(int $viewId, array $fields): bool
    {
        return DB::transaction(function () use ($viewId, $fields) {
            DB::table('view_fields')->where('view_id', $viewId)->delete();

            foreach ($fields as $field) {
                DB::table('view_fields')->insert([
                    'view_id' => $viewId,
                    'field_id' => $field['id'],
                    'label' => $field['label'] ?? null,
                    'enabled' => $field['enabled'] ?? true,
                    'sort_order' => $field['sort_order'] ?? 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return true;
        });
    }

    public function saveConditions(int $viewId, array $conditions): bool
    {
        return DB::transaction(function () use ($viewId, $conditions) {
            DB::table('view_conditions')->where('view_id', $viewId)->delete();

            foreach ($conditions as $condition) {
                DB::table('view_conditions')->insert([
                    'view_id' => $viewId,
                    'field_id' => $condition['field_id'],
                    'operator' => $condition['operator'],
                    'value' => $condition['value'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return true;
        });
    }

    public function saveOutputs(int $viewId, array $outputs): bool
    {
        return DB::transaction(function () use ($viewId, $outputs) {
            DB::table('view_outputs')->where('view_id', $viewId)->delete();

            foreach ($outputs as $output) {
                DB::table('view_outputs')->insert([
                    'view_id' => $viewId,
                    'type' => $output['type'],
                    'enabled' => $output['enabled'] ?? true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return true;
        });
    }

    public function getConfig(int $id): array
    {
        $view = $this->find($id);

        if (!$view) {
            return [];
        }

        return [
            'view' => $view,
            'fields' => DB::table('view_fields')->where('view_id', $id)->get(),
            'conditions' => DB::table('view_conditions')->where('view_id', $id)->get(),
            'outputs' => DB::table('view_outputs')->where('view_id', $id)->get(),
        ];
    }
}
