<?php

namespace App\Repositories;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use App\Models\Fields\FieldData;
use App\Http\Controllers\Admin\AttachmentController;
use App\Http\Controllers\Admin\CategoryController;
use Illuminate\Support\Collection;

class ContentRepository
{
    public function __construct(
        private readonly AttachmentController $attachmentCtrl,
        private readonly CategoryController   $categoryCtrl
    ) {}







    public function applyFieldFilters(
        Builder $q,
        array $filters,
        bool $useOrLogic = true
    ): Builder {
        if (empty($filters)) {
            return $q;
        }
        //dd($filters);
        $q->where(function (Builder $outer) use ($filters, $useOrLogic) {
            foreach ($filters as $filter) {
                // 检查是否是 field_type_id + field_id 组合 + data 组合
                if (
                    is_array($filter)
                    && count($filter) >= 2
                    && is_int($filter[0])
                    && is_int($filter[1])
                ) {
                    [$typeId, $fieldId] = [$filter[0], $filter[1]];
                    $data = $filter[2] ?? [];
                    $clause = function (Builder $inner) use ($typeId, $fieldId, $data) {
                        $inner->where('field_type_id', $typeId)
                            ->where('field_id', $fieldId);

                        // 添加 data->key IN (...) 的逻辑
                        if (!empty($data) && is_array($data)) {
                            //dd($data);
                            $dataConditions = $data['data'] ?? [];
                            $dataLogic = $data['logic'] ?? 'and'; // and/or

                            $inner->where(function (Builder $dataSub) use ($dataConditions, $dataLogic) {
                                foreach ($dataConditions as $key => $values) {
                                    if (!is_array($values) || empty($values)) {
                                        continue;
                                    }

                                    foreach ($values as $val) {
                                        // 生成 JSON_CONTAINS 语句
                                        $rawExpr = "json_contains(`data`, ?, '$.\"{$key}\"')";
                                        $binding = json_encode($val);  // 确保绑定的是合法 JSON 值
                                        if (strtolower($dataLogic) === 'or') {
                                            $dataSub->orWhereRaw($rawExpr, [$binding]);
                                        } else {
                                            $dataSub->whereRaw($rawExpr, [$binding]);
                                        }
                                    }
                                }
                            });
                        }
                    };

                    // 整体组合逻辑
                    $useOrLogic
                        ? $outer->orWhere($clause)
                        : $outer->where($clause);
                }
            }
        });
        //dd($q->toSql(),$q->getBindings());
        return $q;
    }


/**
 * Apply date filter based on front-end inputs.
 *
 * @param \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder $q
 * @param array $dateFilter ['mode'=>'range'|'last_days', 'from'=>..., 'to'=>..., 'last_days'=>...]
 * @param string $column 日期字段，默认 'created_at'
 */
public function applyDateFilter($q, array $dateFilter, string $column = 'created_at')
{
    if (empty($dateFilter['mode'])) {
        return $q;
    }

    if ($dateFilter['mode'] === 'range') {
        $from = $dateFilter['from'] ?? null;
        $to   = $dateFilter['to'] ?? null;

        if ($from) {
            $q->whereDate($column, '>=', $from);
        }
        if ($to) {
            $q->whereDate($column, '<=', $to);
        }
    } elseif ($dateFilter['mode'] === 'last_days') {
        $days = (int)($dateFilter['last_days'] ?? 0);
        if ($days > 0) {
            $q->whereDate($column, '>=', now()->subDays($days));
        }
    }

    return $q;
}


    public function applyUserFilter(Builder $q, array $userIds): Builder
    {
        return empty($userIds) ? $q : $q->whereIn('user_id', $userIds);
    }

    public function applyChangedByFilter(Builder $q, array $userIds): Builder
    {
        return empty($userIds) ? $q : $q->whereIn('changed_by', $userIds);
    }

    public function applyKeywordSearch(Builder $q, ?array $langKw = null): Builder
    {
        if (empty($langKw)) {
            return $q;
        }

        $q = $q->where(function ($query) use ($langKw) {
            foreach ($langKw as $lang => $kw) {
                $kw = "%{$kw}%";
                if ($lang === 'all') {
                    $query->orWhere('data', 'like', $kw);
                } else {
                    $query->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(data, '$.\"{$lang}\"')) LIKE ?", [$kw]);
                }
            }
        });
        return $q;
    }


    public function applyOrdering(Builder $q, array $orders): Builder
    {
        foreach ($orders as $o) {
            if (isset($o['field_id'])) {
                $field = "field_{$o['field_id']}"; // 示例映射
                $q->orderBy($field, $o['direction'] ?? 'asc');
            } else {
                // 系统字段
                foreach ($o as $sys => $dir) {
                    $q->orderBy($sys, $dir);
                }
            }
        }
        return $q;
    }

    /**
     * 从列表行中提取附件/分类 ID，批量查询并返回
     *
     * @param Collection<int, object> $rows         Eloquent rows（或 stdClass）
     * @param array{attachments:bool,categories:bool} $extrasDetail 需要加载的选项
     * @return array{attachments: array<int, object>, categories: array<int, object>}
     */
    public function loadExtras(Collection $rows, array $extrasDetail): array
    {
        $result = [
            'attachments' => [],
            'categories' => [],
        ];

        // 定义映射：extrasDetail的key => field_type_id
        $typeMap = [
            'attachments' => 3,
            'categories' => 4,
        ];

        foreach ($typeMap as $key => $typeId) {
            if (!empty($extrasDetail[$key])) {
                // 过滤对应field_type_id的行
                $ids = $rows
                    ->filter(fn($row) => (int)$row->field_type_id === $typeId)
                    ->pluck('data')
                    ->map(fn($json) => json_decode($json, true) ?? [])
                    ->reduce(fn($carry, $item) => array_merge_recursive($carry, $item), []);

                // 打平、去重ID
                $flatUniqueIds = collect($ids)
                    ->values()
                    ->flatten()
                    ->unique()
                    ->values()
                    ->all();

                // 根据类型调用不同控制器获取数据
                if ($key === 'attachments') {
                    $result['attachments'] = $flatUniqueIds
                        ? $this->attachmentCtrl->fetchAttachmentsByIds($flatUniqueIds)
                        : [];
                } else if ($key === 'categories') {
                    $result['categories'] = $flatUniqueIds
                        ? $this->categoryCtrl->getCategoriesByIds(
                            ids: $flatUniqueIds,
                            returnType: 'collection'
                        )
                        : [];
                }
            }
        }

        return $result;
    }




    public function getDraftStatusForDataIds(array $dataIds): array
    {
        $prefix = DB::getTablePrefix();
        $results = DB::table('field_data_v as drafts')
            ->select('drafts.data_id')
            ->join('field_data_v as current', function ($join) {
                $join->on('drafts.data_id', '=', 'current.data_id')
                    ->where('current.is_current', 1);
            })
            ->whereIn('drafts.data_id', $dataIds)
            ->where('drafts.status', 'draft')
            ->whereRaw($prefix . 'drafts.version > ' . $prefix . 'current.version')
            ->groupBy('drafts.data_id')
            ->pluck('drafts.data_id');

        return $results
            ->mapWithKeys(fn($id) => [$id => true])
            ->toArray();
    }

    /**
 * Apply OR conditions for multiple (field_type_id AND field_id) pairs.
 *
 * @param \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder $query
 * @param array $pairs Format: [[field_type_id, field_id], ...]
 * @return \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder
 */
public function applyFieldTypeAndIdPairs($query, array $pairs)
{
    if (empty($pairs)) {
        return $query;
    }
//dd($pairs);
    return $query->where(function ($q) use ($pairs) {
        foreach ($pairs as [$fieldTypeId, $fieldId]) {
            $q->orWhere(function ($sub) use ($fieldTypeId, $fieldId) {
                $sub->where('field_type_id', $fieldTypeId)
                    ->where('field_id', $fieldId);
            });
        }
    });
}

}
