<?php

namespace App\Repositories\Contracts;

use Illuminate\Support\Collection;
use App\Models\KazView;

interface ViewRepositoryInterface
{
    /**
     * Get all views (optionally by content type)
     */
    public function all(?int $contentTypeId = null): Collection;

    /**
     * Find a single view by ID
     */
    public function find(int $id): ?KazView;

    /**
     * Create a new view
     */
    public function create(array $data): KazView;

    /**
     * Update an existing view
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a view
     */
    public function delete(int $id): bool;

    /**
     * Save field configuration (fields selected in the view)
     */
    public function saveFields(int $viewId, array $fields): bool;

    /**
     * Save conditions (filters) for a view
     */
    public function saveConditions(int $viewId, array $conditions): bool;

    /**
     * Save output configuration (HTML, Excel, CSV, API, etc.)
     */
    public function saveOutputs(int $viewId, array $outputs): bool;

    /**
     * Get full configuration of a view
     */
    public function getConfig(int $id): array;
}
