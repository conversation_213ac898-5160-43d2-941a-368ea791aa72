<?php

namespace App\Services;

use App\Models\Fields\FieldCategory; // Your field category model
use App\Models\Category;
use Illuminate\Support\Facades\Redirect;
use App\Exceptions\MatchException;

class CategoryService
{
    /**
     * Resolve multi-level category paths by slug from URL parameters,
     * based on content type ID and language,
     * returning arrays of category models.
     *
     * @param array $queryParams  Associative array like ['category' => 'tech/mobile', 'brand' => 'apple']
     * @param int $contentTypeId  Content type ID
     * @param string $lang        Language code, e.g. 'en'
     * @return array              Result format: ['category' => [Category, Category], 'brand' => [Category]]
     */

    public function resolveCategoryPathsBySlug(array $queryParams, int $contentTypeId, string $lang = 'en-us'): array
    {
        $fieldData = [];
        // Get all active languages
        $availableLangs = getActiveLanguages();

        $fieldCategoryMap = [];
        foreach ($queryParams as $fieldLabel => $slugPaths) {
            // Find field by localized slug
            $fieldQuery = FieldCategory::query()
                ->where('content_type_id', $contentTypeId)
                ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(label_slug_json, '$.\"$lang\"')) = ?", [$fieldLabel]);

            $fields = $fieldQuery->get();
            //dd($fields->count());
            if ($fields->count() !== 1) {
                //101 in field categories in same content type ,same language have same slug
                throw new MatchException('Field category match error', 101, [
                    'field_label' => $fieldLabel,
                    'matched_count' => $fields->count(),
                    'table' => 'field_categories',
                    'lang' => $lang,
                    'fieldLabel' => $fieldLabel,
                    'contentTypeId' => $contentTypeId,
                    'current_url' => url()->full(), //
                ]);
            }
            // 正常情况：找到唯一的 field
            $field = $fields->first();
            $fieldCategoryMap[$field->id]['field'] = $field;

            $rootCategoryId = $field->category_id;
            $individualPaths = explode('~', $slugPaths);
            $allCategoryIds = [];
            $pathCategoryChains = []; // To collect full category chains per path

            foreach ($individualPaths as $path) {
                $slugs = explode('.', trim($path));
                $currentParentId = $rootCategoryId;
                $currentCategory = null;
                $categoryChain = [];

                foreach ($slugs as $levelIndex => $slug) {
                    $slugLevel = $levelIndex + 2;
                    //dd($rootCategoryId,$slugLevel,$currentParentId,$lang);
                    $category = Category::query()
                        ->where('category_id', $rootCategoryId)
                        ->where('level', $slugLevel)
                        ->where('parent_id', $currentParentId)
                        ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(slug, '$.\"$lang\"')) = ?", [$slug])
                        ->first();
                    //dd($category);
                    if (!$category) {
                        $currentCategory = null;
                        break;
                    }

                    $categoryChain[] = $category;
                    $fieldCategoryMap[$field->id]['categories'][$path][] = $category;

                    $currentParentId = $category->id;
                    $currentCategory = $category;
                }

                if (!empty($categoryChain)) {
                    $pathCategoryChains[] = $categoryChain;
                }

                if ($currentCategory) {
                    $allCategoryIds[] = $currentCategory->id;

                    if ((int)$currentCategory->final_category !== 0) {
                        $descendants = Category::query()
                            ->where('final_category', 0)
                            ->whereJsonContains('parent_path_json', $currentCategory->id)
                            ->pluck('id')
                            ->toArray();

                        $allCategoryIds = array_merge($allCategoryIds, $descendants);
                    }
                }
            }

            $allCategoryIds = array_unique($allCategoryIds);

            // Store all matched category ids
            $fieldData[$field->id] = [
                'category_ids' => $allCategoryIds,
            ];

            // Build merged category tree
//dd($pathCategoryChains);
            $fieldCategoryMap[$field->id]['category_tree'] = self::buildMergedCategoryTree($pathCategoryChains, $lang);
        }
        //dd( $fieldCategoryMap);
        return [
            'fieldData' => $fieldData,
            'fieldCategoryMap' => $fieldCategoryMap,
        ];
    }

    /**
     * Merge multiple category chains into a single nested tree.
     *
     * @param array $categoryChains Array of category arrays, each representing a full path
     * @param string $lang
     * @return array
     */
    protected static function buildMergedCategoryTree(array $categoryChains, string $lang = 'en-us'): array
    {
        $tree = [];

        foreach ($categoryChains as $chain) {
            $currentLevel = &$tree;

            foreach ($chain as $category) {
                $catId = $category->id;

                // Find or create this category at current level
                if (!isset($currentLevel[$catId])) {
                    $currentLevel[$catId] = [
                        'id' => $catId,
                        'name' => $category->name[$lang] ?? '',
                        'slug' => $category->slug[$lang] ?? '',
                        'final_category' => (int) $category->final_category,
                        'children' => []
                    ];
                }

                // Move to children for next iteration
                $currentLevel = &$currentLevel[$catId]['children'];
            }
        }
//dd($tree);
        return $tree; 
    }

    public function generateLocalizedUrls(
        array $fieldCategoryMap,
        string $currentUrl,
        string $currentLang
    ): array {
        $languages = getActiveLanguages();

        // 1. Parse current URL
        $parsed = parse_url($currentUrl);
        $basePath = $parsed['path'] ?? '/';
        parse_str($parsed['query'] ?? '', $originalQueryParams);

        $localizedUrls = [];

        foreach ($languages as $langCode => $langName) {
            $queryParams = $originalQueryParams;
//dd($fieldCategoryMap);
            foreach ($fieldCategoryMap as $fieldEntry) {
                $field = $fieldEntry['field'];
                $categoriesByPath = $fieldEntry['categories'] ?? [];
                //$labelSlugs = json_decode($field->label_slug_json, true);
                $labelSlugs = $field->label_slug_json;
                $currentFieldKey = $labelSlugs[$currentLang] ?? null;
                $translatedFieldKey = $labelSlugs[$langCode] ?? null;

                // Skip if currentLang field key doesn't exist in query
                if (!$currentFieldKey || !array_key_exists($currentFieldKey, $queryParams)) {
                    continue;
                }
//dd($categoriesByPath);
                // Generate new slug path for this field in this language
                $pathStrings = [];

                foreach ($categoriesByPath as $categoryChain) {
                    $slugs = [];
                    foreach ($categoryChain as $category) {
                        $slugArray = $category->slug;
                        $localizedSlug = $slugArray[$langCode] ?? null;

                        if ($localizedSlug === null) {
                            $slugs = [];
                            break;
                        }

                        $slugs[] = $localizedSlug;
                    }

                    if (!empty($slugs)) {
                        $pathStrings[] = implode('.', $slugs);
                    }
                }

                // Replace the parameter
                if ($translatedFieldKey && !empty($pathStrings)) {
                    $queryParams[$translatedFieldKey] = implode('~', $pathStrings);
                    if ($translatedFieldKey !== $currentFieldKey) {
                        unset($queryParams[$currentFieldKey]);
                    }
                }
            }

            // Build the localized URL
            $queryString = http_build_query($queryParams);
            $queryString = str_replace('%7E', '~', $queryString);
            
            $localizedUrls[$langCode] = $queryString ? '?' . $queryString : '';

        }
        //dd($localizedUrls);
        return $localizedUrls;
    }
}
