<?php

namespace App\Services;

use Illuminate\Support\Arr;

class TemplateService
{
    protected string $locale;

    public function __construct()
    {
        $this->locale = app()->getLocale();
    }

    /**
     * Render full template for selected fields
     */
    public function renderTemplateStructure(
        array $fields,
        array $selectedFields,
        string $mode = 'detail',
        int $viewid = 0,
        array $options = []
    ): string {
        //dd($fields, $selectedFields);
        // 初始化输出
        $output = "@php\n    \$locale = app()->getLocale();\n@endphp\n\n";
        $paginationHtml = "";
    
        // 循环字段
        foreach ($selectedFields as $fieldKey) {
            // 解析 fieldKey
            $parts = explode('-', $fieldKey, 2);
            $fieldKey = $parts[1] ?? $fieldKey;
            if (!isset($fields[$fieldKey])) continue;
    
            $field = (array) $fields[$fieldKey];
            $fieldType = $field['field_type_id'] ?? null;
    
            // 构建渲染方法名称
            $methodBase = ucfirst($this->fieldTypeToMethodName($fieldType));
            $renderMethod = match ($mode) {
                'form'   => "render{$methodBase}FormField",
                'list', 'detail' => "render{$methodBase}Field",
                default => null
            };
    
            if ($renderMethod && method_exists($this, $renderMethod)) {
                // form 模式只传 $field，其他模式传 $field 和 fieldKey
                $output .= $mode === 'form'
                    ? $this->$renderMethod($field)
                    : $this->$renderMethod($field, $fieldKey);
    
                $output .= "\n\n";
            }
        }
    
        // list 模式加 @forelse & pagination
        if ($mode === 'list') {
            $output = "@forelse (\$contents['data'] as \$dataId => \$data)\n" . $output;
            $output .= "@empty\n    <p>No content available.</p>\n@endforelse\n";
    
            if (!empty($options['pagination']['enabled'])) {
                $paginationHtml = <<<HTML
                                    {{-- Pagination --}}
                                    <div class="px-8 py-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
                                        <div class="flex items-center justify-between">
                                            <div class="pagination-wrapper">
                                                {{ \$paginator->appends(request()->query())->links() }}
                                            </div>
                                        </div>
                                    </div>
                                HTML;
            }
        }
    
        // form 模式添加表单注释
        if ($mode === 'form') {
            $prepend = <<<HTML
    
    {{-- This is a data submission form. 
         Use it when the user needs to submit or update data. 
    --}}
    
    HTML;
    $output = $prepend . $output;
        }
    
        // viewid 注释
        if ($viewid) {
            $output = "{{-- kazview:{$viewid} --}} \n" . $output;
        }
    
        return $output . $paginationHtml;
    }
    


    protected function fieldTypeToMethodName(?int $fieldType): string
    {
        return match ($fieldType) {
            1 => 'text',
            2 => 'textarea',
            3 => 'file',
            4 => 'categories',
            5 => 'options',
            6 => 'codes',
            default => 'unknown',
        };
    }

    protected function renderTextField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Text Field');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$value = isset(\$data['{$fieldName}']) && isset(\$data['{$fieldName}']->data) ? json_decode(\$data['{$fieldName}']->data,true)[\$locale] ?? '' : '';
                        \$prefix = isset(\$fields['{$fieldName}']['prefix_text_json']) ? json_decode(\$fields['{$fieldName}']['prefix_text_json'], true)[\$locale] ?? '' : '';
                        \$suffix = isset(\$fields['{$fieldName}']['suffix_text_json']) ? json_decode(\$fields['{$fieldName}']['suffix_text_json'], true)[\$locale] ?? '' : '';
                    @endphp

                    @if(\$value)
                    <div class="flex items-start space-x-3 group">
                        @if(\$prefix)
                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                {{ \$prefix }}
                            </span>
                        @endif
                        <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
                            {!! nl2br(e(\$value)) !!}
                        </div>
                        @if(\$suffix)
                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                {{ \$suffix }}
                            </span>
                        @endif
                    </div>
                    @endif
                    HTML;
    }

    protected function renderTextareaField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Textarea');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$value = isset(\$data['{$fieldName}']) && isset(\$data['{$fieldName}']->data) ? json_decode(\$data['{$fieldName}']->data,true)[\$locale] ?? '' : '';
                    @endphp

                    @if(\$value)
                    <div class="relative">
                        <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono">
                            TEXT
                        </div>
                        <div class="text-gray-800 text-sm font-mono leading-relaxed whitespace-pre-wrap bg-gradient-to-br from-gray-50 to-slate-50 p-6 border border-gray-200 rounded-xl shadow-inner">
                            {!! nl2br(e(\$value)) !!}
                        </div>
                    </div>
                    @endif
                    HTML;
    }

    protected function renderFileField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Files');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$image_ids = isset(\$data['{$fieldName}']) && isset(\$data['{$fieldName}']->data) ? json_decode(\$data['{$fieldName}']->data,true)[\$locale] ?? [] : [];
                        \$images = collect(\$image_ids)
                            ->map(fn(\$id) => \$contents['extras']['attachments'][\$id]->url ?? null)
                            ->filter()
                            ->values()
                            ->all();
                    @endphp

                    @if(count(\$images))
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        @foreach(\$images as \$img)
                            <div class="group relative aspect-square bg-gray-100 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                                <img src="{{ \$img }}" alt="Image" class="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300">
                            </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-8 text-gray-400">
                        <p class="text-sm">No images available</p>
                    </div>
                    @endif
                    HTML;
    }

    protected function renderCodesField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'HTML Code');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$htmlCode = isset(\$fields['{$fieldName}']['html_code_json']) ? json_decode(\$fields['{$fieldName}']['html_code_json'], true)[\$locale] ?? '' : '';
                        \$isEnabled = \$fields['{$fieldName}']['is_enabled'] ?? true;
                    @endphp

                    @if(\$isEnabled && \$htmlCode)
                    <div class="prose prose-sm max-w-none text-gray-700 bg-gradient-to-br from-emerald-50 to-teal-50 p-6 border border-emerald-200 rounded-xl shadow-inner">
                        {!! \$htmlCode !!}
                    </div>
                    @endif
                    HTML;
    }

    protected function renderCategoriesField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Categories');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$selectedCategories = isset(\$data['{$fieldName}']) && isset(\$data['{$fieldName}']->data) ? json_decode(\$data['{$fieldName}']->data,true)['all'] ?? [] : [];
                    @endphp

                    @if(is_array(\$selectedCategories) && count(\$selectedCategories))
                    <div class="flex flex-wrap gap-2">
                        @foreach(\$selectedCategories as \$cat_id)
                            <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-800 bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 rounded-full">
                                {{ \$data['categories'][\$cat_id]['name'][\$locale] ?? 'Untitled Category' }}
                            </span>
                        @endforeach
                    </div>
                    @endif
                    HTML;
    }

    protected function renderOptionsField(array $field, string $fieldName): string
    {
        $label = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Options');

        return <<<HTML
                    {{-- field name: {$label} --}}
                    @php
                        \$decoded = isset(\$data['{$fieldName}']) && isset(\$data['{$fieldName}']->data) ? json_decode(\$data['{$fieldName}']->data,true) : [];
                        \$selected = is_array(\$decoded['all'] ?? []) ? \$decoded['all'] : [];
                        \$options = collect(json_decode(\$field['options'] ?? '[]', true) ?? [])
                            ->filter(fn(\$opt) => \$opt['enabled'] ?? false)
                            ->sortBy('sort_order');
                    @endphp

                    @if(count(\$selected))
                    <div class="grid gap-3">
                        @foreach(\$selected as \$val)
                            @php
                                \$matched = \$options->firstWhere('value', \$val);
                                \$labelText = \$matched['label'][\$locale] ?? \$val;
                            @endphp
                            <div class="flex items-center p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg">
                                <span class="text-gray-800 font-medium">{{ \$labelText }}</span>
                            </div>
                        @endforeach
                    </div>
                    @endif
                    HTML;
    }



 

    /** ---------------- FORM FIELD RENDERERS ---------------- */



    protected function del_renderTextFormField(array $field): string
    {
        $labelDescription = Arr::get(json_decode($field['label_json'], true), $this->locale, 'Text input Field');
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field['field_type_id']}_{$field['id']}";
    
        $currentLang = app()->getLocale();
        $label = json_decode($field['label_json'], true)[$currentLang] ?? 'Text Field';
        $placeholders = json_decode($field['placeholder_json'], true) ?? [];
        $help_texts = json_decode($field['help_text_json'], true) ?? [];
        $prefixes = json_decode($field['prefix_text_json'], true) ?? [];
        $suffixes = json_decode($field['suffix_text_json'], true) ?? [];
    
        $required = $field['is_required'] ?? false;
        $min = $field['min_length'] ?? null;
        $max = $field['max_length'] ?? null;
        $only_show_locale = $field['only_show_locale'] ?? true;
    
        // Determine enabled languages (you can adjust based on your app)
        $enabledLanguages = $field['enabled_languages'] ?? [ ['code' => $currentLang] ];
        $enabledLanguagesFiltered = $only_show_locale
            ? array_filter($enabledLanguages, fn($lang) => $lang['code'] === $currentLang)
            : $enabledLanguages;
    
        $showTabs = count($enabledLanguagesFiltered) > 1;
    
        // Build tabs HTML if needed
        $tabsHtml = "";
        if ($showTabs) {
            foreach ($enabledLanguagesFiltered as $index => $lang) {
                $activeClass = $index === 0 ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700';
                $tabsHtml .= "<button type=\"button\" role=\"tab\" aria-selected=\"" . ($index===0?'true':'false') . "\" aria-controls=\"tab-panel-{$lang['code']}-{$field_ids}\" id=\"tab-{$lang['code']}-{$field_ids}\" data-lang=\"{$lang['code']}\" data-field=\"{$field_ids}\" class=\"py-2 px-4 text-sm font-medium rounded-t-lg focus:outline-none {$activeClass}\">" . strtoupper($lang['code']) . "</button>";
            }
            $tabsHtml = "<div class=\"border-b border-gray-200\"><nav class=\"flex -mb-px space-x-4\" aria-label=\"Tabs\" id=\"language-tabs\" role=\"tablist\">{$tabsHtml}</nav></div>";
        }
    
        // Build input panels
        $inputsHtml = "{{-- field name: {$labelDescription} --}}\n";
        foreach ($enabledLanguagesFiltered as $index => $lang) {
            $langCode = $lang['code'];
            $placeholder = $placeholders[$langCode] ?? '';
            $help = $help_texts[$langCode] ?? '';
            $prefix = $prefixes[$langCode] ?? '';
            $suffix = $suffixes[$langCode] ?? '';
            $blockClass = ($index === 0 || !$showTabs) ? 'block' : 'hidden';
            $prefixHtml = $prefix ? "<span class=\"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm\">{$prefix}</span>" : '';
            $suffixHtml = $suffix ? "<span class=\"inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm\">{$suffix}</span>" : '';
            $roundedClass = $prefix ? 'rounded-none rounded-r-md' : 'rounded-md';
            $borderRightClass = $suffix ? 'border-r-0' : '';
            $helpHtml = $help ? "<p class=\"text-xs text-gray-500 mt-1\">{$help}</p>" : '';
            $inputsHtml .= "<div role=\"tabpanel\" id=\"tab-panel-{$langCode}-{$field_ids}\" aria-labelledby=\"tab-{$langCode}-{$field_ids}\" class=\"{$blockClass} pt-4 tabpanel-{$field_ids}\">
                <div class=\"flex rounded-md shadow-sm\">
                    {$prefixHtml}
                    <input type=\"text\" name=\"{$field_name}[{$langCode}]\" value=\"{{ old('{$field_name}.{$langCode}', \$data['{$field_name}'][\$locale] ?? '') }}\" placeholder=\"{$placeholder}\" class=\"block w-full px-3 py-2 border border-gray-300 {$roundedClass} {$borderRightClass} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\">
                    {$suffixHtml}
                </div>
                {$helpHtml}
            </div>";
        }
    
        // Required / min / max info
        $requireHtml = '';
        if ($required || $min || $max) {
            $requireHtml = "<div id=\"req_{$field_name}\" class=\"need_require text-sm mt-1 text-red-600\" data-required=\"" . ($required?'1':'0') . "\" data-minlength=\"" . ($min ?? '') . "\" data-maxlength=\"" . ($max ?? '') . "\"></div>";
        }
    
        // Combine everything
        return "<div class=\"w-48 flex items-start pt-3\">
            <label class=\"block text-lg font-semibold text-gray-900\">{$label}" . ($required?'<span class="text-red-500 ml-1">*</span>':'') . "</label>
        </div>
        <div class=\"flex-1\">
            {$tabsHtml}
            {$inputsHtml}
            {$requireHtml}
        </div>";
    }
    protected function renderTextFormField($field, $enabledLanguages = [])
    {
        //return '';
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        return <<<BLADE
    @php
      // Unique variables for this field
      \$field = \$fields->{'{$field_ids_clean}'};

      \$field_ids_{$field_ids} = "{$field_ids}";
      \$field_name_{$field_ids} = "{$field_name}";
    
      \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'Text Field';
      \$placeholders_{$field_ids} = json_decode(\$field->placeholder_json, true) ?? [];
      \$help_texts_{$field_ids} = json_decode(\$field->help_text_json, true) ?? [];
      \$prefixes_{$field_ids} = json_decode(\$field->prefix_text_json, true) ?? [];
      \$suffixes_{$field_ids} = json_decode(\$field->suffix_text_json, true) ?? [];
    
      \$required_{$field_ids} = \$field->is_required ?? false;
      \$min_{$field_ids} = \$field->min_length ?? null;
      \$max_{$field_ids} = \$field->max_length ?? null;
    
      \$only_show_locale_{$field_ids} = \$field->only_show_locale ?? true;
      \$currentLang_{$field_ids} = app()->getLocale();
    
      // Filter languages to show
      \$enabledLanguagesFiltered_{$field_ids} = \$only_show_locale_{$field_ids}
        ? collect(\$enabledLanguages)->filter(fn(\$lang) => \$lang->code === \$currentLang_{$field_ids})->values()
        : collect(\$enabledLanguages);
    
      \$showTabs_{$field_ids} = \$enabledLanguagesFiltered_{$field_ids}->count() > 1;
    @endphp
    
    <!-- Left Label -->
    <div class="w-48 flex items-start pt-3">
      <label class="block text-lg font-semibold text-gray-900">
        {{ \$label_{$field_ids} }} @if(\$required_{$field_ids})<span class="text-red-500 ml-1">*</span>@endif
      </label>
    </div>
    
    <!-- Right Input Area -->
    <div class="flex-1">
    
      @if(\$showTabs_{$field_ids})
        <!-- Tabs for multiple languages -->
        <div class="border-b border-gray-200">
          <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{\$field_ids_{$field_ids}}}" role="tablist">
            @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
              <button
                type="button"
                role="tab"
                aria-selected="{{ \$loop->first ? 'true' : 'false' }}"
                aria-controls="tab-panel-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                id="tab-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                data-lang="{{ \$lang->code }}"
                data-field="{{ \$field_ids }}"
                class="py-2 px-4 text-sm font-medium rounded-t-lg
                  focus:outline-none
                  {{ \$loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                {{ strtoupper(\$lang->code) }}
              </button>
            @endforeach
          </nav>
        </div>
      @endif
    
      <!-- Input(s) -->
      @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
        @php
          \$langCode = \$lang->code;
          \$placeholder = \$placeholders_{$field_ids}[\$langCode] ?? '';
          \$help = \$help_texts_{$field_ids}[\$langCode] ?? '';
          \$prefix = \$prefixes_{$field_ids}[\$langCode] ?? '';
          \$suffix = \$suffixes_{$field_ids}[\$langCode] ?? '';
        @endphp
    
        <div
          role="tabpanel"
          id="tab-panel-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}"
          aria-labelledby="tab-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}"
          class="{{ (!\$showTabs_{$field_ids} || \$loop->first) ? 'block' : 'hidden' }} pt-4 tabpanel-{{\$field_ids_{$field_ids}}}"
        >
          <div class="flex rounded-md shadow-sm">
            @if(\$prefix)
              <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                {{ \$prefix }}
              </span>
            @endif
    
            <input
              type="text"
              name="{{ \$field_name_{$field_ids} }}[{{ \$langCode }}]"
              value="{{ old(\$field_name_{$field_ids} . '.' . \$langCode, \$data[\$field_name_{$field_ids}][\$langCode] ?? '') }}"
              placeholder="{{ \$placeholder }}"
              @if(\$required_{$field_ids}) required @endif
              @if(\$min_{$field_ids}) minlength="{{\$min_{$field_ids}}}" @endif
              @if(\$max_{$field_ids}) maxlength="{{\$max_{$field_ids}}}" @endif
              class="block w-full px-3 py-2 border border-gray-300
                     {{ \$prefix ? 'rounded-none rounded-r-md' : 'rounded-md' }}
                     {{ \$suffix ? 'border-r-0' : '' }}
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
    
            @if(\$suffix)
              <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                {{ \$suffix }}
              </span>
            @endif
          </div>
    
          @if(\$help)
            <p class="text-xs text-gray-500 mt-1">{{ \$help }}</p>
          @endif
        </div>
      @endforeach
    
      @if(\$required_{$field_ids} || \$min_{$field_ids} || \$max_{$field_ids})
        <div
          id="req_{{ \$field_name_{$field_ids} }}"
          class="need_require text-sm mt-1 text-red-600"
          data-required="{{ \$required_{$field_ids} ? '1' : '0' }}"
          data-minlength="{{ \$min_{$field_ids} ?? '' }}"
          data-maxlength="{{ \$max_{$field_ids} ?? '' }}"
        ></div>
      @endif
    
    </div>
    BLADE;
    }
    
    
    

    protected function renderTextareaFormField($field, $enabledLanguages = [], $data = [])
    {
        //return '';
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        return <<<BLADE
    @php
        // Unique variables for this field
        \$field = \$fields->{'{$field_ids_clean}'};
    
        \$field_ids_{$field_ids} = "{$field_ids}";
        \$field_name_{$field_ids} = "{$field_name}";
    
        \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'Code Field';
        \$help_texts_{$field_ids} = json_decode(\$field->help_text_json ?? '{}', true);
        \$required_{$field_ids} = \$field->is_required ?? false;
    
        \$only_show_locale_{$field_ids} = \$field->only_show_locale ?? true;
        \$currentLang_{$field_ids} = app()->getLocale();
    
        // Filter languages to show
        \$enabledLanguagesFiltered_{$field_ids} = \$only_show_locale_{$field_ids}
            ? collect(\$enabledLanguages)->filter(fn(\$lang) => \$lang->code === \$currentLang_{$field_ids})->values()
            : collect(\$enabledLanguages);
    
        \$showTabs_{$field_ids} = \$enabledLanguagesFiltered_{$field_ids}->count() > 1;
    @endphp
    
    <!-- Left Label -->
    <div class="w-48 flex items-start pt-3">
        <label class="block text-lg font-semibold text-gray-900">
            {{ \$label_{$field_ids} }} @if(\$required_{$field_ids})<span class="text-red-500 ml-1">*</span>@endif
        </label>
    </div>
    
    <!-- Right side textarea(s) -->
    <div class="flex-1">
    
        @if(\$showTabs_{$field_ids})
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{\$field_ids_{$field_ids}}}" role="tablist">
                    @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
                        <button
                            type="button"
                            role="tab"
                            aria-selected="{{ \$loop->first ? 'true' : 'false' }}"
                            aria-controls="tab-panel-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                            id="tab-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                            data-lang="{{ \$lang->code }}"
                            data-field="{{ \$field_ids }}"
                            class="py-2 px-4 text-sm font-medium rounded-t-lg
                                focus:outline-none
                                {{ \$loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                            {{ strtoupper(\$lang->code) }}
                        </button>
                    @endforeach
                </nav>
            </div>
        @endif
    
        @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
            @php
                \$langCode = \$lang->code;
                \$help = \$help_texts_{$field_ids}[\$langCode] ?? '';
                \$value = old(\$field_name_{$field_ids} . '.' . \$langCode, \$data[\$field_name_{$field_ids}][\$langCode] ?? '');
            @endphp
            <div 
                class="{{ (!\$showTabs_{$field_ids} || \$loop->first) ? 'block' : 'hidden' }} tabpanel-{{\$field_ids_{$field_ids}}}"
                aria-labelledby="tab-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}"
                id="tab-panel-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}"
            >
                <textarea
                    id="textarea_\{\$field_name_{$field_ids}\}_\{\$langCode\}"
                    name="{{ \$field_name_{$field_ids} }}[{{ \$langCode }}]"
                    rows="6"
                    placeholder="{{ \$help }}"
                    class="kazcms-rich-textarea w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono resize-y"
                    @if(\$required_{$field_ids}) required @endif
                >{{ \$value }}</textarea>
                @error(\$field_name_{$field_ids} . '.' . \$langCode)
                    <p class="mt-1 text-sm text-red-600">{{ \$message }}</p>
                @enderror
            </div>
        @endforeach
    
        @if(\$required_{$field_ids})
            <div
                id="req_\{\$field_name_{$field_ids}\}"
                class="need_require text-sm mt-1 text-red-600"
                data-required="1"
            ></div>
        @endif
    </div>
    
    <link rel="stylesheet" href="{{ asset('static/css/kazeditor.css') }}">
    <script src="{{ asset('static/js/kazeditor.'.app()->getLocale().'.js') }}"></script>
    <script src="{{ asset('static/js/kaz-editor.umd.js') }}?v=1.0.2"></script>
    <script>
        // Optional KazEditor init
        // document.addEventListener('DOMContentLoaded', function () {
        //     const allButtons = Object.keys(window.KazEditorLang || {});
        //     window.KazEditor.KazEditor.init({
        //         lang: '{{ app()->getLocale() }}',
        //         target: 'textarea.kazcms-rich-textarea',
        //         autosync: true,
        //         toolbar: allButtons
        //     });
        // });
    </script>
    BLADE;
    }
    
    

    protected function renderFileFormField($field, $enabledLanguages = [])
    {
        //return '';
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        $acceptTypes = $field->allowed_file_types ?? '';
        $acceptMime = collect(explode(',', $acceptTypes))
            ->map(fn($ext) => '.' . trim($ext))
            ->join(',');
    
        $maxUploadCount = $field->max_upload_count ?? 1;
    
        return <<<BLADE
    @php
        // Unique variables for this field
        \$field = \$fields->{'{$field_ids_clean}'};
    
        \$field_ids_{$field_ids} = "{$field_ids}";
        \$field_name_{$field_ids} = "{$field_name}";
    
        \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'File Field';
        \$help_texts_{$field_ids} = json_decode(\$field->help_text_json ?? '{}', true);
        \$required_{$field_ids} = \$field->is_required ?? false;
    
        \$only_show_locale_{$field_ids} = \$field->only_show_locale ?? true;
        \$currentLang_{$field_ids} = app()->getLocale();
    
        // Filter languages to show
        \$enabledLanguagesFiltered_{$field_ids} = \$only_show_locale_{$field_ids}
            ? collect(\$enabledLanguages)->filter(fn(\$lang) => \$lang->code === \$currentLang_{$field_ids})->values()
            : collect(\$enabledLanguages);
    
        \$showTabs_{$field_ids} = \$enabledLanguagesFiltered_{$field_ids}->count() > 1;
        \$acceptMime_{$field_ids} = '{$acceptMime}';
        \$maxUploadCount_{$field_ids} = {$maxUploadCount};
    @endphp
    
    <div class="w-48 flex items-start pt-3">
        <label class="block text-lg font-semibold text-gray-900">
            {{ \$label_{$field_ids} }} @if(\$required_{$field_ids})<span class="text-red-500 ml-1">*</span>@endif
        </label>
    </div>
    
    <div class="flex-1">
    
        @if(\$showTabs_{$field_ids})
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{\$field_ids_{$field_ids}}}" role="tablist">
                    @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
                        <button
                            type="button"
                            role="tab"
                            aria-selected="{{ \$loop->first ? 'true' : 'false' }}"
                            aria-controls="tab-panel-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                            id="tab-{{ \$lang->code }}-{{\$field_ids_{$field_ids}}}"
                            data-lang="{{ \$lang->code }}"
                            data-field="{{ \$field_ids }}"
                            class="py-2 px-4 text-sm font-medium rounded-t-lg
                                focus:outline-none
                                {{ \$loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                            {{ strtoupper(\$lang->code) }}
                        </button>
                    @endforeach
                </nav>
            </div>
    
            {{-- Checkbox for shared images --}}
            <div class="mt-4 flex items-center space-x-2">
                <input 
                    type="checkbox"
                    id="shared_{{ \$field_name_{$field_ids} }}"
                    name="shared_{{ \$field_name_{$field_ids} }}"
                    value="1"
                    class="shared-sameimages"
                    data-field-ids="{{ \$field_ids_{$field_ids} }}"
                    data-primary="{{ \$currentLang_{$field_ids} }}"
                    {{ old("shared_{\$field_name_{$field_ids}}") ? 'checked' : '' }}
                >
                <label for="shared_{{ \$field_name_{$field_ids} }}" class="text-sm text-gray-700">
                    Use the same files for all languages
                </label>
            </div>
        @endif
    
        @foreach(\$enabledLanguagesFiltered_{$field_ids} as \$index => \$lang)
            @php
                \$langCode = \$lang->code;
                \$help = \$help_texts_{$field_ids}[\$langCode] ?? '';
                \$existingImages = collect(\$data[\$field_name_{$field_ids}][\$langCode] ?? [])
                    ->map(fn(\$id) => \$data['attachments'][\$id]->url ?? null)
                    ->filter()
                    ->values()
                    ->all();
                \$existingImagesValue = old("existing_images_\{\$field_name_{$field_ids}\}.\{\$langCode\}", json_encode(\$existingImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                \$panelClass = \$showTabs_{$field_ids} ? (\$loop->first ? 'block' : 'hidden') : 'block';
            @endphp
    
            <div class="code-tab-content {{ \$panelClass }} tabpanel-{{\$field_ids_{$field_ids}}}" id="tab-panel-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}" aria-labelledby="tab-{{ \$langCode }}-{{\$field_ids_{$field_ids}}}">
                <input
                    type="file"
                    class="kaz-file-input"
                    data-preview="PreviewContainer_{{ \$field_name_{$field_ids} }}_{{ \$langCode }}"
                    name="{{ \$field_name_{$field_ids} }}[{{ \$langCode }}]"
                    id="{{ \$field_name_{$field_ids} }}_{{ \$langCode }}"
                    {{ \$acceptMime_{$field_ids} ? "accept=\{\$acceptMime_{$field_ids}\}" : '' }}
                    {{ \$maxUploadCount_{$field_ids} > 1 ? 'multiple' : '' }}
                    data-target-existing="#existingImages_{{ \$field_name_{$field_ids} }}_{{ \$langCode }}"
                >
    
                <input
                    type="text"
                    id="existingImages_{{ \$field_name_{$field_ids} }}_{{ \$langCode }}"
                    name="existing_images_{{ \$field_name_{$field_ids} }}[{{ \$langCode }}]"
                    value='{{ \$existingImagesValue }}'
                >
    
                <input 
                    type="text" 
                    name="image_order_{{ \$field_name_{$field_ids} }}[{{ \$langCode }}]" 
                    value="" 
                >
    
                @if(\$help)
                    <p class="mt-1 text-sm text-gray-500">{{ \$help }}</p>
                @endif
    
                @if(\$required_{$field_ids})
                    <div
                        id="req_{{ \$field_name_{$field_ids} }}"
                        class="need_require text-sm mt-1 text-red-600"
                        data-required="1"
                        data-maxupload="{{ \$maxUploadCount_{$field_ids} }}"
                    ></div>
                @endif
    
                @error(\$field_name_{$field_ids} . '.' . \$langCode)
                    <p class="mt-1 text-sm text-red-600">{{ \$message }}</p>
                @enderror
            </div>
        @endforeach
    </div>
    
    @push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const toggleGroupInheritance = (checkbox) => {
            const fieldIds = checkbox.dataset.fieldIds;
            const primaryLang = checkbox.dataset.primary;
            const isChecked = checkbox.checked;
            document.querySelectorAll(`.tabpanel-\${fieldIds}`).forEach(panel => {
                const isPrimary = panel.id.endsWith(\`\${primaryLang}-\${fieldIds}\`);
                if (isPrimary) return;
                panel.classList.toggle('kaz-inherited', isChecked);
            });
            document.querySelectorAll(`#language-tabs button[data-field="\${fieldIds}"]`).forEach(tab => {
                const isPrimary = tab.dataset.lang === primaryLang;
                if (isPrimary) return;
                tab.classList.toggle('tab-inherited', isChecked);
            });
        };
        document.querySelectorAll('input.shared-sameimages').forEach(checkbox => {
            toggleGroupInheritance(checkbox);
            checkbox.addEventListener('change', () => toggleGroupInheritance(checkbox));
        });
    });
    </script>
    @endpush
    BLADE;
    }
    

    protected function renderCategoriesFormField($field, $enabledLanguages = [])
    {
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        return <<<BLADE
    @php
        // Unique variables for this field
        \$field = \$fields->{'{$field_ids_clean}'};
    
        \$field_ids_{$field_ids} = "{$field_ids}";
        \$field_name_{$field_ids} = "{$field_name}";
    
        \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'Category Field';
        \$help_texts_{$field_ids} = json_decode(\$field->help_text_json ?? '{}', true);
        \$required_{$field_ids} = \$field->is_required ?? false;
        \$maxSelectCount_{$field_ids} = \$field->max_select_count ?? null;
    
        // Selected categories
        \$selectedCategories_{$field_ids} = \$data[\$field_name_{$field_ids}]['all'] ?? [];
    @endphp
    
    <div class="w-48 flex items-start pt-3">
        <label class="block text-lg font-semibold text-gray-900">
            {{ \$label_{$field_ids} }} @if(\$required_{$field_ids})<span class="text-red-500 ml-1">*</span>@endif
        </label>
    </div>
    
    <div class="flex-1">
        <div class="relative flex items-center justify-between w-full px-4 py-3 bg-white transition-all duration-200">
            <span
                id="categorySelectButton_{{ \$field_ids_{$field_ids} }}"
                data-categoryid="{{ \$field->category_id }}"
                data-displaystyle="{{ \$field->display_style }}"
                class="category-select-button cursor-pointer text-gray-700 font-medium hover:text-blue-700 transition-colors duration-200 select-none"
            >
                选择分类
            </span>
    
            <div id="selectedCategories_{{ \$field_ids_{$field_ids} }}" class="{{ empty(\$selectedCategories_{$field_ids}) ? 'hidden' : 'flex-1 ml-4 text-lg text-gray-600 truncate' }}">
                @if(isset(\$selectedCategories_{$field_ids}) && is_array(\$selectedCategories_{$field_ids}))
                    @foreach(\$selectedCategories_{$field_ids} as \$cat_id)
                        <span class="inline-flex items-center px-3 py-1 mr-2 mb-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                            {{ \$data['categories'][\$cat_id]['name'][app()->getLocale()] ?? 'Untitled Category' }}
                            <button class="ml-2 w-4 h-4 text-blue-600 hover:text-red-600 kazcms-remove-category" data-inputid="field_{{ \$field_ids_{$field_ids} }}" data-categoryid="{{ \$cat_id }}">×</button>
                        </span>
                    @endforeach
                @endif
            </div>
    
            <input type="text" id="field_{{ \$field_ids_{$field_ids} }}" name="{{ \$field_name_{$field_ids} }}" value="{{ implode(',', \$selectedCategories_{$field_ids}) }}">
        </div>
    
        @if(\$required_{$field_ids} || \$maxSelectCount_{$field_ids})
            <div
                id="req_{{ \$field_name_{$field_ids} }}"
                class="need_require text-sm mt-1 text-red-600"
                data-required="{{ \$required_{$field_ids} ? '1' : '0' }}"
                data-maxselect="{{ \$maxSelectCount_{$field_ids} ?? '' }}"
            ></div>
        @endif
    
        @if(\$help_texts_{$field_ids}[app()->getLocale()] ?? false)
            <p class="mt-1 text-sm text-gray-500">{{ \$help_texts_{$field_ids}[app()->getLocale()] }}</p>
        @endif
    </div>
    BLADE;
    }
    
    protected function renderOptionsFormField($field, $enabledLanguages = [], )
    {
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        return <<<BLADE
    @php
        // Unique variables for this field
        \$field = \$fields->{'{$field_ids_clean}'};
    
        \$field_ids_{$field_ids} = "{$field_ids}";
        \$field_name_{$field_ids} = "{$field_name}";
    
        \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'Options Field';
        \$help_texts_{$field_ids} = json_decode(\$field->help_text_json ?? '{}', true);
        \$required_{$field_ids} = \$field->is_required ?? false;
    
        \$style_{$field_ids} = \$field->display_style;
        \$isMultiple_{$field_ids} = in_array(\$style_{$field_ids}, ['checkbox']);
        \$maxSelectCount_{$field_ids} = \$field->max_select_count ?? 0;
        \$prefixText_{$field_ids} = json_decode(\$field->prefix_text_json, true)[app()->getLocale()] ?? '';
        \$suffixText_{$field_ids} = json_decode(\$field->suffix_text_json, true)[app()->getLocale()] ?? '';
    
        // Get stored value
        \$dataValue_{$field_ids} = \$data[\$field_name_{$field_ids}]['all'] ?? [];
        \$selected_{$field_ids} = old(\$field_name_{$field_ids}, \$dataValue_{$field_ids});
        \$selected_{$field_ids} = is_array(\$selected_{$field_ids}) ? \$selected_{$field_ids} : [\$selected_{$field_ids}];
    
        // Filter and sort options
        \$options_{$field_ids} = collect(json_decode(\$field->options, true) ?? [])
            ->filter(fn(\$opt) => \$opt['enabled'] ?? false)
            ->sortBy('sort_order');
    @endphp
    
    <div class="mb-6 option-group"
         data-field-id="{{ \$field_ids_{$field_ids} }}"
         data-max-select="{{ \$maxSelectCount_{$field_ids} }}"
         data-multiple="{{ \$isMultiple_{$field_ids} ? 'true' : 'false' }}"
    >
        @if(\$label_{$field_ids})
            <label class="block text-lg font-semibold text-gray-900 mb-2">
                {{ \$label_{$field_ids} }}
                @if(\$required_{$field_ids})
                    <span class="text-red-600">*</span>
                @endif
            </label>
        @endif
    
        @if(\$isMultiple_{$field_ids} && \$maxSelectCount_{$field_ids} > 0)
            <p class="text-xs text-gray-500 mb-3">
                最多可选 {{ \$maxSelectCount_{$field_ids} }} 项
            </p>
        @endif
    
        @if(\$prefixText_{$field_ids})
            <p class="text-sm text-gray-600 mb-2">{{ \$prefixText_{$field_ids} }}</p>
        @endif
    
        @if(in_array(\$style_{$field_ids}, ['select', 'multi-select']))
            <select name="{{ \$field_name_{$field_ids} }}{{ \$isMultiple_{$field_ids} ? '[]' : '' }}"
                    id="field_select_{{ \$field_ids_{$field_ids} }}"
                    class="block w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500"
                    {{ \$isMultiple_{$field_ids} ? 'multiple' : '' }}>
                @foreach(\$options_{$field_ids} as \$option)
                    @php
                        \$optVal = \$option['value'] ?? '';
                        \$optLabel = \$option['label'][app()->getLocale()] ?? \$optVal;
                        \$selectedAttr = in_array(\$optVal, \$selected_{$field_ids}) ? 'selected' : '';
                    @endphp
                    <option value="{{ \$optVal }}" {{ \$selectedAttr }}>{{ \$optLabel }}</option>
                @endforeach
            </select>
        @else
            <div class="space-y-3">
                @foreach(\$options_{$field_ids} as \$index => \$option)
                    @php
                        \$optVal = \$option['value'] ?? '';
                        \$optLabel = \$option['label'][app()->getLocale()] ?? \$optVal;
                        \$checked = in_array(\$optVal, \$selected_{$field_ids}) ? 'checked' : '';
                        \$type = \$isMultiple_{$field_ids} ? 'checkbox' : 'radio';
                    @endphp
                    <div class="flex items-center">
                        <input type="{{ \$type }}"
                               name="{{ \$field_name_{$field_ids} }}{{ \$isMultiple_{$field_ids} ? '[]' : '' }}"
                               value="{{ \$optVal }}"
                               id="field_{{ \$field_ids_{$field_ids} }}_{{ \$loop->index }}"
                               class="option-input h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                               {{ \$checked }}
                               >
                        <label for="field_{{ \$field_ids_{$field_ids} }}_{{ \$loop->index }}"
                               class="ml-2 block text-sm text-gray-700 select-none">
                            {{ \$optLabel }}
                        </label>
                    </div>
                @endforeach
            </div>
        @endif
    
        @if(\$suffixText_{$field_ids})
            <p class="text-sm text-gray-600 mt-3">{{ \$suffixText_{$field_ids} }}</p>
        @endif
    
        @if(\$required_{$field_ids} || (\$isMultiple_{$field_ids} && \$maxSelectCount_{$field_ids} > 0))
            <div
              id="req_{{ \$field_name_{$field_ids} }}"
              class="need_require text-sm mt-1 text-red-600"
              data-required="{{ \$required_{$field_ids} ? '1' : '0' }}"
              data-maxselect="{{ \$isMultiple_{$field_ids} ? \$maxSelectCount_{$field_ids} : '' }}"
            ></div>
        @endif
    
        @error(\$field_name_{$field_ids})
            <p class="text-sm text-red-600 mt-2">{{ \$message }}</p>
        @enderror
    </div>
    BLADE;
    }
    
    protected function renderCodeFormField($field, $enabledLanguages = [])
    {
        $field_ids = "{$field['field_type_id']}_{$field['id']}";
        $field_name = "fields_{$field_ids}";
        $field_ids_clean = str_replace('_', '-', $field_ids);
    
        return <<<BLADE
    @php
        // Unique field object
        \$field = \$fields->{'{$field_ids_clean}'};
    
        \$field_ids_{$field_ids} = "{$field_ids}";
        \$field_name_{$field_ids} = "{$field_name}";
    
        // Multi-language label, description, htmlCode
        \$label_{$field_ids} = json_decode(\$field->label_json, true)[app()->getLocale()] ?? 'Code Field';
        \$description_{$field_ids} = json_decode(\$field->description_json, true)[app()->getLocale()] ?? '';
        \$htmlCode_{$field_ids} = json_decode(\$field->html_code_json, true)[app()->getLocale()] ?? '';
        \$isEnabled_{$field_ids} = \$field->is_enabled ?? true;
    @endphp
    
    @if(\$isEnabled_{$field_ids})
        <div class="mb-6">
            {{-- Label --}}
            @if(\$label_{$field_ids})
            <div class="w-48 flex items-start pt-3">
                <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
                    {{ \$label_{$field_ids} }}
                </label>
            </div>
            @endif
    
            {{-- Description --}}
            @if(\$description_{$field_ids})
                <p class="text-sm text-gray-500 mb-2">{{ \$description_{$field_ids} }}</p>
            @endif
    
            {{-- Render HTML Code --}}
            @if(\$htmlCode_{$field_ids})
                <div class="prose max-w-full text-sm text-gray-700">
                    {!! \$htmlCode_{$field_ids} !!}
                </div>
            @else
                <p class="text-sm text-red-400 italic">[No code available for current language]</p>
            @endif
        </div>
    @endif
    BLADE;
    }
    

    
    


}
