<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\Fields\FieldText;
use App\Models\Fields\FieldData;

class FieldService
{
    /**
     * Supported field types: category, code, file, options, text, textarea
     */
    protected array $fieldTypeTables = [
        'text',
        'textarea',
        'file',
        'categories',
        'options',
        'codes',
    ];

    /**
     * Get filtered data for a list of field definitions.
     *
     * @param array $fieldList Format: [
     *     [$contentTypeId(int), $fieldType(int), [
     *         'columns' => [[$column, $value], ...],
     *         'logic' => 'and'|'or'
     *     ]],
     *     ...
     * ]
     * @return array Returns data grouped by "contentTypeId-fieldType"
     */
    public function getFilteredFieldData(array $fieldList): array
    {
        $allData = [];

        foreach ($fieldList as $field) {
            [$contentTypeId, $fieldType, $filter] = $field;

            // Validate field type
            if ($fieldType < 1 || $fieldType > 6) {
                continue; // Skip invalid field type
            }

            $tableName = 'field_' . $this->fieldTypeTables[$fieldType - 1];

            // Start building query
            $query = DB::table($tableName)
                ->where('content_type_id', $contentTypeId);

            // Apply column conditions
            $logic = strtolower($filter['logic'] ?? 'and');
            $columns = $filter['columns'] ?? [];

            $query->where(function ($q) use ($columns, $logic) {
                foreach ($columns as [$column, $value]) {
                    if ($logic === 'or') {
                        $q->orWhere($column, $value);
                    } else {
                        $q->where($column, $value);
                    }
                }
            });

            // Execute query and store results
            $key = "$contentTypeId-$fieldType";
            $allData[$key] = $query->get();
        }

        return $allData;
    }



    public function getSeoVariableFieldWithData(int $content_type_id, string $seoType = 'slug', string $lang, string $value): array
    {
        // Step 1: Find fields with the given seo_type for the given content_type_id
        $fields = FieldText::where('content_type_id', $content_type_id)
            ->where('seo_type', $seoType)
            ->get();

        $count = $fields->count();

        // Step 2: Validate the number of matching fields
        if ($count > 1) {
            throw new \Exception("Error: Found {$count} fields with seo_type = '{$seoType}' for content_type_id = {$content_type_id}");
        }

        if ($count < 1) {
            throw new \Exception("Error: No field found with seo_type = '{$seoType}' for content_type_id = {$content_type_id}");
        }

        $field = $fields->first();
        // Step 3: Optionally retrieve the associated FieldData if a data_id is provided
        $data = null;
        if ($value) {
            $data = FieldData::where('content_type_id', $content_type_id)
                ->where('field_type_id', 1)
                ->where('field_id', $field->id)
                ->whereRaw(
                    "JSON_UNQUOTE(JSON_EXTRACT(data, '$.\"{$lang}\"')) = ?",
                    [$value]
                )
                ->first();
        }
        // Step 4: Return the result as an array
        return [
            'field' => $field,
            'data'  => $data
        ];
    }

    public function getFieldsByContentType(
        int $contentTypeId,
        string $locale = null,
        array $mapTablesAndFields = [] // 新的第三个参数
    ): array {
        $locale = $locale ?? app()->getLocale();
        $result = [];
    
        // 遍历要处理的表
        $tablesToProcess = !empty($mapTablesAndFields) ? array_keys($mapTablesAndFields) : $this->fieldTypeTables;
    
        foreach ($tablesToProcess as $table) {
            if (!in_array($table, $this->fieldTypeTables)) {
                continue; // 跳过不存在的表
            }
    
            $fields = DB::table("field_{$table}")
                ->where('content_type_id', $contentTypeId)
                ->get();
    
            foreach ($fields as $field) {
                $key = "{$contentTypeId}-{$field->field_type_id}-{$field->id}";
    
                $row = [];

                if (!empty($mapTablesAndFields[$table])) {
                    foreach ($mapTablesAndFields[$table] as $jsonKey => $alias) {
                        $value = null;
                
                        if (str_contains($jsonKey, '.')) {
                            $keys = explode('.', $jsonKey);
                            $valueTmp = $field;
                
                            foreach ($keys as $k) {
                                $valueTmp = is_object($valueTmp) ? ($valueTmp->$k ?? null) : ($valueTmp[$k] ?? null);
                            }
                
                            // 如果是 JSON 字符串，decode
                            if (is_string($valueTmp)) {
                                $decoded = json_decode($valueTmp, true);
                                $value = is_array($decoded) ? ($decoded[$locale] ?? $decoded['en-us'] ?? '') : $valueTmp;
                            } else {
                                $value = $valueTmp;
                            }
                        } else {
                            $valueTmp = $field->$jsonKey ?? null;
                            if (is_string($valueTmp)) {
                                $decoded = json_decode($valueTmp, true);
                                $value = is_array($decoded) ? ($decoded[$locale] ?? $decoded['en-us'] ?? '') : $valueTmp;
                            } else {
                                $value = $valueTmp;
                            }
                        }
                
                        $row[$alias] = $value;
                    }
                } else {
                    // 默认返回完整信息
                    $labelArray = json_decode($field->label_json ?? '{}', true);
                    $row = [
                        'id' => $field->id,
                        'content_type_id' => $contentTypeId,
                        'field_type' => $table,
                        'label' => is_array($labelArray) ? ($labelArray[$locale] ?? $labelArray['en-us'] ?? '') : ($field->name ?? ''),
                    ];
                }
                
    
                $result[$key] = $row;
            }
        }
    
        return $result;
    }
    
}
