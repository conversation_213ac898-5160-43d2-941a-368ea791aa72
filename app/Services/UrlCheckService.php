<?php
namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\ReservedUrl;

class UrlCheckService
{
    /**
     * Check if a URL already exists
     *
     * @param string $url Example: "en-us/some-slug/other-segment"
     * @param bool $isMultilang Whether the site uses multiple languages
     * @param string|null $defaultLang System default language if not multilang
     * @return bool true if exists, false otherwise
     */
  /**
 * Check if given URL or slugs already exist.
 *
 * @param array|string $slugs  单个 slug 字符串 或 多语言数组 ['en-us'=>'blog','zh-cn'=>'boke']
 * @param bool $isMultilang   是否多语言
 * @return array              返回每个语言是否存在，示例 ['en-us'=>false, 'zh-cn'=>true]
 */
public function urlExists(array|string $slugs, bool $isMultilang = true): array
{
    $defaultLang = config('app.locale', 'en-us');
    $results = [];

    if (is_string($slugs)) {
        // 单个 URL 字符串，解析语言和 slug
        $segments = explode('/', trim($slugs, '/'));
        if ($isMultilang) {
            $lang = $segments[0] ?? $defaultLang;
            $slug = $segments[1] ?? null;
        } else {
            $lang = $defaultLang;
            $slug = $segments[0] ?? null;
        }

        $slugs = [$lang => $slug];
    }

    foreach ($slugs as $lang => $slug) {
        if (!$slug) {
            $results[$lang] = false;
            continue;
        }

        // 1. 检查 content_types
        $existsInContentTypes = DB::table('content_types')
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(slug, '$.\"{$lang}\"')) = ?", [$slug])
            ->exists();

        if ($existsInContentTypes) {
            $results[$lang] = true;
            continue;
        }

        // 2. 检查 reserved_urls 表
        $existsInReserved = ReservedUrl::where('url', "/{$slug}")->exists();
        $results[$lang] = $existsInReserved;
    }

    return $results; // 每个语言的存在情况
}

    
}
