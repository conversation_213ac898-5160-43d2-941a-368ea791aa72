<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Admin\AttachmentController;

class ContentDataService
{
    /**
     * 保存或更新字段草稿
     */
    public function saveOrUpdateFields(Request $request): int
    {
       // dd($request->all());
        $userId = auth()->id() ?? 0;
        $userAction = $request->input('user_action', 'draft');
        $dataId = (int)$request->input('data_id', 0);
        $contentTypeId = (int)$request->input('content_type_id');
        $languages = getActiveLanguages()->pluck('code')->toArray();
        $locale = app()->getLocale();
        $now = now();

        // 新建内容：插入主表占位
        if ($dataId === 0) {
            $dataId = DB::table('field_data')->insertGetId([
                'user_id' => $userId,
                'content_type_id' => $contentTypeId,
                'field_id' => 0,
                'data_id' => 0,
                'field_type_id' => 0,
                'data' => '{}',
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }

        // 处理字段数据
        $fieldPayloads = [];

        foreach ($request->all() as $key => $value) {
            if (!Str::startsWith($key, 'fields_') || !preg_match('/^fields_(\d+)_(\d+)/', $key, $matches)) {
                continue;
            }

            $fieldTypeId = (int)$matches[1];
            $fieldId = (int)$matches[2];
            $fieldName = "fields_{$fieldTypeId}_{$fieldId}";
            $sharedFlag = $request->boolean("shared_{$fieldName}");
            $primaryLang = $locale;

            $langValues = [];

            if ($fieldTypeId === 3) { // 图片上传字段
                $uploadedFiles = (array)($value[$primaryLang] ?? []);
                $savedIds = [];

                foreach ($uploadedFiles as $file) {
                    if ($file instanceof \Illuminate\Http\UploadedFile) {
                        $attachCtrl = new AttachmentController();
                        if ($aid = $attachCtrl->handleFileUploadAndSave($file, $userId)) {
                            $savedIds[] = $aid;
                        }
                    }
                }

                $langValues[$primaryLang] = $savedIds;

                if ($sharedFlag) {
                    foreach ($languages as $lg) {
                        $langValues[$lg] = $savedIds;
                    }
                } else {
                    foreach ($languages as $lg) {
                        if ($lg === $primaryLang) continue;
                        $langValues[$lg] = (array)($value[$lg] ?? []);
                    }
                }
            } elseif ($fieldTypeId === 4 || $fieldTypeId === 5) {
                $val = $fieldTypeId === 4
                    ? array_filter(array_map('intval', explode(',', (string)$value)))
                    : (is_array($value) ? array_values($value) : (string)$value);

                $langValues = ['all' => $val];
            } else {
                foreach ($languages as $lg) {
                    $langValues[$lg] = $value[$lg] ?? '';
                }
            }
            // 判断是否为空，跳过空字段
            $isEmpty = true;

            foreach ($langValues as $val) {
                if (is_array($val)) {
                    $val = array_filter($val, fn($v) => $v !== null && $v !== '');
                    if (!empty($val)) {
                        $isEmpty = false;
                        break;
                    }
                } elseif ($val !== null && $val !== '') {
                    $isEmpty = false;
                    break;
                }
            }

            if ($isEmpty) {
                continue; // 跳过这个字段，不保存
            }

            $fieldPayloads[] = [
                'field_id' => $fieldId,
                'field_type_id' => $fieldTypeId,
                'content_type_id' => $contentTypeId,
                'data' => $langValues,
            ];
        }

        // 保存为草稿版本
        $version = $this->saveFieldsAsDraft($dataId, $contentTypeId, $userId, $fieldPayloads);
        if($userAction === 'publish'){
            $this->publishFields($dataId, $version, $userId);
        }
        return $dataId;
    }

    /**
     * 保存字段草稿版本
     */
    public function saveFieldsAsDraft(
        int $dataId,
        int $contentTypeId,
        int $userId,
        array $fieldPayloads,
        string|Carbon|null $timestamp = null
    ): int {
        $timestamp = $timestamp ? Carbon::parse($timestamp) : now();
        $version = $this->getNextVersion($dataId);
        foreach ($fieldPayloads as $item) {
            //foreach ($item['data'] as $lang => $val) {
                DB::table('field_data_v')->insert([
                    'data_id' => $dataId,
                    'content_type_id' => $contentTypeId,
                    'field_id' => $item['field_id'],
                    'field_type_id' => $item['field_type_id'],
                    'data' => json_encode($item['data'], JSON_UNESCAPED_UNICODE),
                    'version' => $version,
                    'status' => 'draft',
                    'is_current' => 1,
                    'changed_by' => $userId,
                    'changed_at' => $timestamp,
                ]);
            //}
        }

        return $version;
    }

    /**
     * 发布草稿版本字段为正式内容
     */
    public function publishFields(int $dataId, int $version, int $userId, array $fieldIds = []): void
    {
        DB::transaction(function () use ($dataId, $version, $userId, $fieldIds) {
            $query = DB::table('field_data_v')
                ->where('data_id', $dataId)
                ->where('version', $version)
                ->where('status', 'draft');

            if (!empty($fieldIds)) {
                $query->whereIn('field_id', $fieldIds);
            }

            $drafts = $query->get();
            // 删除已发布字段（主表中每个 field 仅有一条）
            DB::table('field_data')->where('data_id', $dataId)->delete();
            foreach ($drafts as $draft) {
               

                DB::table('field_data')->insert([
                    'data_id' => $dataId,
                    'user_id' => $userId,
                    'content_type_id' => $draft->content_type_id,
                    'field_id' => $draft->field_id,
                    'field_type_id' => $draft->field_type_id,
                    'data' => $draft->data,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // 更新版本表：旧 is_current 清除、新版本设为当前并标记为发布
            DB::table('field_data_v')
                ->where('data_id', $dataId)
                ->update(['is_current' => 0]);

            DB::table('field_data_v')
                ->where('data_id', $dataId)
                ->where('version', $version)
                ->update([
                    'status' => 'published',
                    'is_current' => 1,
                    'changed_by' => $userId,
                    'changed_at' => now(),
                ]);


        });
    }

    /**
     * 获取下一个版本号
     */
    public function getNextVersion(int $dataId): int
    {
        $max = DB::table('field_data_v')
            ->where('data_id', $dataId)
            ->max('version');

        return $max ? $max + 1 : 1;
    }

    /**
     * 回滚到某个已发布版本
     */
    public function revertToVersion(int $dataId, int $version, int $userId): void
    {
        DB::transaction(function () use ($dataId, $version, $userId) {
            DB::table('field_data_v')
                ->where('data_id', $dataId)
                ->update(['is_current' => 0]);

            DB::table('field_data_v')
                ->where('data_id', $dataId)
                ->where('version', $version)
                ->where('status', 'published')
                ->update([
                    'is_current' => 1,
                    'changed_by' => $userId,
                    'changed_at' => now()
                ]);

            DB::table('content')
                ->where('id', $dataId)
                ->update(['updated_at' => now()]);
        });
    }

    /**
     * 清理旧草稿
     */
    public function cleanupOldDrafts(int $days = 90): int
    {
        return DB::table('field_data_v')
            ->where('status', 'draft')
            ->where('changed_at', '<', now()->subDays($days))
            ->delete();
    }


public function del_getFieldDataBySlug(int $contentTypeId, string $lang, string $slug): ?\stdClass
{
    // 查找符合条件的记录
    $fieldSlug = DB::table('field_text')
        ->where('content_type_id', $contentTypeId)
        ->where("seo_type", 'slug')
        ->get();
    if(count($fieldSlug) != 1){
        
    }

    $record = DB::table('field_data')
        ->where('content_type_id', $contentTypeId)
        ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(data, '$.\"$lang\"')) = ?", [$slug])
        ->first();

    if (!$record) {
        return null;
    }

    // 解析 JSON 字段，返回指定语言的值
    return $record ?? null;
}

}
