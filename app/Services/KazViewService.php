<?php

namespace App\Services;

use App\Models\KazView;
use App\Repositories\View\ViewRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;


class KazViewService
{
    protected ViewRepository $repository;

    public function __construct(ViewRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 保存或更新 KazView
     */
    public function saveView(array $data, ?int $id = null): KazView
    {
        // Validation
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'content_type_id' => 'required|integer',
            'config' => 'required|array',
            'permissions' => 'nullable|array',
            'status' => 'required|boolean',
        ]);
        if ($validator->fails()) {
            throw new \InvalidArgumentException($validator->errors()->first());
        }

        // 如果是新建，补充 user_id
        if (!$id) {
            $data['user_id'] = Auth::id();
        }

        return $this->repository->save($data, $id);
    }

    /**
     * 删除 KazView
     */
    public function deleteView(int $id): void
    {
        $view = KazView::findOrFail($id); // 如果找不到会抛 404
        $view->delete();
    }

    /**
     * 切换启用 / 禁用状态
     */
    public function del_toggleStatus(int $id): KazView
    {
        $view = $this->repository->find($id);
        $view->status = !$view->status;
        $this->repository->save($view->toArray(), $view->id);

        return $view;
    }

    /**
     * 获取用户的所有 KazView
     */
    public function del_getUserViews(int $userId)
    {
        return $this->repository->getByUser($userId);
    }

    /**
     * 获取单个 KazView
     */
    public function getView(int $id): KazView
    {
        return $this->repository->find($id);
    }


    function buildParamsFromConfig(array $config, int $contentTypeId): array
    {
        $params = [
            'content_type_id' => $contentTypeId,
            'page'    => request('page', 1),
            'perpage' => $config['pagination']['per_page'] ?? 20,
        ];

        // Sorting
        if (!empty($config['sort'])) {
            $field = $config['sort']['field'];
            $dir   = $config['sort']['direction'] ?? 'asc';

            // 判断是系统字段还是自定义 field_id
            if (is_numeric($field)) {
                // 自定义字段 ID
                $params['order'] = [
                    ['field_id' => $field, 'direction' => $dir]
                ];
            } else {
                // 系统字段
                $params['order'] = [
                    [$field => $dir]
                ];
            }
        }


        // Filters
        if (!empty($config['filters'])) {
            $filters = [];
            foreach ($config['filters'] as $fieldId => $rules) {
                foreach ($rules as $rule) {
                    $filters[] = [
                        'field'    => $rule['fieldId'],
                        'operator' => $rule['operator'],
                        'value'    => $rule['value'],
                        'logic'    => $rule['logic'] ?? 'AND',
                    ];
                }
            }
            $params['filters'] = $filters;
        }

        // Date filter → 转成 getList 支持的 date 二维数组
        if (!empty($config['date_filter'])) {
            $dateFilter = $config['date_filter'];
            if (isset($dateFilter['mode'])) {
                if ($dateFilter['mode'] === 'range' && !empty($dateFilter['from']) && !empty($dateFilter['to'])) {
                    $params['date'] = [
                        'mode' => 'range',
                        'from' => $dateFilter['from'],
                        'to'   => $dateFilter['to'],
                    ];
                } elseif ($dateFilter['mode'] === 'last_days' && !empty($dateFilter['days'])) {
                    $params['date'] = [
                        'mode' => 'last_days',
                        'days' => $dateFilter['days'],
                    ];
                } else {
                    // 向后兼容，直接传整个 date_filter
                    $params['date'] = $dateFilter;
                }
            }
        }

        // Data IDs
        if (!empty($config['data_id_list'])) {
            $params['ids'] = $config['data_id_list'];
        }

        // Selected fields
        if (!empty($config['selected_fields'])) {
            $params['fields_list'] = $config['selected_fields'];
        }

        return $params;
    }

    /**
     * Get KazView model either by content placeholder or by ID directly
     *
     * @param string|int|null $contentOrId  Can be content string or direct ID
     * @return \App\Models\KazView|null
     */
    public function processKazViewConfig($contentOrId)
    {
        $viewId = null;

        // If an integer is passed, treat it as the ID
        if (is_numeric($contentOrId)) {
            $viewId = (int) $contentOrId;
        }
        // If a string is passed, try to extract the ID from content
        elseif (is_string($contentOrId)) {
            preg_match('/\{\-\-\s*kazview:(\d+)\s*\-\-\}/', $contentOrId, $matches);
            if (!empty($matches[1])) {
                $viewId = (int)$matches[1];
            }
        }

        if ($viewId) {
            return \App\Models\KazView::find($viewId);
        }

        return null;
    }
}
