<?php
namespace App\Services;

use App\Repositories\ContentRepository;
use App\Models\Fields\FieldsOrder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\ContentType;
use App\Exceptions\MatchException;

class ContentListService
{
    protected ContentRepository $repo;
    protected FieldService $fieldService;

    public function __construct(ContentRepository $repo, FieldService $fieldService)
    {
        $this->repo = $repo;
        $this->fieldService = $fieldService;
    }


    public function del_getList(array $params): array
    {
        /* ---------- 0. 解析参数 ---------- */
        $contentTypeId = $params['content_type_id'];                 // 必填
        $fieldsList    = $params['fields_list']   ?? [];             // 可选
        $userIds       = $params['user_ids']      ?? [];             // 可选
        $langKw        = $params['langKw']        ?? null;           // 可选
        $orderRule     = $params['order']         ?? [];             // 可选
        $per           = min((int)($params['perpage'] ?? 20), 100); // 默认为20，最大100
        $page          = max((int)($params['page'] ?? 1), 1);
        $returnFields  = $params['return_fields'] ?? ['*'];
        $extrasDetail  = $params['extras'] ?? ['attachments' => false, 'categories' => false];
        $status        = $params['status'] ?? 'published';
        $fieldTypeFieldIdList = $params['field_type_field_id_list'] ?? [];
        $date = $params['date'] ?? null;

        $lang = app()->getLocale();
    
        // 支持日期参数（二维数组）
    
        /* ---------- 1. 分页出 data_id（以内容为单位） ---------- */
        $dataIdQuery = $status === 'published'
            ? DB::table('field_data')
            : DB::table('field_data_v')->where('status', $status);
    
        $dataIdQuery = $dataIdQuery->where('data_id','>', 0)
            ->where('content_type_id', $contentTypeId)
            ->when($userIds, fn($q) => $status === 'published'
                ? $this->repo->applyUserFilter($q, $userIds)
                : $this->repo->applyChangedByFilter($q, $userIds))
            ->when($fieldsList, fn($q) => $this->repo->applyFieldFilters($q, $fieldsList))
            ->when($langKw, fn($q) => $this->repo->applyKeywordSearch($q, $langKw));
    
        // ---------- 日期筛选 ----------
        if ($date && is_array($date)) {
            $mode = $date['mode'] ?? null;
            if ($mode === 'range' && !empty($date['from']) && !empty($date['to'])) {
                $dataIdQuery->whereBetween('created_at', [$date['from'], $date['to']]);
            } elseif ($mode === 'last_days' && !empty($date['days'])) {
                $dataIdQuery->where('created_at', '>=', now()->subDays((int)$date['days']));
            }
        }
    
        $dataIdQuery = $dataIdQuery->select('data_id')->distinct();
    
        // 排序字段优先级：用户传入 > 默认按 data_id 降序
        if (!empty($orderRule)) {
            //do it later
            //$dataIdQuery = $this->repo->applyOrdering($dataIdQuery, $orderRule);
        } else {
            $dataIdQuery->orderByDesc('data_id');
        }
    
        // 统计唯一 data_id 总数
        $subQuery = clone $dataIdQuery;
        $subQuery->orders = null; // 清除 orderBy
        
        $total = DB::table(DB::raw("({$subQuery->toSql()}) as sub"))
            ->mergeBindings($subQuery)
            ->count();

    
        // 获取当前页 data_id
        $pageDataIds = (clone $dataIdQuery)
            ->orderByDesc('data_id')
            ->forPage($page, $per)
            ->pluck('data_id')
            ->toArray();
    
        // 构造 paginator
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            collect($pageDataIds),
            $total,
            $per,
            $page,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );
    
        $dataIds = $pageDataIds;
    
        if (empty($dataIds)) {
            return [
                'data' => [],
                'meta' => ['page' => $page, 'perpage' => $per],
                'paginator' => $paginator,
                'extras' => ['attachments' => [], 'categories' => []],
                'has_drafts' => [],
            ];
        }
    
        /* ---------- 2. 获取字段行 ---------- */
        $fieldQuery = $status === 'published'
            ? DB::table('field_data')
            : DB::table('field_data_v')->where('status', $status);
    
        // 获取 slug 字段
        $slugFieldRows = $this->fieldService->getFilteredFieldData([
            [$contentTypeId, 1, [
                'columns' => [['seo_type', 'slug']], 
                'logic' => 'and'
            ]]
        ]);
        if(count($slugFieldRows["{$contentTypeId}-1"] ?? []) != 1){
            throw new MatchException('Field category match error', 102, [
                'field_label' => '',
                'matched_count' => count($slugFieldRows["{$contentTypeId}-1"]),
                'table' => 'field_text',
                'lang' => $lang,
                'contentTypeId' => $contentTypeId,
                'current_url' => url()->full(),
            ]);
        }
    
        $slugFieldIdList = (
            $slugFieldRows["{$contentTypeId}-1"] ?? collect()
        )
            ->map(fn($item) => [$item->field_type_id, $item->id])
            ->unique()
            ->values()
            ->all();
    
        $fieldRows = $fieldQuery
            ->where('content_type_id', $contentTypeId)
            ->whereIn('data_id', $dataIds)
            ->when($slugFieldIdList, fn($q, $list) =>
                $this->repo->applyFieldTypeAndIdPairs($q, $list)
            )
            ->get();
    
        /* ---------- 3. 加载 has_drafts 信息 ---------- */
        $hasDrafts = [];
        if ($status === 'published') {
            $hasDrafts = $this->repo->getDraftStatusForDataIds($dataIds);
        }
    
        /* ---------- 4. 加载附件和分类 ---------- */
        $extras = ['attachments' => [], 'categories' => []];
        if ($extrasDetail['attachments'] || $extrasDetail['categories']) {
            $extras = $this->repo->loadExtras($fieldRows, $extrasDetail);
        }
    
        /* ---------- 5. 字段排序并分组 ---------- */
        $orders = FieldsOrder::where('content_type_id', $contentTypeId)
            ->orderBy('sort_order')
            ->get(['field_type_id', 'field_id', 'sort_order'])
            ->mapWithKeys(fn($r) => ["{$r->field_type_id}-{$r->field_id}" => $r->sort_order])
            ->toArray();
    
        $grouped = [];
        foreach ($fieldRows as $row) {
            $dataId = $row->data_id;
            $fieldKey = "{$row->field_type_id}-{$row->field_id}";
            $grouped[$dataId][$fieldKey] = $row;
        }
    
        foreach ($grouped as &$fields) {
            uksort($fields, fn($a, $b) => ($orders[$a] ?? PHP_INT_MAX) <=> ($orders[$b] ?? PHP_INT_MAX));
        }
        unset($fields);
    
        /* ---------- 6. 返回结构 ---------- */
        return [
            'data' => $grouped,
            'slugFieldIdList' => $slugFieldIdList,
            'meta' => [
                'page'    => $page,
                'perpage' => $per,
            ],
            'paginator' => $paginator,
            'extras' => $extras,
            'has_drafts' => $hasDrafts,
        ];
    }

    public function getList(array $params): array
    {
        /* ---------- 0. Parse Parameters ---------- */
        $mode          = $params['mode'] ?? 'list'; // list | detail | document | form
        $contentTypeId = $params['content_type_id'] ?? null;
        $fieldsList    = $params['fields_list']   ?? [];
        $userIds       = $params['user_ids']      ?? [];
        $langKw        = $params['langKw']        ?? null;
        $orderRule     = $params['order']         ?? [];
        $dataIdFilter  = $params['data_ids']      ?? []; // 指定data_id
        $dateFilter    = $params['date']          ?? null;
        $extrasDetail  = $params['extras']        ?? ['attachments' => false, 'categories' => false];
        $status        = $params['status']        ?? 'published';
    
        $per           = min((int)($params['perpage'] ?? 20), 100);
        $page          = max((int)($params['page'] ?? 1), 1);
    
        if (!$contentTypeId) {
            throw new \InvalidArgumentException("content_type_id is required");
        }
    
        /* ---------- 1. Build Query for data_id (Filtering only) ---------- */
        $dataIdQuery = $status === 'published'
            ? DB::table('field_data')
            : DB::table('field_data_v')->where('status', $status);
    
        $dataIdQuery = $dataIdQuery
            ->where('content_type_id', $contentTypeId)
            ->where('data_id', '>', 0);
    
        // User filter
        $dataIdQuery = !empty($userIds)
            ? ($status === 'published'
                ? $this->repo->applyUserFilter($dataIdQuery, $userIds)
                : $this->repo->applyChangedByFilter($dataIdQuery, $userIds))
            : $dataIdQuery;
    
        // Keyword filter
        $dataIdQuery = $this->repo->applyKeywordSearch($dataIdQuery, $langKw);
    
        // Field filters
        if (!empty($fieldsList)) {
            $dataIdQuery = $this->repo->applyFieldFilters($dataIdQuery, $fieldsList);
        }
    
        // Data ID filter
        if (!empty($dataIdFilter)) {
            $dataIdQuery->whereIn('data_id', (array)$dataIdFilter);
        }
    
        // Date filter
        if ($dateFilter && is_array($dateFilter)) {
            $dataIdQuery = $this->repo->applyDateFilter($dataIdQuery, $dateFilter, 'created_at');
        }
    
        $dataIdQuery->select('data_id')->distinct();
    
        // Count total before pagination
        $subQuery = clone $dataIdQuery;
        $total = DB::table(DB::raw("({$subQuery->toSql()}) as sub"))
            ->mergeBindings($subQuery)
            ->count();
    
        /* ---------- 2. Pagination (default order by data_id desc) ---------- */
        if ($mode === 'detail') {
            $dataIds = (array)$dataIdFilter;
        } else {
            $pageDataIds = (clone $dataIdQuery)
                ->orderByDesc('data_id')
                ->forPage($page, $per)
                ->pluck('data_id')
                ->toArray();
            $dataIds = array_unique($pageDataIds);
        }
    
        if (empty($dataIds)) {
            return [
                'data'       => [],
                'meta'       => ['page' => $page, 'perpage' => $per],
                'paginator'  => null,
                'extras'     => ['attachments' => [], 'categories' => []],
                'has_drafts' => [],
            ];
        }
    
        // Build paginator only for list mode
        $paginator = null;
        if ($mode === 'list') {
            $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                collect($dataIds),
                $total,
                $per,
                $page,
                [
                    'path'  => request()->url(),
                    'query' => request()->query(),
                ]
            );
        }
    
        /* ---------- 3. Fetch field rows ---------- */
        $fieldQuery = $status === 'published'
            ? DB::table('field_data')
            : DB::table('field_data_v')->where('status', $status);
    
        $fieldQuery = $fieldQuery
            ->where('content_type_id', $contentTypeId)
            ->whereIn('data_id', $dataIds);
    
        // Restrict fields by fields_list if provided
        if (!empty($fieldsList)) {
            $pairs = collect($fieldsList)
                ->map(function ($item) {
                    $parts = explode('-', $item);
                    return count($parts) === 3 ? [(int)$parts[1], (int)$parts[2]] : null;
                })
                ->filter()
                ->toArray();
                //dd( $pairs);
            $fieldQuery = $this->repo->applyFieldTypeAndIdPairs($fieldQuery, $pairs);
            //echo $fieldQuery->toSql();
            //dd($fieldQuery->getBindings());
        }
    
        $fieldRows = $fieldQuery->get();
    
        /* ---------- 4. Draft status ---------- */
        $hasDrafts = $status === 'published'
            ? $this->repo->getDraftStatusForDataIds($dataIds)
            : [];
    
        /* ---------- 5. Extras ---------- */
        $extras = (!empty($extrasDetail['attachments']) || !empty($extrasDetail['categories']))
            ? $this->repo->loadExtras($fieldRows, $extrasDetail)
            : ['attachments' => [], 'categories' => []];
    
        /* ---------- 6. Group fields by data_id ---------- */
        $grouped = [];
        foreach ($fieldRows as $row) {
            $fieldKey = "{$row->field_type_id}-{$row->field_id}";
            $grouped[$row->data_id][$fieldKey] = $row;
        }
    
        /* ---------- 7. Apply ordering AFTER field fetch ---------- */
        if (!empty($orderRule)) {
            //$grouped = $this->applyFieldBasedOrdering($grouped, $orderRule);
        }
    
        /* ---------- 8. Return ---------- */
        return [
            'data'       => $grouped,
            'meta'       => ['page' => $page, 'perpage' => $per],
            'paginator'  => $paginator,
            'extras'     => $extras,
            'has_drafts' => $hasDrafts,
        ];
    }
    

    

    function getContentTypeByLangAndSlug($lang, $slugValue)
    {
        return ContentType::where("slug->{$lang}", $slugValue)->first();
    }

    public function getListByConfig(array $config, int $contentTypeId, ?int $page = null): array
{
    // 使用之前写好的 KazViewService helper
    $params = app(KazViewService::class)->buildParamsFromConfig($config, $contentTypeId);

    // 如果外部传入 page，覆盖 config 内的 page
    if ($page) {
        $params['page'] = $page;
    }

    return $this->getList($params);
}

    
}
