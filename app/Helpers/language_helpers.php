<?php

use Illuminate\Support\Facades\Cache;
use App\Models\KazcmsLanguage;

if (!function_exists('getActiveLanguages')) {
    /**
     * Retrieve the list of active languages, cached for 60 minutes.
     *
     * @return \Illuminate\Support\Collection
     */
    function getActiveLanguages()
    {
        return Cache::remember('kazcms_languages_active', 60, function () {
            return KazcmsLanguage::where('enabled', true)
                ->orderBy('sort_order')
                ->get()
                ->keyBy('code');  // 以 code 字段作为 key 返回
        });
    }
}

if (!function_exists('getDefaultLanguage')) {
    /**
     * Retrieve the default language from the active list.
     * If no default is set, return the first by sort order.
     *
     * @return KazcmsLanguage|null
     */
    function getDefaultLanguage()
    {
        return getActiveLanguages()->firstWhere('is_default', true)
            ?? getActiveLanguages()->first();
    }
}

if (!function_exists('isValidLocale')) {
    /**
     * Check whether the given language code is supported.
     *
     * @param string $langCode
     * @return bool
     */
    function isValidLocale(string $langCode): bool
    {
        return getActiveLanguages()->pluck('code')->contains($langCode);
    }
}

if (!function_exists('localized_route')) {
    /**
     * Generate a route URL, optionally prefixed with the current locale if multilingual.
     *
     * @param string $name   The name of the route.
     * @param array  $params Optional parameters to pass to the route.
     * @return string
     */
    function localized_route(string $name, array $params = [])
    {
        if (is_multilang_site()) {
            $params['lang'] = app()->getLocale();
        }

        return route($name, $params);
    }

    if (!function_exists('localized_current_url')) {
        function localized_current_url(string $langCode, array $localizedPaths = []): string
        {
            $request = request();
            $uri = $request->path();
            $segments = explode('/', $uri);
    
            // 移除当前URL的语言段
            if (isValidLocale($segments[0] ?? '')) {
                array_shift($segments);
            }
    
            $path = '';
            $queryString = '';
    
            foreach ($localizedPaths as $index => $map) {
                if (!is_array($map)) continue;
    
                $value = $map[$langCode] ?? '';
                $value = trim($value);
    
                if ($value === '') continue;
    
                if ($index === 0) {
                    // 第一项作为路径，去除开头的斜杠
                    $path = ltrim($value, '/');
                } else {
                    // 其他项，如果以 ? 或 & 开头，拼接到查询字符串后面
                    if (str_starts_with($value, '?') || str_starts_with($value, '&')) {
                        // 如果已有query字符串，且新值以?开头，替换?为&
                        if ($queryString !== '' && str_starts_with($value, '?')) {
                            $value = '&' . substr($value, 1);
                        }
                        $queryString .= $value;
                    } else {
                        // 不是查询字符串的，作为路径继续拼接（用斜杠分隔）
                        $path = rtrim($path, '/') . '/' . ltrim($value, '/');
                    }
                }
            }
    
            if ($path === '') {
                // 路径空则用当前URL剩余部分
                $path = implode('/', $segments);
            }
    
            $fullPath = $langCode . '/' . $path . $queryString;
    
            return url($fullPath);
        }
    }
    
    



    if (!function_exists('getDirection')) {
        function getDirection($locale = null): string
        {
            $locale = $locale ?: app()->getLocale();


            $lang = strtolower(explode('-', $locale)[0]);

            $rtlLanguages = ['ar', 'he', 'fa', 'ur']; // 常见RTL语言

            return in_array($lang, $rtlLanguages) ? 'rtl' : 'ltr';
        }
    }
}
