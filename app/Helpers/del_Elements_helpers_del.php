<?php

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

if (!function_exists('kazcms_get_elements_cache')) {
    /**
     * Get all elements from cache or DB.
     *
     * @return \Illuminate\Support\Collection
     */
    function kazcms_get_elements_cache()
    {
        return Cache::rememberForever('kazcms_elements_all', function () {
            return DB::table('elements')->get();
        });
    }
}

if (!function_exists('kazcms_refresh_elements_cache')) {
    /**
     * Refresh elements cache after any update.
     *
     * @return void
     */
    function kazcms_refresh_elements_cache()
    {
        Cache::forget('kazcms_elements_all');
        kazcms_get_elements_cache(); // regenerate cache immediately
    }
}
