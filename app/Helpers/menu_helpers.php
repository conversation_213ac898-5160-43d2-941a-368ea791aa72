<?php

use Illuminate\Support\Facades\Cache;
use App\Models\Menu;


if (!function_exists('kaz_menus')) {
    function kaz_menus(string $type, ?string $locale = null, string $position = 'admin.left')
    {
        $locale = $locale ?? app()->getLocale();
    
        return Cache::remember("menus_{$type}_{$position}_{$locale}", 60, function () use ($type, $locale, $position) {
            $menus = Menu::where('type', $type)
                ->where('position', $position)
                ->where('is_active', 1)
                ->orderBy('order_index')
                ->get();
    
            $menus = $menus->map(function ($menu) use ($locale) {
                $names = is_array($menu->name) ? $menu->name : json_decode($menu->name, true);
                $menu->display_name = $names[$locale] ?? reset($names);
                return $menu;
            });
    
            return build_menu_tree($menus);
        });
    }
    
}

if (!function_exists('build_menu_tree')) {
    function build_menu_tree($menus, $parentId = null)
    {
        return $menus
            ->filter(fn($item) => $item->parent_id == $parentId)
            ->map(function ($item) use ($menus) {
                $item->children = build_menu_tree($menus, $item->id);
                return $item;
            })
            ->values();
    }
}
