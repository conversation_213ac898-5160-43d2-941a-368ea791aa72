<?php

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

if (!function_exists('is_multilang_site')) {
    /**
     * Determine whether the site is in multilingual mode
     *
     * @return bool
     */
    function is_multilang_site(): bool
    {
        return Cache::remember('kazcms_is_multilang', 60, function () {
            return DB::table('site_settings')->value('is_multilang') == 1;
        });
    }
}

if (!function_exists('valid_slugs')) {
    /**
     * Get a list of valid slugs (excluding system-reserved slugs)
     *
     * @return array
     */
    function valid_slugs(): array
    {
        $reserved = ['login', 'register', 'logout', 'admin', 'password', 'api'];
        $slugs = DB::table('elements')->pluck('slug')->toArray();
        return array_diff($slugs, $reserved);
    }
}
