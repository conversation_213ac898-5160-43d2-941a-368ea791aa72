<?php

use App\Models\ContentType;
use Illuminate\Support\Facades\Cache;

if (!function_exists('getFrontendContentTypes')) {
    /**
     * Get frontend-visible content types with caching.
     *
     * @return \Illuminate\Support\Collection
     */
    function getFrontendContentTypes()
    {
        Cache::forget('frontend_content_types');
        return Cache::rememberForever('frontend_content_types', function () {
            return ContentType::where('show_on_frontend', true)
                ->orderBy('display_order')
                ->get();
        });
    }






}
