<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $exception)
    {
        // 全局拦截 MatchException
        if ($exception instanceof MatchException) {
            $lang = app()->getLocale();
            return redirect()->route('error.page', [
                'lang' => $lang,
                'code' => $exception->errorCode
            ])->with(['error_data' => $exception->context]);
        }

        return parent::render($request, $exception);
    }
}
