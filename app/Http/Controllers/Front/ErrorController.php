<?php
// app/Http/Controllers/Frontend/ContentTypeController.php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\ContentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Http\Requests\ContentListRequest;
use App\Services\ContentListService;
use Illuminate\Http\JsonResponse;
//Admin
use App\Http\Controllers\Admin\fields\FieldsController as AdminFieldsController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Models\Fields\FieldType;
use Illuminate\Support\Facades\DB;
use App\Services\CategoryService;
use Illuminate\Support\Str;



class ErrorController extends Controller
{
        public function index($code)
        {
            
            $errorData = session('error_data'); 
        return view('frontend.errors.page', compact('errorData', 'code'));
        }
}