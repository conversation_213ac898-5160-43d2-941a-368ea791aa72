<?php

namespace App\Http\Controllers\Front;

use App\Models\Page;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FrontPagesController extends Controller
{
    /**
     * Display a page based on language and Page model.
     */
    public function show(string $lang, Page $page)
    {
        // Get the title for the current language, fallback to 'all' if not set
        $title = $page->title[$lang] ?? ($page->title['all'] ?? '');

        // Get the content
        // If content_all_languages is set, use 'all' key; otherwise use language-specific content with fallback
        if (!empty($page->content_all_languages)) {
            $content = $page->content['all'] ?? '';
        } else {
            $content = $page->content[$lang] ?? ($page->content['all'] ?? '');
        }

        // Get the slug for the current language, fallback to 'all'
        $slug = $page->slug[$lang] ?? ($page->slug['all'] ?? '');

        // Get meta information per field for the current language
        $meta = [];
        if (!empty($page->meta)) {
            foreach ($page->meta as $field => $values) {
                $meta[$field] = $values[$lang] ?? ($values['all'] ?? '');
            }
        }

        // Process kazview placeholders inside content and return the view config
        $kazview = $this->processKazViewConfig($content);
        if (!$kazview) {
            abort(404); // Abort if no kazview is found
        }

        // Get the configuration from the kazview
        $kazviewConfigs = $kazview->config;

        // 1️⃣ Prepare config for getList usage
        $params = app(\App\Services\KazViewService::class)
            ->buildParamsFromConfig($kazviewConfigs, $kazview->content_type_id);

        // 2️⃣ Call getList service to fetch data based on parameters
        $result = app(\App\Services\ContentListService::class)
            ->getList($params);

        // Render the frontend single page view
        return view('frontend.single_page.index', [
            'page' => $page,
            'title' => $title,
            'content' => $content,
            'slug' => $slug,
            'meta' => $meta,
            'lang' => $lang,
            'kazviewData' => $kazviewConfigs,
        ]);
    }



}
