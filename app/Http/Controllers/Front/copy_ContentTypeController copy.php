<?php
// app/Http/Controllers/Frontend/ContentTypeController.php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\ContentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Http\Requests\ContentListRequest;
use App\Services\ContentListService;
use App\Services\ContentDataService;
use Illuminate\Http\JsonResponse;
//Admin
use App\Http\Controllers\Admin\fields\FieldsController as AdminFieldsController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Http\Controllers\Admin\FieldSubmitController as AdminFieldSubmitController;
use App\Models\Fields\FieldType;
use Illuminate\Support\Facades\DB;
use App\Services\CategoryService;
use App\Services\KazViewService;
use Illuminate\Support\Str;
use App\Models\DocumentContentVersion;
use App\Models\DocumentContent;
use App\Services\FieldService;
use App\Models\Page;
use App\Http\Controllers\Front\FrontPagesController;
use Illuminate\Support\Facades\Blade;
//use App\Http\Controllers\Admin\KazViewController as AdminKazViewController;
use App\Services\TemplateService;



class ContentTypeController extends Controller
{
    protected ContentListService $service;
    protected ContentDataService $dataService;
    protected FieldService $FieldService;
    protected KazViewService $kazViewService;

    public function __construct(ContentListService $service, ContentDataService $dataService, FieldService $FieldService, KazViewService $kazViewService)
    {
        $this->service = $service;
        $this->dataService = $dataService;
        $this->FieldService = $FieldService;
        $this->kazViewService = $kazViewService;
    }

    public function handle($slug, Request $request)
    {
        $locale = App::getLocale(); // 当前语言，例如 en、zh-cn
        $contentType = ContentType::where("slug->{$locale}", $slug)->first();

        if (!$contentType) {
            // 没找到 slug，继续让 Laravel 走 fallback 或 404
            abort(404);
        }
        // 找到，进入对应模块的逻辑
        return view('frontend.content_type.index', [
            'contentType' => $contentType,
        ]);
    }
    public function index($lang, $slug)
    {
        $contentType = $this->service->getContentTypeByLangAndSlug($lang, $slug);

        if (!$contentType || !$contentType->show_on_frontend) {
            abort(404);
        }
        $data_id = 0;
        dd(1);
        switch ($contentType->render_type) {
            case 'form':
                return $this->renderForm($contentType, $lang, $data_id);

            case 'document':
                return $this->renderDocument($contentType);

            case 'normal':
            default:
                return $this->renderNormalList($contentType);
        }
    }

    protected function renderNormalList($contentType)
    {
        $lang = app()->getLocale();
        $contentTypeId = $contentType->id;

        // Fetch pages for this content type
        $pages = Page::getPages($contentTypeId, 'list', 1, 1, true);
        if ($pages->isEmpty()) {
            abort(404);
        }

        $page = $pages[0];

        // Determine content based on language
        if (!empty($page->content_all_languages)) {
            $pageContent = $page->content['all'] ?? '';
        } else {
            $pageContent = $page->content[$lang] ?? ($page->content['all'] ?? '');
        }

        $kazview = $this->kazViewService->processKazViewConfig($pageContent);

        $fc = app(AdminFieldsController::class);
        $fieldCategoriesResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId, [], [4 => ["*"]]);
        $fieldCategories = collect($fieldCategoriesResponse->getData(true));
        $fields = $fc->getFieldsByContentTypeDynamic($contentTypeId, [], [], true);

        // Prepare contents array
        if (!$kazview) {
            $selectedFields = [];
            $contents = [
                'data' => [],
                'meta' => [],
                'paginator' => [],
                'extras' => ['attachments' => [], 'categories' => []],
                'has_drafts' => [],
            ];
        } else {
            $selectedFields = (array)($kazview->config['selected_fields'] ?? []);
            $params = $this->kazViewService->buildParamsFromConfig($kazview->config, $contentTypeId);
            $params['extras'] = ['attachments' => true, 'categories' => true];
            $contents = $this->service->getList($params);
        }

        // Prepare category labels and selections
        $categoryFieldLabels = $fieldCategories
            ->pluck("label_slug_json")
            ->map(fn($json) => json_decode($json, true)[$lang] ?? null)
            ->filter()
            ->values()
            ->toArray();

        $queryParams = collect(request()->query())
            ->only($categoryFieldLabels)
            ->toArray();

        $categorySelections = app(CategoryService::class)->resolveCategoryPathsBySlug($queryParams, $contentTypeId, $lang);
        $localizedUrls = app(CategoryService::class)->generateLocalizedUrls(
            $categorySelections['fieldCategoryMap'],
            url()->full(),
            $lang
        );

        // Prepare category tree
        $categoryIds = $fieldCategories
            ->filter(fn($item) => isset($item['show_in_frontend']) && $item['show_in_frontend'] == 1)
            ->pluck('category_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();

        $tree = app(AdminCategoryController::class)->getCategoryTreeByIds($categoryIds, 2, false);
        $categories = collect($tree->getData(true));
        $categoryTree = $categories->map(fn($items) => collect($items));

        // Render KazView template
        $renderedPageContent = Blade::render($pageContent, [
            'fields' => $fields,
            'contentTypeId' => $contentTypeId,
            'contentType' => $contentType,
            'contents' => $contents,
            'status' => 'published',
            'paginator' => $contents['paginator'],
            'categoryTree' => $categoryTree,
            'fieldCategories' => $fieldCategories,
            'tree' => $categorySelections['fieldCategoryMap'],
            'selectedCategoryFieldIds' => $categorySelections['fieldData'],
            'localizedPaths' => [$contentType->slug, $localizedUrls],
        ]);

        // Return the view
        return view('frontend.content.templates/' . $contentTypeId . '.list', [
            'contentTypeId' => $contentTypeId,
            'contentType' => $contentType,
            'contents' => $contents,
            'status' => 'published',
            'paginator' => $contents['paginator'],
            'categoryTree' => $categoryTree,
            'fieldCategories' => $fieldCategories,
            'tree' => $categorySelections['fieldCategoryMap'],
            'selectedCategoryFieldIds' => $categorySelections['fieldData'],
            'localizedPaths' => [$contentType->slug, $localizedUrls],
            'renderedPageContent' => $renderedPageContent,
        ]);
    }


    protected function renderForm($contentType, $lang, $data_id = 0)
    //设置提交后是否可以再编辑，开发时间，开发时间内是否可以编辑
    {
        $content_type_id = $contentType->id;
        $fc = app(AdminFieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);
        $fields = $fieldsResponse->getData();
        $status = 'published';
        $version = '0';
        $pages = Page::getPages($content_type_id, 'form', 1, 1, true);
        if ($pages->isEmpty()) {
            abort(404);
        }
        // Determine content based on language
        if (!empty($page->content_all_languages)) {
            $pageContent = $page->content['all'] ?? '';
        } else {
            $pageContent = $page->content[$lang] ?? ($page->content['all'] ?? '');
        }
        $kazview = $this->kazViewService->processKazViewConfig($pageContent);

        $fieldTypes = FieldType::all()->keyBy('id');
        $contentType = ContentType::find($content_type_id);

        $data = [];

        if ($data_id) {
            if ($status === 'version' && $version !== null) {
                $data = $this->filterFieldDataWithVersion($fieldsResponse, $data_id, 'draft', $version);
            } elseif ($status === 'draft') {
                $data = $this->filterFieldDataWithVersion($fieldsResponse, $data_id, 'draft');
            } elseif ($status === 'published') {
                $data = $this->filterFieldDataWithVersion($fieldsResponse, $data_id, 'published');
            } else {
                $hasDraft = DB::table('field_data_v')
                    ->where('data_id', $data_id)
                    ->where('content_type_id', $content_type_id)
                    ->where('status', 'draft')
                    ->exists();

                if ($hasDraft) {
                    $data = $this->filterFieldDataWithVersion($fieldsResponse, $data_id, 'draft');
                } else {
                    $data = $this->filterFieldDataWithVersion($fieldsResponse, $data_id, 'published');
                }
            }
        }

        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);
        $renderedPageContent = Blade::render($pageContent, [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'content_type' => $contentType,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
            'status' => $status,
            'version' => $version,
        ]);
        dd($renderedPageContent);
        return view('frontend.form.submit', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'content_type' => $contentType,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
            'status' => $status,
            'version' => $version,
            'localizedPaths' => $contentType->slug,
            'renderedPageContent' => $renderedPageContent,

        ]);
    }

    protected function renderDocument($contentType)
    {
        //dd(Request()->all());
        //dd($contentType);
        $contentTypeId = $contentType->id;

        $fc = app(AdminFieldsController::class);


        $fieldCategoriesResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId, [], [4 => ["*"]]);
        $fieldCategories = collect($fieldCategoriesResponse->getData(true));
        // dd($fieldCategories);
        $lang = app()->getLocale();

        //dd($fieldCategories);
        $categoryFieldLabels = $fieldCategories
            ->pluck("label_slug_json")
            ->map(fn($json) => json_decode($json, true)[$lang] ?? null)
            ->filter()
            ->values()
            ->toArray();
        //dd($categoryFieldLabels);
        $queryParams = collect(request()->query())
            ->only($categoryFieldLabels)
            ->toArray();
        //dd($queryParams);
        $categorySelections = app(CategoryService::class)->resolveCategoryPathsBySlug($queryParams, $contentTypeId, $lang);
        //dd($categorySelections);

        $localizedUrls = app(CategoryService::class)->generateLocalizedUrls(
            $categorySelections['fieldCategoryMap'],
            url()->full(),
            $lang, // or 'en-us'


        );
        //dd($localizedUrls);
        $categoryIds = $fieldCategories
            ->filter(function ($item) {
                return isset($item['show_in_frontend']) && $item['show_in_frontend'] == 1;
            })
            ->pluck('category_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();
        //dd($categoryIds);
        $tree = app(AdminCategoryController::class)->getCategoryTreeByIds($categoryIds, 2, false);
        $fields_list = [];

        //dd($categorySelections['fieldData']);
        if (!empty($categorySelections['fieldData'])) {
            foreach ($categorySelections['fieldData'] as $fieldId => $data) {
                //foreach($data['category_ids'] as $categoryId) {
                $fields_list[] = [4, $fieldId, ['data' => ['all' => $data['category_ids']]]];
                //}
            }
        }
        //dd($fields_list);
        $contents = $this->service->getList([
            'content_type_id' => $contentTypeId,
            'fields_list' => $fields_list,

            'perpage' => 20,
            'status' => 'published',
            'extras' => ['attachments' => true, 'categories' => true],
        ]);
        $categories = collect($tree->getData(true));
        $categoryTree = collect($categories)->map(fn($items) => collect($items));
        $fieldCategories = collect($fieldCategoriesResponse->getData(true));
        return view('frontend.content.data.list', [
            'contentTypeId' => $contentTypeId,
            'contentType' => $contentType,
            'contents' => $contents,
            'status' => 'published',
            'paginator' => $contents['paginator'],
            'categoryTree' => $categoryTree,
            'fieldCategories' => $fieldCategories,
            'tree' => $categorySelections['fieldCategoryMap'],
            'selectedCategoryFieldIds' => $categorySelections['fieldData'],
            'localizedPaths' =>  [$contentType->slug, $localizedUrls] //$contentType->slug,
        ]);
    }


    public function getCategoryChildren(Request $request, $categoryId)
    {

        $categoryIds = [$categoryId];

        return $tree = app(AdminCategoryController::class)->getCategoryTreeByIds(
            rootCategoryIds: $categoryIds,
            depth: 2,
            onlyExactLevel: false,
            selectFields: ['id', 'name', 'slug', 'parent_id', 'level', 'category_id', 'final_category'],
            includeRoot: false,
            lang: app()->getLocale()
        );

        // Usually the structure is [parent_id => [children]]
        //$children = $tree[$categoryId] ?? [];

        // Return the children as JSON response
        return response()->json($tree);
    }

    public function detail($lang, $slug, $dataSlug)
    {
        $contentType = $this->service->getContentTypeByLangAndSlug($lang, $slug);

        if (!$contentType || !$contentType->show_on_frontend) {
            abort(404);
        }

        // Route to different handler methods based on render_type
        switch ($contentType->render_type) {
            case 'normal':
                return $this->detailNormal($lang, $slug, $dataSlug, $contentType);

            case 'form':
                return $this->detailForm($lang, $slug, $dataSlug, $contentType);

            case 'document':
                return $this->detailDocument($lang, $slug, $dataSlug, $contentType);

            default:
                abort(404, 'Unsupported render type.');
        }
    }

    /**
     * Handle render_type = "normal"
     */
    protected function del_detailNormal($lang, $slug, $extraSegments, $contentType)
    {
        $content_type_id = $contentType->id;

        $dataSlug = $this->FieldService->getSeoVariableFieldWithData($content_type_id, 'slug', $lang, $extraSegments[0]);

        if (!$dataSlug['data']) {
            abort(404);
        }

        $data_id = $dataSlug['data']->data_id;



        $fc = app(AdminFieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);
        $fields = $fieldsResponse->getData();

        $fieldTypes = FieldType::all()->keyBy('id');

        $data = [];
        if ($data_id) {
            $adminField = app(AdminFieldSubmitController::class);
            $data = $adminField->filterFieldDataWithVersion($fieldsResponse, $data_id, 'published');
        }

        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);
        //$dataSlug = $this->FieldService->getSeoVariableFieldWithData($content_type_id, 'slug', $lang, $extraSegments[0]);

        $localizedPaths = [$contentType->slug, $dataSlug['data']['data']];

        return view('frontend.content.templates/' . $content_type_id . '.detail-normal', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'contentType' => $contentType,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
            'extraSegments' => $extraSegments,
            'localizedPaths' => $localizedPaths,
        ]);
    }
    protected function detailNormal($lang, $slug, $extraSegments, $contentType)
    {
        $lang = app()->getLocale();
        $contentTypeId = $contentType->id;
        $dataSlug = $this->FieldService->getSeoVariableFieldWithData($contentTypeId, 'slug', $lang, $extraSegments[0]);

        if (!$dataSlug['data']) {
            abort(404);
        }

        $data_id = $dataSlug['data']->data_id;
        // Fetch pages for this content type
        $pages = Page::getPages($contentTypeId, 'detail', 1, 1, true);
        if ($pages->isEmpty()) {
            abort(404);
        }

        $page = $pages[0];

        // Determine content based on language
        if (!empty($page->content_all_languages)) {
            $pageContent = $page->content['all'] ?? '';
        } else {
            $pageContent = $page->content[$lang] ?? ($page->content['all'] ?? '');
        }

        $kazview = $this->kazViewService->processKazViewConfig($pageContent);
        $fc = app(AdminFieldsController::class);
        //$fieldCategoriesResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId, [], [4 => ["*"]]);
        //$fieldCategories = collect($fieldCategoriesResponse->getData(true));
        $fields = $fc->getFieldsByContentTypeDynamic($contentTypeId, [], [], true);

        // Prepare contents array
        if (!$kazview) {
            $selectedFields = [];
            $contents = [
                'data' => [],
                'meta' => [],
                'paginator' => [],
                'extras' => ['attachments' => [], 'categories' => []],
                'has_drafts' => [],
            ];
        } else {
            $selectedFields = (array)($kazview->config['selected_fields'] ?? []);
            $params = $this->kazViewService->buildParamsFromConfig($kazview->config, $contentTypeId);
            $params['extras'] = ['attachments' => true, 'categories' => true];
            $params['data_ids'] = [$data_id];
            $contents = $this->service->getList($params);
            //dd($contents);
        }
        /*
        // Prepare category labels and selections
        $categoryFieldLabels = $fieldCategories
            ->pluck("label_slug_json")
            ->map(fn($json) => json_decode($json, true)[$lang] ?? null)
            ->filter()
            ->values()
            ->toArray();
    
        $queryParams = collect(request()->query())
            ->only($categoryFieldLabels)
            ->toArray();
    
        $categorySelections = app(CategoryService::class)->resolveCategoryPathsBySlug($queryParams, $contentTypeId, $lang);
        $localizedUrls = app(CategoryService::class)->generateLocalizedUrls(
            $categorySelections['fieldCategoryMap'],
            url()->full(),
            $lang
        );
    
        // Prepare category tree
        $categoryIds = $fieldCategories
            ->filter(fn($item) => isset($item['show_in_frontend']) && $item['show_in_frontend'] == 1)
            ->pluck('category_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    
        $tree = app(AdminCategoryController::class)->getCategoryTreeByIds($categoryIds, 2, false);
        $categories = collect($tree->getData(true));
        $categoryTree = $categories->map(fn($items) => collect($items));
    */

        $localizedUrls = [$contentType->slug, $dataSlug['data']['data']];

        // Render KazView template
        $renderedPageContent = Blade::render($pageContent, [
            'fields' => $fields,
            'contentTypeId' => $contentTypeId,
            'contentType' => $contentType,
            'contents' => $contents,
            'data' => $contents['data'][$data_id] ?? [],
            'status' => 'published',
            'paginator' => $contents['paginator'],
            //'categoryTree' => $categoryTree,
            //'fieldCategories' => $fieldCategories,
            //'tree' => $categorySelections['fieldCategoryMap'],
            //'selectedCategoryFieldIds' => $categorySelections['fieldData'],
            //'localizedPaths' => [$contentType->slug, $localizedUrls],
        ]);

        // Return the view
        return view('frontend.content.templates/' . $contentTypeId . '.detail-normal', [
            'contentTypeId' => $contentTypeId,
            'contentType' => $contentType,
            'contents' => $contents,
            'status' => 'published',
            'paginator' => $contents['paginator'],
            //'categoryTree' => $categoryTree,
            //'fieldCategories' => $fieldCategories,
            //'tree' => $categorySelections['fieldCategoryMap'],
            //'selectedCategoryFieldIds' => $categorySelections['fieldData'],
            'localizedPaths' => [$contentType->slug, $localizedUrls],
            'renderedPageContent' => $renderedPageContent,
        ]);
    }

    /**
     * Handle render_type = "form"
     */
    protected function detailForm($lang, $slug, $dataSlug, $contentType)
    {
        $content_type_id = $contentType->id;

        $data_id = $this->dataService->getFieldDataBySlug($content_type_id, $lang, $dataSlug[0]);
        if (!$data_id) {
            abort(404);
        }



        $fc = app(AdminFieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);
        $fields = $fieldsResponse->getData();

        $fieldTypes = FieldType::all()->keyBy('id');

        $data = [];
        if ($data_id) {
            $adminField = app(AdminFieldSubmitController::class);
            $data = $adminField->filterFieldDataWithVersion($fieldsResponse, $data_id, 'published');
        }

        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);

        return view('frontend.content.data.detail', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'contentType' => $contentType,
            'extraSegments' => $dataSlug,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
        ]);
    }

    /**
     * Handle render_type = "document"
     */
    protected function detailDocument($lang, $slug, $extraSegments, $contentType)
    {
        $content_type_id = $contentType->id;

        $dataSlug = $this->FieldService->getSeoVariableFieldWithData($content_type_id, 'slug', $lang, $extraSegments[0]);

        if (!$dataSlug['data']) {
            abort(404);
        }

        $data_id = $dataSlug['data']->data_id;
        $latestVersion = DocumentContentVersion::getLastActiveVersionNumber($content_type_id, $data_id);
        $versionId = $latestVersion->id;
        $documentTree = DocumentContent::getDocumentTreeFlat(
            $data_id,
            1,
            $versionId,
            ['id', 'name', 'slug', 'parent_id', 'level', 'show_order'],
            [
                ['content', '<>', ''],
                ['version_id', '=', $versionId],
                ['content_type_id', '=', $content_type_id],
            ]
        );

        $fc = app(AdminFieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);
        $fields = $fieldsResponse->getData();

        $fieldTypes = FieldType::all()->keyBy('id');

        $data = [];
        if ($data_id) {
            $adminField = app(AdminFieldSubmitController::class);
            $data = $adminField->filterFieldDataWithVersion($fieldsResponse, $data_id, 'published');
        }
        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);

        $localizedPaths = [$contentType->slug, $dataSlug['data']['data']];
        return view('frontend.content.data.detail.document', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'contentType' => $contentType,
            'fieldTypes' => $fieldTypes,
            'extraSegments' => $extraSegments,
            'data_id' => $data_id,
            'versionId' => $versionId,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
            'documentTree' => $documentTree,
            'localizedPaths' => $localizedPaths,
        ]);
    }
}
