<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\DocumentContent;
use Illuminate\Http\Request;
use App\Models\Fields\FieldData;
use App\Models\DocumentContentVersion;

use App\Services\ContentListService;
use App\Services\ContentDataService;
use App\Services\FieldService;

class DocumentController extends Controller
{
    protected ContentListService $service;
    protected ContentDataService $dataService;
    protected FieldService $fieldService;

    public function __construct(ContentListService $service, ContentDataService $dataService, FieldService $fieldService)
    {
        $this->service = $service;
        $this->dataService = $dataService;
        $this->fieldService = $fieldService;
    }
    /**
     * 显示指定 content_type_id 和 data_id 的文档章节树及默认编辑区
     * URL: GET /admin/document-content/{content_type_id}/{data_id}?chapter_id=...
     */
    public function index($lang, $contentTypeSlug, $extraSegments)
    {
        $contentType = $this->service->getContentTypeByLangAndSlug($lang, $contentTypeSlug);
        $content_type_id = $contentType->id;
        $dataSlug = $this->fieldService->getSeoVariableFieldWithData($content_type_id, 'slug',$lang, $extraSegments[0]);
        $data_id = $dataSlug['data']->data_id;
        $request = Request();
        $chapterId = $request->query('chapter_id', 0);
        $versionId = $request->query('version', 0);
        $queryParams = $request->query(); 

        // 获取章节树（只顶层+递归子章节）
        $fieldData = FieldData::where('data_id', 0)
            ->where('content_type_id', $content_type_id)
            ->where('id', $data_id)
            ->first();
        $expandLevel = 1; // 默认展开一级
        if ($fieldData && $fieldData->extra_config) {
            $config = json_decode($fieldData->extra_config, true);
            if (isset($config['expand_level']['frontend']) && is_numeric($config['expand_level']['frontend'])) {
                $expandLevel = (int) $config['expand_level']['frontend'];
            }
        }
        $tree = DocumentContent::getDocumentTreeFlat($data_id, $expandLevel, $versionId);
        // 默认选中章节内容，如果 chapter_id=0，右侧编辑区为空

        $chapter = null;
     
        $versions = DocumentContentVersion::activeVersions($content_type_id, $data_id)->get();
        $queryParams = $request->query(); 
        $localizedUrls = $this->getLocalizedChapterPaths(array_slice($extraSegments,1), $lang,$queryParams);
        $localizedPaths = [$contentType->slug,$dataSlug['data']['data'],$localizedUrls['pathsByLang']];
        $chapter = end($localizedUrls['chapters']);
        return view('frontend.document.index', compact(
            'content_type_id',
            'contentType',
            'data_id',
            'tree',
            'chapter',
            'chapterId',
            'versions',
            'versionId',
            'extraSegments',
            'localizedPaths'

        ));
    }



    public function getLocalizedChapterPaths(array $segments, string $currentLang, array $queryParams = []): array
    {
        $availableLangs = getActiveLanguages(); // 获取所有语言对象
        $pathsByLang = [];
    
        // 初始化结果数组
        foreach ($availableLangs as $lang) {
            $pathsByLang[$lang->code] = '';
        }
        $parentId = 0;
        $chapters = [];
        // 逐级查找
        foreach ($segments as $slugPart) {
            $chapter = DocumentContent::where('parent_id', $parentId)
                ->where("slug->$currentLang", $slugPart)
                ->first();
    
            if (!$chapter) {
                break; // 找不到就中断
            }
    
            // 添加该层的多语言 slug
            foreach ($availableLangs as $lang) {
                $pathsByLang[$lang->code] .= '/' . ($chapter->slug[$lang->code] ?? '');
            }
            $chapters[] = $chapter;
            // 更新 parentId 继续往下查
            $parentId = $chapter->id;
        }
    
        // 构造查询参数字符串
        $queryString = '';
        if (!empty($queryParams)) {
            $queryString = '?' . http_build_query($queryParams);
        }
    
        // 把查询参数追加到每个语言路径后面
        foreach ($pathsByLang as $langCode => &$path) {
            $path .= $queryString;
        }
        return ['pathsByLang'=>$pathsByLang,'chapters'=>$chapters];
    }
    
}
