<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Services\KazViewService;
use Illuminate\Support\Facades\Auth;
use App\Models\ContentType;
use App\Services\FieldService;
use App\Models\KazView;
use App\Http\Controllers\Controller;
use App\Services\TemplateService;
use App\Http\Controllers\Admin\fields\FieldsController;

class KazViewController extends Controller
{
    protected $service;
    protected $fieldService;
    protected $templateService;

    public function __construct(KazViewService $service, TemplateService $templateService, FieldService $fieldService)
    {
        $this->service = $service;
        $this->templateService = $templateService;
        $this->fieldService = $fieldService;

        // 中间件限制只有管理员可以操作
    }

    /**
     * 首页：列出所有 KazView
     */
    public function del_index()
    {
        $views = $this->service->getUserViews(Auth::id());
        return view('admin.kazview.index', compact('views'));
    }
    public function index(Request $request)
    {
        // Optional: add search & pagination
        $query = KazView::query();

        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%")
                  ->orWhere('template_type', 'like', "%{$search}%");
        }

        $views = $query->orderBy('created_at', 'desc')->get();

        return view('admin.kazview.index', compact('views'));
    }
    /**
     * 编辑 / 新增页
     */
    public function edit(?int $id = null)
    {
        $view = $id ? $this->service->getView($id) : null;

        // 获取 Content Type 列表
        // 假设你有 ContentType 模型
        $contentTypes = ContentType::all(); // 或按需要排序
    
        return view('admin.kazview.edit', compact('view', 'contentTypes'));
    }

    /**
     * 保存 KazView
     */
    public function save(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'content_type_id' => 'required|integer',
            'template_type' => 'required|string|in:data-list,data-detail,form,document',
            'config' => 'required|array',
        ]);
        $data['secret_token'] = bin2hex(random_bytes(16)); // 32-char hex string
        // 如果有 ID，说明是更新，否则创建
        $id = $request->input('id');
        if ($id) {
            $view = KazView::find($id);
            if (!$view) {
                return response()->json(['error' => 'View not found'], 404);
            }
        } else {
            $view = new KazView();
        }

        $view->name = $data['name'];
        $view->secret_token = $data['secret_token'];
        $view->content_type_id = $data['content_type_id'];
        $view->config = $data['config']; // Laravel 会自动保存 JSON 类型
        $view->user_id = Auth::id();
        $view->template_type = $data['template_type'];
        $view->save();

        return response()->json([
            'success' => true,
            'id' => $view->id,
            'message' => $id ? 'View updated' : 'View created'
        ]);
    }

    /**
     * 删除 KazView
     */
    public function delete(Request $request)
    {
        $id = $request->input('id'); // 从 JSON body 里取 id
        $this->service->deleteView($id);
        return response()->json(['success' => true]);
    }

    /**
     * 启用 / 禁用
     */
    public function toggleStatus(int $id)
    {
        $this->service->toggleStatus($id);
        return redirect()->route('kazview.index')->with('success', 'Status updated');
    }

    public function getFields($id)
    {
        // 假设 contentType->fields 是字段数组，每个字段多语言存储
        $fields = $this->fieldService->getFieldsByContentType($id);

        return response()->json($fields);
    }
    public function render($id, $token = null)
    {
        // Build query
        $query = KazView::where('id', $id);
    
        if (!is_null($token)) {
            // If token is provided, enforce it
            $query->where('secret_token', $token);
        }
    
        $view = $query->firstOrFail();
    
        // Decode config JSON
        $config = $view->config;
        $selectedFields = (array)($config['selected_fields'] ?? []);
    
        // Get fields by content type
        $fc = app(FieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($view->content_type_id);
        $fields = (array)$fieldsResponse->getData();
    
        // Render template
        $output = $this->templateService->renderTemplateStructure($fields, $selectedFields, $view->template_type,$id);
        return response($output);
    }
    

}

