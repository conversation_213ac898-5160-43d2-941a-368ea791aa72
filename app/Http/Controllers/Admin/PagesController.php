<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use App\Services\UrlCheckService;
use App\Models\ContentType;

class PagesController extends Controller
{
    protected UrlCheckService $urlCheckService;

    public function __construct(UrlCheckService $urlCheckService)
    {
        $this->urlCheckService = $urlCheckService;
    }
    /**
     * List all single pages, optionally with pagination
     */
    public function list(Request $request)
    {
        $perPage = $request->input('per_page', 20);
        $pages = Page::orderBy('id', 'desc')->paginate($perPage);
        $contentTypes =  ContentType::all()->keyBy('id'); // 或按需要排序
        return view('admin.pages.list', [
            'pages' => $pages, // 编辑时传对象，新增时为 null
            'contentTypes' => $contentTypes,
        ]);
    }

    /**
     * Get a single page by ID
     */
    public function edit(int $id = 0)
    {

        $page = $id ? Page::findOrFail($id) : null;
        $contentTypes = ContentType::all(); // 或按需要排序
        return view('admin.pages.form', [
            'page' => $page, // 编辑时传对象，新增时为 null
            'contentTypes' => $contentTypes,
        ]);
    }

    /**
     * Create or update a single page
     * If $id is null, create; else update
     */
    public function save(Request $request, int $id = null)
    {
        $data = $request->validate([
            'content_type_id' => 'required|integer',
            'content_type' => 'sometimes|string|in:list,detail,static,form',
            'enable' => 'sometimes|boolean',
            // Slug can be either single string or array
            'slug_single' => 'required_without:slug|string|nullable',
            'slug' => 'required_without:slug_single|array|nullable',
            'slug_all_languages' => 'sometimes|boolean',

            // Title
            'title_single' => 'required_without:title|string|nullable',
            'title' => 'required_without:title_single|array|nullable',
            'title_all_languages' => 'sometimes|boolean',

            // Content
            'content_single' => 'required_without:content|string|nullable',
            'content' => 'required_without:content_single|array|nullable',
            'content_all_languages' => 'sometimes|boolean',

            // Meta (optional)
            'meta' => 'sometimes|array|nullable',
            'meta_all_languages' => 'sometimes|boolean',
        ]);


        // Format slug
        if (!empty($data['slug_all_languages'])) {
            $slugValue = $data['slug_single'] ?? reset($data['slug']);
            $data['slug'] = [
                'all' => $slugValue,
                'segments' => explode('/', trim($slugValue, '/'))
            ];
        } else {
            $slugs = $data['slug'] ?? [];
            $segments = [];

            foreach ($slugs as $lang => $slugValue) {
                $segments[$lang] = explode('/', trim($slugValue, '/'));
            }

            $data['slug'] = $slugs;
            $data['slug']['segments'] = $segments;
        }

        // Format title
        if (!empty($data['title_all_languages'])) {
            $titleValue = $data['title_single'] ?? reset($data['title']);
            $data['title'] = ['all' => $titleValue];
        } else {
            $data['title'] = $data['title'] ?? [];
        }

        // Format content
        if (!empty($data['content_all_languages'])) {
            $contentValue = $data['content_single'] ?? reset($data['content']);
            $data['content'] = ['all' => $contentValue];
        } else {
            $data['content'] = $data['content'] ?? [];
        }


        // ✅ Format meta
        if (!empty($data['meta_all_languages'])) {
            $metaValue = reset($data['meta']);
            $data['meta'] = ['all' => $metaValue];
        } else {
            $data['meta'] = array_filter($data['meta']);
        }

        // ✅ Slug uniqueness check (disabled for now)
        if (false) {
            if ($this->UrlCheckService($data['slug'], $lang)) {
                return response()->json([
                    'error' => "Slug already exists for language '{$lang}'"
                ], 422);
            }
        }
        if (isset($data['content_type_id']) && (int)$data['content_type_id'] === 0) {
            $data['content_type'] = 'static';
        }


        // ✅ Save
        if ($id) {
            $page = Page::findOrFail($id);
            $page->update($data);
        } else {
            $page = Page::create($data);
        }

        //return response()->json($page);
        return redirect()->to(localized_route('admin.pages.list'))
        ->with('success', __('Page created successfully.'));
    }



    /**
     * Delete a single page
     */
    public function delete(int $id)
    {
        $page = Page::findOrFail($id);
        $page->delete();
        return response()->json(['message' => 'Deleted successfully']);
    }

    public function checkSlug(Request $request)
    {
        $slugs = $request->input('slugs', []);
        $useSame = $request->boolean('slug_all_languages');
        $errors = [];

        foreach ($slugs as $lang => $slug) {
            if (empty($slug)) {
                $errors[] = strtoupper($lang) . " slug is empty";
                continue;
            }

            // 检查数据库是否有重复
            $exists = $this->UrlCheckService($slug, $lang);
            if ($exists) {
                $errors[] = strtoupper($lang) . " slug '{$slug}' already exists.";
            }
        }

        if ($errors) {
            return response()->json([
                'status' => 'error',
                'errors' => $errors
            ]);
        }

        return response()->json(['status' => 'ok']);
    }

    public function setMain(int $pageId)
    {
        $page = Page::findOrFail($pageId);
    
        // 只处理 content_type_id > 0 的情况
        if ($page->content_type_id <= 0) {
            return response()->json([
                'success' => false,
                'message' => __('This page cannot be set as main (invalid content type id).')
            ]);
        }
    
        // 将同一个 content_type_id 下的其他页面取消主页面
        Page::where('content_type_id', $page->content_type_id)
            ->where('id', '!=', $page->id)
            ->update(['is_main' => false]);
    
        // 设置当前页面为主页面
        $page->is_main = true;
        $page->save();
    
        return response()->json([
            'success' => true,
            'message' => __('Main page updated for content type ID :id.', ['id' => $page->content_type_id])
        ]);
    }
    
}
