<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Category;


class CategoryController extends Controller
{
    public function form(Request $request, $categoryId, $parentId = 0, $id = null,$show = 0)
    {
       
        $parent = $parentId > 0 ? Category::findOrFail($parentId) : null;
     
        
        $categoryRoot = $categoryId > 0 ? Category::findOrFail($categoryId) : null;
        $category = $id ? Category::findOrFail($id) : null;
        
        $categoryTree = [];
    
        if ($categoryRoot) {
            // 获取该分类下的所有分类（按 category_id 匹配）
            $flat = Category::where('category_id', $categoryId)->orderBy('show_order')->get();
    
            // 构建树结构
            $categoryTree = $this->buildTree($flat,$categoryId);
        }
    
        return view('admin.categories.form', compact('category', 'parent', 'categoryId', 'parentId', 'id', 'categoryTree', 'categoryRoot', 'show'));
    }
    
    
    public function storeOrUpdate(Request $request)
    {
        $parentId = $request->input('parent_id') ?? 0;
        $id = $request->input('id') ?? 0;
        //dd($request->all());
        //exit;
        $data = $request->validate([
            'name' => 'required|array',
            'name.*' => 'required|string',
            'slug' => 'required|array',
            'slug.*' => 'required|string',
            'description' => 'nullable|array',
            'show_order' => 'nullable|integer',
            'is_virtual' => 'nullable|boolean',
            'category_id'=>'required|integer',

        ]);

        $isUpdate = $id > 0 ;
        $category = $isUpdate ? Category::findOrFail($id) : new Category();

        $category->name = $data['name'];
        $category->slug = $data['slug'];
        $category->description = $data['description'] ?? [];
        $category->show_order = $data['show_order'] ?? 0;
        $category->is_virtual = $data['is_virtual'] ?? false;
        if($parentId == 0){
            $data['category_id'] =0;
        }
        $category->category_id = $data['category_id'] ?? 0;

        $duplicateLangs = $this->getDuplicateLanguages($data['name'], $parentId, $id);
        if (!empty($duplicateLangs)) {
            $langList = implode(', ', $duplicateLangs);
            return back()->withErrors([
                'name' => __('Duplicate category name under the same parent in language(s): ') . $langList
            ])->withInput();
        }

        if (!$isUpdate) {
            $category->parent_id = $parentId;
        
            if ($parentId == 0) {
                $category->level = 1;
                $category->parent_path_json = null; // or json_encode([])
            } else {
                $parentCategory = Category::findOrFail($parentId);
                $category->level = $parentCategory->level + 1;
        
                $parentPath = json_decode($parentCategory->parent_path_json,true) ?? [];
                $parentPath[] = $parentId;
                $category->parent_path_json = json_encode($parentPath, JSON_UNESCAPED_UNICODE);
            }
        }
        
        $category->save();

        // 如果是新增，且有父分类，则更新父分类 final_category=1
        if (!$isUpdate && $parentId != 0) {
            Category::where('id', $parentId)->update(['final_category' => 1]);
        }

        // 返回或重定向
        if($parentId == 0){
            return redirect(localized_route('admin.categories.index'))
                        ->with('success', $isUpdate ? __('Updated successfully') : __('Created successfully'));
        }
        return redirect(localized_route('admin.categories.form', [$data['category_id'], $data['category_id'], 0,0]))
                        ->with('success', $isUpdate ? __('Updated successfully') : __('Created successfully'));
    }

    private function buildTree($categories, $parentId = 0)
    {
        $branch = [];

        foreach ($categories as $category) {
        
            if ($category->parent_id == $parentId) {
                $children = $this->buildTree($categories, $category->id);
                if ($children) {
                    $category->children = $children;
                }
                $branch[] = $category;
            }
        }

        return $branch;
    }

    public function index()
    {
        $categories = \App\Models\Category::where('category_id', 0)->get();
    
        
        return view('admin.categories.list', compact('categories'));
    }

    protected function getDuplicateLanguages(array $names, int $parentId, int $currentId = 0): array
    {

        $duplicateLangs = [];

        $categories = Category::where('parent_id', $parentId)->get();

        foreach ($categories as $category) {
            if ($currentId && $category->id == $currentId) {
                continue;
            }

            $existingNames = $category->name;

            foreach ($names as $lang => $name) {
                if (isset($existingNames[$lang]) && $existingNames[$lang] === $name) {
                    $duplicateLangs[] = $lang;
                }
            }
        }

        return array_unique($duplicateLangs);
    }

    public function destroy($categoryId,$id)
    {
        $category = Category::findOrFail($id);
    
        // 递归删除所有子分类
        $this->deleteCategoryWithChildren($category);
    
        return redirect(localized_route('admin.categories.form', [$categoryId, 0, 0, 0]))->with('success', 'Category deleted successfully.');
    }
    
    protected function deleteCategoryWithChildren($category)
    {
        // 先删除子分类
        $children = Category::where('parent_id', $category->id)->get();
    
        foreach ($children as $child) {
            $this->deleteCategoryWithChildren($child);
        }
    
        // 删除当前分类
        $category->delete();
    }
    

    public function getChildren($id)
    {
        $children = Category::where('parent_id', $id)
            ->select('id', 'name', 'final_category') 
            ->get();

        return response()->json($children);
    }


    public function getCategoriesByIds(array $ids, array $selectFields = ['id', 'name', 'final_category', 'parent_path_json'], string $returnType = 'json')
    {
        $query = Category::query()
            ->whereIn('id', $ids)
            ->select($selectFields);
    
        $categories = $query->get()
            ->keyBy('id')
            ->map(function ($item) use ($selectFields) {
                $result = [];
                foreach ($selectFields as $field) {
                    $result[$field] = $item->$field;
                }
                //$result['parents'] =$item->parent_path_json;
                $result['parents'] =$this->getCategoriesByIdsPreserveOrder($item->parent_path_json,$selectFields);
                return $result;
            });
    
        if ($returnType === 'json') {
            return response()->json($categories);
        } elseif ($returnType === 'collection') {
            return $categories;
        } else { 
            return $categories->toArray();
        }
    }
    

    public function getCategoryTreeByIds(
        array $rootCategoryIds,
        int $depth = 1,
        bool $onlyExactLevel = false,
        array $selectFields = ['id', 'name', 'slug','parent_id', 'level', 'category_id', 'final_category'],
        bool $includeRoot = true,
        ?string $lang = null // New parameter to filter by language
    ) {
        if (empty($rootCategoryIds)) {
            return response()->json([]);
        }
    
        // Load all relevant categories once
        $allCategories = Category::select($selectFields)->get();
    
        // Filter JSON fields by language, if $lang is provided
        if ($lang) {
            $allCategories->transform(function ($item) use ($lang) {
                foreach ($item->getAttributes() as $key => $value) {
                    if (
                        is_string($value) &&
                        json_decode($value) !== null &&
                        json_last_error() === JSON_ERROR_NONE
                    ) {
                        $decoded = json_decode($value, true);
                        $item->$key = $decoded[$lang] ?? null;
                    }
                }
                return $item;
            });
        }
    
        // Group categories by parent_id for fast tree traversal
        $categoriesByParent = $allCategories->groupBy('parent_id');
    
        // Group all categories by id for quick lookup (used for including root node)
        $categoriesById = $allCategories->keyBy('id');
    
        $treeResult = [];
    
        foreach ($rootCategoryIds as $rootId) {
            $collected = collect();
    
            // Conditionally include the root node itself
            if ($includeRoot && isset($categoriesById[$rootId]) && $depth >= 1) {
                if (!$onlyExactLevel || ($onlyExactLevel && $depth === 1)) {
                    $collected->push($categoriesById[$rootId]);
                }
            }
    
            // Recursively collect children under root
            $children = $this->collectSubtreeCategories(
                $rootId,
                $categoriesByParent,
                $depth,
                $onlyExactLevel,
                2 // Children of root start at level 2
            );
    
            $collected = $collected->merge($children);
    
            // Set result keyed by root ID
            $treeResult[$rootId] = $collected->unique('id')->values();
        }
    
        return response()->json($treeResult);
    }
    
    
    /**
     * Recursively collect child categories under a parent.
     *
     * @param int $parentId Parent category ID to start from
     * @param \Illuminate\Support\Collection $groupedCategories Categories grouped by parent_id
     * @param int $depth Target depth
     * @param bool $onlyExactLevel Whether to include only categories at exact depth
     * @param int $currentLevel Current depth level (starts at 2 for children of root)
     * @return \Illuminate\Support\Collection
     */
    private function collectSubtreeCategories(
        int $parentId,
        \Illuminate\Support\Collection $groupedCategories,
        int $depth,
        bool $onlyExactLevel,
        int $currentLevel
    ): \Illuminate\Support\Collection {
        $result = collect();
    
        if ($currentLevel > $depth) {
            return $result;
        }
    
        $children = $groupedCategories->get($parentId, collect());
    
        foreach ($children as $child) {
            if (
                (!$onlyExactLevel && $currentLevel <= $depth) ||
                ($onlyExactLevel && $currentLevel === $depth)
            ) {
                $result->push($child);
            }
    
            // Recursively go deeper
            $result = $result->merge(
                $this->collectSubtreeCategories(
                    $child->id,
                    $groupedCategories,
                    $depth,
                    $onlyExactLevel,
                    $currentLevel + 1
                )
            );
        }
    
        return $result;
    }
    
    /**
 * Get categories by ID array, preserving the order
 *
 * @param array $ids
 * @param array $selectFields
 * @return array
 */
public function getCategoriesByIdsPreserveOrder(
    array $ids,
    array $selectFields = ['id', 'name', 'final_category', 'parent_path_json']
): array {
    // 查询所有对应 ID 的分类，只取指定字段
    $categories = \App\Models\Category::whereIn('id', $ids)
        ->select($selectFields)
        ->get()
        ->keyBy('id');

    $result = [];
    foreach ($ids as $id) {
        if (isset($categories[$id])) {
            $result[$id] = $categories[$id];
        }
    }

    return $result;
}


  
}
