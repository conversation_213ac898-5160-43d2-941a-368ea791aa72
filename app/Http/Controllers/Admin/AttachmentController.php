<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\Controller;

class AttachmentController extends Controller
{
    /**
    * Placeholder for file upload and save
    *
    * @param \Illuminate\Http\UploadedFile $file
    * @param int $fieldId
    * @param int $fieldTypeId
    * @param string $lang
    * @param int $userId
    * @return int|null Attachment record ID or null on failure
    */
    public function handleFileUploadAndSave($file, $userId)
    {
        $extension = $file->getClientOriginalExtension();
    
        // 生成日期目录，格式：uploads/2025/07/09/
        $datePath = date('Y/m/d');
        $directory = 'uploads/' . $datePath;
    
        // 生成唯一文件名
        $fileName = Str::random(40) . '.' . $extension;
    
        // 按日期目录存储
        $filePath = $file->storeAs($directory, $fileName, 'public');
    
        if (!$filePath) {
            return null; // 上传失败
        }
    
        $attachmentId = DB::table('attachments')->insertGetId([
            'file_path' => $filePath,
            'file_name' => $fileName,
            'file_ext' => $extension,
            'original_name' => $file->getClientOriginalName(),
            'title' => null,
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'user_id' => $userId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    
        return $attachmentId;
    }
    

    /**
     * Save attachment record to database (does NOT handle file upload)
     *
     * @param array $data
     * @return int Inserted record ID
     */
    protected function saveAttachmentRecord(array $data): int
    {
        return DB::table('attachments')->insertGetId($data);
    }


    public function fetchAttachmentsByIds(array $ids)
    {
        if (empty($ids)) {
            return collect();
        }
    
        return DB::table('attachments')
            ->whereIn('id', $ids)
            ->get()
            ->map(function ($row) {
                // Ensure file_path is browser‑ready
                $row->url = asset('storage/' . $row->file_path);
                return $row;
            })
            ->keyBy('id');
    }
}
