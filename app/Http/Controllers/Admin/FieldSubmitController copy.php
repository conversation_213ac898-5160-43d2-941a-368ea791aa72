<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\Field;
use App\Models\ContentType;
use App\Models\Fields\FieldsOrder;
use App\Models\Fields\FieldType;
use App\Models\Fields\FieldSubtypeLabel;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Support\Str;
use App\Http\Controllers\Admin\fields\FieldsController;
use App\Http\Controllers\Admin\AttachmentController;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Admin\CategoryController;

class FieldSubmitController extends Controller
{
    public function submit($content_type_id, $data_id = 0)
    {
        // 获取字段列表
        $fc = app(FieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);

        $data = [];
        if($data_id){
            $data = $this->filterFieldData($fieldsResponse, $data_id);
        }

        //print_r($data);exit();

        // 获取字段类型数据
        $fieldTypes = FieldType::all()->keyBy('id');

        $fields = $fieldsResponse->getData(); // JsonResponse 转为对象数组

        // 获取内容类型数据
        $contentType = ContentType::find($content_type_id);

        // 返回 Blade 视图

        //get allowed search fields
        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);

        return view('admin.fields.submit', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'content_type' => $contentType,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
        ]);
    }
    public function del_save(Request $request)
    {
        exit();
        $request->validate([
            'content_type_id' => 'required|integer',
            'data_id' => 'required|integer',
        ]);

        $contentTypeId = $request->input('content_type_id');
        $dataId = $request->input('data_id');

        // 获取字段列表
        $fc = app(FieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId);

        $fields = $fieldsResponse->getData(); // JsonResponse 转为对象数组
    }

    public function saveOrUpdateFields(Request $request)
    {
        
        $userId = auth()->id() ?? 0;
        $dataId = (int) $request->input('data_id',0);
        $contentTypeId = (int) $request->input('content_type_id');
        $languages = getActiveLanguages()->pluck('code')->toArray();
        $locale = app()->getLocale();

        $now = now();
        //if data_id is 0, insert a new record
        if ($dataId === 0) {
            $dataId = DB::table('field_data')->insertGetId([
                'data_id' => 0,
                'user_id' => $userId,
                'content_type_id' => $contentTypeId,
                'field_id' => 0,
                'field_type_id' => 0,
                'data' => '{}',
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }

        

        foreach ($request->all() as $key => $value) {
            if (!Str::startsWith($key, 'fields_') || !preg_match('/^fields_(\d+)_(\d+)/', $key, $matches)) {
                continue;
            }
        
            $fieldTypeId = (int) $matches[1];
            $fieldId     = (int) $matches[2];
            $fieldName   = "fields_{$fieldTypeId}_{$fieldId}";     // ← 与前端对应
            $sharedFlag  = $request->boolean("shared_{$fieldName}");
            $primaryLang = $locale;   // app()->getLocale()
        
            $langValues  = [];        // <— 即将写入的数据
        
            if ($fieldTypeId === 3) { // 文件 / 图片类型
                // 1. 先处理主语言上传
                $uploadedFiles = (array)($value[$primaryLang] ?? []);
                $savedIds      = [];
        
                foreach ($uploadedFiles as $file) {
                    if ($file instanceof \Illuminate\Http\UploadedFile) {
                        $attachCtrl = new AttachmentController();
                        if ($aid = $attachCtrl->handleFileUploadAndSave($file, $userId)) {
                            $savedIds[] = $aid;
                        }
                    }
                }
        
                // 2. 写入主语言
                $langValues[$primaryLang] = $savedIds;
        
                if ($sharedFlag) {
                    // 3. 将同一数组复制到其余语言键
                    foreach ($languages as $lg) {
                        $langValues[$lg] = $savedIds;
                    }
                } else {
                    // 4. 针对其他语言，只有用户真正上传时才保存
                    foreach ($languages as $lg) {
                        if ($lg === $primaryLang) continue;
                        $langValues[$lg] = (array)($value[$lg] ?? []);   // 可能为空
                    }
                }
            } elseif($fieldTypeId === 4) {
                $ids = array_filter(
                    array_map('intval', explode(',', (string) $value))
                );
            
                $langValues = ['all' => $ids];
            } elseif ($fieldTypeId === 5) {          // 选项字段 (radio / checkbox / select)

                $value = is_array($value)
                         ? array_values($value)   // 只重新索引，不去重
                         : (string) $value;
            
                $langValues = ['all' => $value];
            } else {
                // 非文件字段按旧逻辑
                foreach ($languages as $lg) {
                    $langValues[$lg] = $value[$lg] ?? '';
                }
            }
        
            // 保存
            $this->saveFieldAndBackup(
                $dataId, $contentTypeId, $fieldId, $fieldTypeId,
                $userId, $langValues, $now
            );
        }
        

        return view('admin.fields.data_save_success')
        ->with('content_type_id', $request->input('content_type_id'))
        ->with('success', 'Data saved successfully.');
    }

    /**
     * Placeholder for file upload and save
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param int $fieldId
     * @param int $fieldTypeId
     * @param string $lang
     * @param int $userId
     * @return int|null Attachment record ID or null on failure
     */


    protected function saveFieldAndBackup(
        int $dataId,
        int $contentTypeId,
        int $fieldId,
        int $fieldTypeId,
        int $userId,
        array $langValues,
        string|\Carbon\Carbon $timestamp
    ): void {
        //echo "$dataId, $contentTypeId, $fieldId, $fieldTypeId, $userId, " . json_encode($langValues) . ", $timestamp";

        $jsonNew = json_encode($langValues, JSON_UNESCAPED_UNICODE);

        $existing = DB::table('field_data')
            ->where('data_id', $dataId)
            ->where('content_type_id', $contentTypeId)
            ->where('field_type_id', $fieldTypeId)
            ->where('field_id', $fieldId)
            ->first();

        if ($existing) {
            if ($jsonNew !== $existing->data) {
                DB::table('field_data_backup')->insert([
                    'data_id' => $dataId,
                    'content_type_id' => $contentTypeId,
                    'field_id' => $fieldId,
                    'field_type_id' => $fieldTypeId,
                    'old_data' => $existing->data,
                    'changed_by' => $userId,
                    'changed_at' => $timestamp,
                ]);

                DB::table('field_data')->where('id', $existing->id)->update([
                    'data' => $jsonNew,
                    'updated_at' => $timestamp,
                ]);
            }
        } else {
            DB::table('field_data')->insert([
                'data_id' => $dataId,
                'user_id' => $userId,
                'content_type_id' => $contentTypeId,
                'field_id' => $fieldId,
                'field_type_id' => $fieldTypeId,
                'data' => $jsonNew,
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            ]);


            
        }
    }

    



    /**
     * Get only those field‑data rows that match the given $fieldsResponse list.
     *
     * @param  \Illuminate\Http\JsonResponse  $fieldsResponse
     * @param  int                            $dataId
     * @return array                          e.g. ['data_3_7' => [...], 'data_1_19' => [...]]
     */
   /**
 * Return field‑data values for $dataId, limited to $fieldsResponse,
 * and bundle all attachments (field_type_id == 3) under "attachments".
 *
 * @param  \Illuminate\Http\JsonResponse  $fieldsResponse
 * @param  int                            $dataId
 * @return array
 */
public function filterFieldData(JsonResponse $fieldsResponse, int $dataId): array
{
    // ---------- 1. build allowed lookup ----------
    $fields  = collect($fieldsResponse->original ?? $fieldsResponse->getData(true));
    $allowed = $fields->map(fn ($f) =>
        ($f->content_type_id ?? $f['content_type_id']) . '-' .
        ($f->field_type_id   ?? $f['field_type_id'])   . '-' .
        ($f->id              ?? $f['id'])
    )->unique()->flip();

    if ($allowed->isEmpty()) {
        return [];
    }

    // ---------- 2. query once ----------
    $rows = DB::table('field_data')
        ->where('data_id', $dataId)
        ->get();

    // ---------- 3. build result ----------
    $result         = [];
    $attachmentIds  = [];   // ① 收集附件 ID
    $categoryIds    = [];   // ② 收集分类 ID

    foreach ($rows as $row) {
        $triplet = "{$row->content_type_id}-{$row->field_type_id}-{$row->field_id}";

        if (!$allowed->has($triplet)) {
            continue;
        }

        $key      = "fields_{$row->field_type_id}_{$row->field_id}";
        $decoded  = json_decode($row->data, true);

        // ---- A. 附件：field_type_id == 3 ----
        if ($row->field_type_id == 3 && is_array($decoded)) {
            foreach ($decoded as $langVal) {
                $ids = is_array($langVal) ? $langVal : [$langVal];
                $attachmentIds = array_merge($attachmentIds, $ids);
            }
        }

        // ---- B. 分类：field_type_id == 4 ----
        if ($row->field_type_id == 4 && is_array($decoded)) {
            foreach ($decoded as $langVal) {
                $ids = is_array($langVal) ? $langVal : [$langVal];
                $categoryIds = array_merge($categoryIds, $ids);
            }
        }

        $result[$key] = $decoded;
    }

    // ---------- 4. fetch attachments ----------
    $attachmentIds = array_values(array_unique($attachmentIds));
    if ($attachmentIds) {
        $attachCtrl = app(AttachmentController::class);
        $result['attachments'] = $attachCtrl->fetchAttachmentsByIds($attachmentIds);
    }

    // ---------- 5. fetch categories ----------
    $categoryIds = array_values(array_unique($categoryIds));
    if ($categoryIds) {
        $catResponse = app(CategoryController::class)->getCategoriesByIds($categoryIds);

        // 如果 getCategoriesByIds 返回 JsonResponse
        $result['categories'] = $catResponse->getData(true);
    }
    //print_r($result);exit();
    return $result;
}



}