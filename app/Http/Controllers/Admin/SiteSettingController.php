<?php

namespace App\Http\Controllers\Admin;

use App\Models\SiteSetting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SiteSettingController extends Controller
{
    /**
     * 显示设置页面
     */
    public function edit()
    {
        $siteSetting = SiteSetting::first();
        $languages = \App\Models\Language::all(); // 假设有语言表
        return view('admin.site_settings.edit', compact('siteSetting', 'languages'));
    }

    /**
     * 保存（创建或更新）站点设置
     */
    public function save(Request $request)
    {
        $data = $request->validate([
            'site_name' => 'required|array',
            'site_url' => 'required|url',
            'meta_title' => 'required|array',
            'meta_keywords' => 'required|array',
            'meta_description' => 'required|array',
            'default_language_id' => 'required|integer|exists:languages,id',
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|array',
            'favicon' => 'nullable|string|max:255',
            'logo' => 'nullable|string|max:255',
            'logo_alt_text' => 'nullable|array',
            'copyright' => 'nullable|array',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:50',
            'timezone' => 'nullable|string|max:100',
            'default_currency' => 'nullable|string|max:10',
            'open_graph_image' => 'nullable|string|max:255',
            'robots_txt' => 'nullable|string',
            'sitemap_url' => 'nullable|string|max:255',
            'social_links' => 'nullable|array',
            'analytics_code' => 'nullable|string',
        ]);

        $siteSetting = SiteSetting::first();

        if ($siteSetting) {
            $siteSetting->update($data);
        } else {
            SiteSetting::create($data);
        }

        return redirect()->back()->with('success', 'Site settings saved successfully.');
    }
}
