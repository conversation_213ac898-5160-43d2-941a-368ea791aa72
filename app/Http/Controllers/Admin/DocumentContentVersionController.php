<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DocumentContentVersion;
use Illuminate\Http\Request;

class DocumentContentVersionController extends Controller
{
    /**
     * 获取指定 content_type_id 和 data_id 的所有版本列表
     */
    public function getVersionList(Request $request, $content_type_id, $data_id)
    {
        $versions = DocumentContentVersion::select('id', 'version', 'is_active')
            ->where('content_type_id', $content_type_id)
            ->where('data_id', $data_id)
            ->orderByDesc('version')
            ->get();

        return response()->json([
            'success' => true,
            'versions' => $versions,
        ]);
    }

    public function store(Request $request, $content_type_id, $data_id)
{
    $validated = $request->validate([
        'version' => 'required|numeric|min:0',
        'is_active' => 'required|numeric|in:0,1',
    ]);

    // 检查是否已存在该版本号
    $exists = DocumentContentVersion::where('content_type_id', $content_type_id)
        ->where('data_id', $data_id)
        ->where('version', $validated['version'])
        ->exists();

    if ($exists) {
        return response()->json([
            'success' => false,
            'message' => 'This version number already exists.'
        ], 409);
    }

    $version = DocumentContentVersion::create([
        'content_type_id' => $content_type_id,
        'data_id' => $data_id,
        'version' => $validated['version'],
        'is_active' => $validated['is_active'],
        'created_by' => auth()->id(),
    ]);

    return response()->json([
        'success' => true,
        'version' => $version,
    ]);
}

}
