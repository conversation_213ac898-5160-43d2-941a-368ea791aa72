<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContentListRequest;
use App\Services\ContentListService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DataController extends Controller
{
    protected ContentListService $service;

    public function __construct(ContentListService $service)
    {
        $this->service = $service;
    }

    /**
     * GET /api/v1/contents
     */
    public function index(ContentListRequest $request): JsonResponse
    {
        $payload = $this->service->getList($request->validated());
        return response()->json($payload);
    }


    public function list(Request $r,$contentTypeId)
    {
        // 固定content_type_id = 1
    

        $contents = $this->service->getList([
            'content_type_id' => $contentTypeId,
            //'langKw'=>['all'=>'地地'],
            'perpage'=>20,
            'status'=>$r->input('status','published'),
            'user_ids'=>[1,2,3],
            //'langKw'=>['kz-kz'=>'好'],
            'extras' => ['attachments'=>true, 'categories'=>true]
        ]);
        return view('admin.data.list', [
            'contentTypeId' => $contentTypeId,
            'contents' => $contents,
            'status' => $r->input('status','published'),
            'paginator' => $contents['paginator'],
        ]);
    }
}