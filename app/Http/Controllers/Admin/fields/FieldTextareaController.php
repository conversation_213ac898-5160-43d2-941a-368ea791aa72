<?php

namespace App\Http\Controllers\Admin\Fields;


use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fields\FieldTextarea;
use App\Rules\DefaultLangJsonNotEmpty;


class FieldTextareaController extends Controller
{
    public function modelClass(): string
    {
        return \App\Models\Fields\FieldTextarea::class;
    }
    public function rules(Request $request): array
    {

       return [
            'id' => 'nullable|integer',
            'label_json' => ['required', 'array', new DefaultLangJsonNotEmpty()],

            'help_text_json' => 'nullable|array',
            'is_required' => 'nullable|boolean',
            'field_type_id' => 'required|integer',
            'content_type_id' => 'required|integer',
        ];
    
    }

    public function checkboxFields(): array
    {
        return [ 'is_required'];
    }
}
