<?php

namespace App\Http\Controllers\Admin\fields;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Fields\FieldCategory;


class FieldCategoriesController extends Controller
{

    public function modelClass(): string
    {
        return \App\Models\Fields\FieldCategory::class;
    }
    public function rules(Request $request): array
    {

        return [
            'id' => 'nullable|integer',
            'label_json' => 'required|array',
            'label_slug_json' => 'required|array',
            'display_style' => 'required|in:radio,checkbox',
            'show_in_frontend' => 'required|boolean',
            'is_required' => 'required|boolean',
            'max_select_count' => 'nullable|integer|min:1',
            'content_type_id' => 'required|integer',
            'field_type_id' => 'required|integer',
            'category_id' => 'required|integer',
        ];
    }


    public function checkboxFields(): array
    {
        return [ 'show_in_frontend','is_required'];
    }
}
