<?php

namespace App\Http\Controllers\Admin\fields;

use Illuminate\Http\Request;
use App\Models\Field;
use App\Models\ContentType;
use App\Models\Fields\FieldsOrder;
use App\Models\Fields\FieldType;
use App\Models\Fields\FieldSubtypeLabel;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use App\Models\Fields\FieldPermissionRule;
use Illuminate\Validation\ValidationException;
use App\Models\Fields\FieldText;
use App\Models\Fields\FieldData;

class FieldsController extends Controller
{
    public function save(Request $request)
    {


        $fieldTypeId = $request->input('field_type_id');

        $fieldType = FieldType::findOrFail($fieldTypeId);
        $relatedTable = $fieldType->related_table;

        $controllerClass = 'App\\Http\\Controllers\\Admin\\fields\\Field' . Str::studly($relatedTable) . 'Controller';

        if (!class_exists($controllerClass)) {
            abort(500, "Controller not found: {$controllerClass}");
        }

        $controllerInstance = app()->make($controllerClass);

        if (!method_exists($controllerInstance, 'rules')) {
            abort(500, "Method rules() not found in {$controllerClass}");
        }

        if (method_exists($controllerInstance, 'checkboxFields')) {
            foreach ($controllerInstance->checkboxFields() as $field) {
                if (!$request->has($field)) {
                    $request->merge([$field => 0]);
                }
            }
        }
        $rules = app()->call([$controllerInstance, 'rules'], ['request' => $request]);
        $modelClass = $controllerInstance->modelClass();

        $fieldModel = $this->saveToModel($request, $modelClass, $rules);
        if($fieldModel['is_new']){
            $this->storeOrUpdateFieldOrders([[
                'content_type_id' => $request->input('content_type_id'),
                'field_id' => $fieldModel['model']->id,
                'field_type_id' => $fieldTypeId,
                'sort_order' => -1,
            ]]);
        }

        return view('admin.fields.fields_save_success')
            ->with('content_type_id', $request->input('content_type_id'))
            ->with('success', 'Field saved successfully.');
    }

    protected function saveToModel(Request $request, string $modelClass, array $rules)
    {
        $data = $request->validate($rules);
        $id = (int) $request->input('id');
        $isNew = $id <= 0;

        $model = $isNew ? new $modelClass() : $modelClass::find($id);

        if (!$isNew && !$model) {
            abort(404, 'Record not found');
        }

        $model->fill($data);
        $model->save();

        $ruleData = [
            'field_id'        => $model->id,
            'content_type_id' => $model->content_type_id,
            'field_type_id'   => $model->field_type_id,
            'action'          => [
                'schema' => ['create','view', 'update','delete'],
                'data'   => ['create','view', 'update','delete'],
            ],
            'user_id'  => auth()->id(),
            'group_id' => null,
        ];

        $this->saveFieldPermissionRule($ruleData);
        return [
            'model' => $model,
            'is_new' => $isNew,
        ];
    }



    public function showFieldsList($content_type_id)
    {
        // 获取字段列表
        $fieldsResponse = $this->getFieldsByContentTypeDynamic($content_type_id);

        // 获取字段类型数据
        $fieldTypes = FieldType::all()->keyBy('id');

        $fields = $fieldsResponse->getData(); // JsonResponse 转为对象数组

        // 获取内容类型数据
        $contentType = ContentType::find($content_type_id);

        // 返回 Blade 视图

        //get allowed search fields

        $allowedSearchFields = $this->getAllowedSearchFields($content_type_id);

        return view('admin.fields.list', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'content_type' => $contentType,
            'fieldTypes' => $fieldTypes,
            'allowedSearchFields' => $allowedSearchFields,
        ]);
    }

    public function showAddFieldStep1($contentTypeId)
    {
        // 取 ContentType 模型，假设你有 ContentType 模型
        $contentType = ContentType::findOrFail($contentTypeId);

        // 取所有可用的 FieldTypes
        $fieldTypes = FieldType::where('is_enabled', 1)->get();

        // 当前语言
        $locale = app()->getLocale();

        return view('admin.fields.add_step1', compact('contentType', 'fieldTypes', 'locale'));
    }


    public function showAddFieldStep2($contentTypeId, $fieldTypeId = 0, $fieldId = 0)
    {
        //$fieldTypeId = $request->input('field_type_id');
        //$contentTypeId = $request->input('content_type_id');

        // 获取字段类型记录
        $fieldType = DB::table('field_types')->where('id', $fieldTypeId)->first();

        if (!$fieldType) {
            return redirect()->back()->withErrors(['field_type_id' => 'Invalid field type ID.']);
        }

        // 获取关联表名
        $relatedTable = $fieldType->related_table;

        if (!$relatedTable) {
            return redirect()->back()->withErrors(['related_table' => 'No related table defined for this field type.']);
        }

        // 查询相关字段记录（用于更新或为空时添加）
        $fieldRecord = null;
        if ($fieldId > 0 ){
            $tableName = preg_replace('/[^a-zA-Z0-9_]/', '', $relatedTable);
            $fieldRecord = DB::table('field_' . $tableName)
                ->where('content_type_id', $contentTypeId)
                ->where('field_type_id', $fieldTypeId)
                ->where('id', $fieldId)
                ->first();
        
            // ✅ 自动把 *_json 字段转为 array
            if ($fieldRecord) {
                foreach ($fieldRecord as $key => $value) {
                    if (is_string($value) && str_ends_with($key, '_json')) {
                        $decoded = json_decode($value, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $fieldRecord->$key = $decoded;
                        }
                    }
                }
            }
        }
        
        if ($fieldTypeId == 1) {
            $subtypeLabels = FieldSubtypeLabel::all();
        }

        if ($fieldTypeId == 4) {
            // 仅当类型为4时查询顶级分类
            $categories = Category::where('parent_id', 0)->get();
        }
        if ($fieldTypeId == 5) {
            // 仅当类型为5时查询显示样式
            $displayStyles = DB::table('option_display_styles')
                ->where('enabled', 1)
                ->orderBy('sort_order')
                ->get()
                ->map(function ($style) {
                    $locale = app()->getLocale();
                    $style->name = json_decode($style->name, true)[$locale] ?? $style->key;
                    $style->description = json_decode($style->description, true)[$locale] ?? '';
                    return $style;
                });
        }

        // 当前语言
        $enabledLanguages = getActiveLanguages();

        // 拼接视图路径
        $viewName = 'admin.fields.create.create_' . $relatedTable;

        // 返回视图
        return view($viewName, [
            'fieldType' => $fieldType,
            'fieldRecord' => $fieldRecord,
            'content_type_id' => $contentTypeId,
            'field_type_id' => $fieldTypeId,
            'enabledLanguages' => $enabledLanguages,
            'categories' => $categories ?? null,
            'displayStyles' => $displayStyles ?? null,
            'subtypeLabels' => $subtypeLabels ?? null,
            'field'=>$fieldRecord,
        ]);
    }


    /**
     * Get the list of fields for a given content type, sorted by FieldsOrder if exists.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function del_getFieldsByContentType(Request $request)
    {
        $contentTypeId = $request->input('content_type_id');

        if (!$contentTypeId) {
            return response()->json(['error' => 'content_type_id is required'], 400);
        }

        // Get fields order list for this content type, keyed by field_id => sort_order
        $orders = FieldsOrder::where('content_type_id', $contentTypeId)
                ->orderBy('sort_order', 'asc')
                ->get(['field_type_id', 'field_id', 'sort_order'])   // 先取需要的3列
                ->mapWithKeys(function ($row) {
                    // 自定义键：field_type_id-field_id  →  对应的 sort_order
                    $key = "{$row->field_type_id}-{$row->field_id}";
                    return [$key => $row->sort_order];
                })
                ->toArray();

                if (!empty($orders)) {
                    // 有自定义排序
                    $fields = Field::where('content_type_id', $contentTypeId)
                        ->get()
                        ->sortBy(function ($field) use ($orders) {
                            // 拼出与 orders 相同的键：field_type_id-field_id
                            $key = "{$field->field_type_id}-{$field->id}";
                            return $orders[$key] ?? PHP_INT_MAX;   // 不在 orders 的放最后
                        })
                        ->values();   // 重新索引
                } else {
                    // 无自定义排序，按 id 升序
                    $fields = Field::where('content_type_id', $contentTypeId)
                        ->orderBy('id', 'asc')
                        ->get();
                }
                
        return response()->json($fields);
    }



    public function del_getFieldsByContentTypeDynamic($contentTypeId, array $permissions = [], array $filter = [])
    {
        
        if (!$contentTypeId) {
            return response()->json(['error' => 'content_type_id is required'], 400);
        }

        $userIds = $permissions['user_ids'] ?? [];
        $groupIds = $permissions['group_ids'] ?? [];

        // 清理 filter，只保留有效类型
        $filter = collect($filter)->filter(function ($v) {
            return is_array($v) && (in_array('*', $v) || count($v) > 0);
        })->toArray();

        // 1. 获取所有字段类型及相关表名
        $fieldTypes = FieldType::all();
        $allFields = collect();

        // 2. 遍历每种字段类型，查询对应的表
        foreach ($fieldTypes as $fieldType) {
            $fieldTypeId = $fieldType->id;
            $table = $fieldType->related_table;

            if (!$table) {
                continue;
            }

            $table = 'field_' . $table;

            // 判断是否应当跳过这个字段类型（根据 filter）
            if (!empty($filter) && !array_key_exists($fieldTypeId, $filter)) {
                continue; // 传了 filter 且未包含此类型，跳过
            }

            $query = DB::table($table)->where('content_type_id', $contentTypeId);

            if (!empty($filter)) {
                $fieldFilter = $filter[$fieldTypeId] ?? null;

                if (is_array($fieldFilter)) {
                    if (in_array('*', $fieldFilter)) {
                        // 所有字段，无需额外处理
                    } elseif (count($fieldFilter) > 0) {
                        $query->whereIn('id', $fieldFilter);
                    } else {
                        continue; // 空数组则跳过
                    }
                }
            }

            $fields = $query->get();

            // 添加字段类型ID
            $fields = $fields->map(function ($item) use ($fieldTypeId) {
                $item->field_type_id = $fieldTypeId;
                return $item;
            });

            $allFields = $allFields->merge($fields);
        }

        // 3. 权限过滤
        $fieldTypeIdSet = $allFields->pluck('field_type_id')->unique()->toArray();
        $fieldIdSet = $allFields->pluck('id')->unique()->toArray();

        $allowedKeys = $this->getPermissionRuleKeys(
            $contentTypeId,
            null,
            $userIds,
            $groupIds,
            null
        );

        $allFields = $allFields->filter(function ($field) use ($contentTypeId, $allowedKeys) {
            $fieldId = $field->field_id ?? $field->id ?? null;
            $fieldTypeId = $field->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";
            return in_array($key, $allowedKeys);
        });

        // 4. 获取排序信息
        $orders = FieldsOrder::where('content_type_id', $contentTypeId)
            ->get(['content_type_id', 'field_type_id', 'field_id', 'sort_order'])
            ->mapWithKeys(function ($item) {
                $key = "{$item->content_type_id}-{$item->field_type_id}-{$item->field_id}";
                return [$key => $item->sort_order];
            })
            ->toArray();

        // 5. 排序
        $counter = 0;
        $sorted = $allFields->sortBy(function ($field) use ($orders, &$counter, $contentTypeId) {
            $fieldId = $field->field_id ?? $field->id ?? null;
            $fieldTypeId = $field->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";
            return $orders[$key] ?? ($counter++);
        })->values();

        // 6. 生成最终结构
        $result = $sorted->mapWithKeys(function ($item) use ($orders, $contentTypeId) {
            $fieldId = $item->field_id ?? $item->id ?? null;
            $fieldTypeId = $item->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";

            $item->sort_order = $orders[$key] ?? null;

            return ["{$fieldTypeId}-{$fieldId}" => $item];
        });

        return response()->json($result);
    }
    public function getFieldsByContentTypeDynamic($contentTypeId, array $permissions = [], array $filter = [], bool $asArray = false)
    {
        if (!$contentTypeId) {
            if ($asArray) {
                return ['error' => 'content_type_id is required'];
            }
            return response()->json(['error' => 'content_type_id is required'], 400);
        }
    
        $userIds = $permissions['user_ids'] ?? [];
        $groupIds = $permissions['group_ids'] ?? [];
    
        // 清理 filter
        $filter = collect($filter)->filter(function ($v) {
            return is_array($v) && (in_array('*', $v) || count($v) > 0);
        })->toArray();
    
        // 1. 获取所有字段类型及相关表名
        $fieldTypes = FieldType::all();
        $allFields = collect();
    
        foreach ($fieldTypes as $fieldType) {
            $fieldTypeId = $fieldType->id;
            $table = $fieldType->related_table;
    
            if (!$table) {
                continue;
            }
    
            $table = 'field_' . $table;
    
            if (!empty($filter) && !array_key_exists($fieldTypeId, $filter)) {
                continue;
            }
    
            $query = DB::table($table)->where('content_type_id', $contentTypeId);
    
            if (!empty($filter)) {
                $fieldFilter = $filter[$fieldTypeId] ?? null;
    
                if (is_array($fieldFilter)) {
                    if (in_array('*', $fieldFilter)) {
                        // all fields
                    } elseif (count($fieldFilter) > 0) {
                        $query->whereIn('id', $fieldFilter);
                    } else {
                        continue;
                    }
                }
            }
    
            $fields = $query->get()->map(function ($item) use ($fieldTypeId) {
                $item->field_type_id = $fieldTypeId;
                return $item;
            });
    
            $allFields = $allFields->merge($fields);
        }
    
        // 权限过滤
        $allowedKeys = $this->getPermissionRuleKeys(
            $contentTypeId,
            null,
            $userIds,
            $groupIds,
            null
        );
    
        $allFields = $allFields->filter(function ($field) use ($contentTypeId, $allowedKeys) {
            $fieldId = $field->field_id ?? $field->id ?? null;
            $fieldTypeId = $field->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";
            return in_array($key, $allowedKeys);
        });
    
        // 排序
        $orders = FieldsOrder::where('content_type_id', $contentTypeId)
            ->get(['content_type_id', 'field_type_id', 'field_id', 'sort_order'])
            ->mapWithKeys(function ($item) {
                $key = "{$item->content_type_id}-{$item->field_type_id}-{$item->field_id}";
                return [$key => $item->sort_order];
            })
            ->toArray();
    
        $counter = 0;
        $sorted = $allFields->sortBy(function ($field) use ($orders, &$counter, $contentTypeId) {
            $fieldId = $field->field_id ?? $field->id ?? null;
            $fieldTypeId = $field->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";
            return $orders[$key] ?? ($counter++);
        })->values();
    
        // 最终结构
        $result = $sorted->mapWithKeys(function ($item) use ($orders, $contentTypeId,$asArray) {
            $fieldId = $item->field_id ?? $item->id ?? null;
            $fieldTypeId = $item->field_type_id ?? null;
            $key = "{$contentTypeId}-{$fieldTypeId}-{$fieldId}";
    
            $item->sort_order = $orders[$key] ?? null;
    
            return ["{$fieldTypeId}-{$fieldId}" => $asArray ? (array) $item : $item];
        })->toArray();
    
        // ✅ 根据 $asArray 返回
        return $asArray ? $result : response()->json($result);
    }
    

    public function delete(Request $request)
    {
        $request->validate([
            'content_type_id' => 'required|integer',
            'field_type_id' => 'required|integer',
            'field_id' => 'required|integer',
        ]);

        $fieldTypeId = $request->input('field_type_id');
        $fieldId = $request->input('field_id');

        // 取 field_type 记录，拿到关联的表名
        $fieldType = FieldType::findOrFail($fieldTypeId);
        $relatedTable = $fieldType->related_table; // e.g. 'text', 'file', 'options'

        // 拼接模型全类名，假设模型都在 App\Models\Fields\ 目录，命名规则统一
        $modelClass = 'App\\Models\\Fields\\Field' . Str::studly($relatedTable);

        if (!class_exists($modelClass)) {
            abort(500, "Model class not found: {$modelClass}");
        }

        // 找到对应模型实例
        $model = $modelClass::find($fieldId);
        if (!$model) {
            abort(404, "Field record not found");
        }

        // 删除
        $model->delete();

        // 返回成功响应，或重定向
        return response()->json(['message' => 'Field deleted successfully']);
    }

    public function updateOrder(Request $request)
    {
        return $this->storeOrUpdateFieldOrders($request->input('orders', []));
    }


    public function storeOrUpdateFieldOrders($data)
    {
        //$data = $request->input('orders'); // 多维数组输入

        foreach ($data as $item) {
            $contentTypeId = $item['content_type_id'];
            $fieldId = $item['field_id'];
            $fieldTypeId = $item['field_type_id'];
            $sortOrder = $item['sort_order'];

            // 如果 sort_order 是 -1，就取当前最大值 + 5
            if ($sortOrder === -1) {
                $maxOrder = FieldsOrder::where('content_type_id', $contentTypeId)->max('sort_order');
                $sortOrder = is_null($maxOrder) ? 5 : $maxOrder + 5;
            }

            // 查找是否已存在该记录
            $existing = FieldsOrder::where('content_type_id', $contentTypeId)
                ->where('field_id', $fieldId)
                ->where('field_type_id', $fieldTypeId)
                ->first();

            if ($existing) {
                // 更新排序值
                $existing->sort_order = $sortOrder;
                $existing->save();
            } else {
                // 新建记录
                FieldsOrder::create([
                    'content_type_id' => $contentTypeId,
                    'field_id' => $fieldId,
                    'field_type_id' => $fieldTypeId,
                    'sort_order' => $sortOrder,
                ]);
            }
        }

        return response()->json(['status' => 'success']);
    }


    public function allowedSearch(Request $request)
    {
        $validated = $request->validate([
            'content_type_id' => 'required|integer',
            'field_type_id'   => 'required|integer',
            'field_id'        => 'required|integer',
            'allowed'         => 'required|boolean',
        ]);

        if ($validated['allowed']) {
            // 插入或更新记录
            DB::table('allowed_search_fields')->updateOrInsert(
                [
                    'content_type_id' => $validated['content_type_id'],
                    'field_type_id' => $validated['field_type_id'],
                    'field_id' => $validated['field_id'],
                ],
                [
                    'updated_at' => now(),
                    'created_at' => now(), // 如果新增，设置创建时间
                ]
            );
        } else {
            // 取消允许搜索，删除记录
            DB::table('allowed_search_fields')
                ->where('content_type_id', $validated['content_type_id'])
                ->where('field_type_id', $validated['field_type_id'])
                ->where('field_id', $validated['field_id'])
                ->delete();
        }

        return response()->json(['success' => true]);
    }


    public function getAllowedSearchFields($content_type_id )
    {
        
        $result = [];
        $records = DB::table('allowed_search_fields')
            ->where('content_type_id', $content_type_id)
            ->get(['field_type_id', 'field_id']);

        // 组装成 "field_type_id-field_id" => true 形式的数组
        $result = [];
        foreach ($records as $record) {
            $key = $record->field_type_id . '-' . $record->field_id;
            $result[$key] = true;
        }

        return $result;
    }
    function decodeJsonFields($item)
    {
        foreach ($item as $key => $value) {
            if (is_string($value) && isJson($value)) {
                $decoded = json_decode($value, true);
                if (is_array($decoded)) {
                    $item->$key = $decoded;
                }
            }
        }
        return $item;
    }
    
    function isJson($string)
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }


    public function deleteField(Request $request)
    {
        // 获取参数
        $fieldTypeId = $request->input('field_type_id');
        $contentTypeId = $request->input('content_type_id');
        $fieldId = $request->input('field_id');
    
        // 获取字段类型记录
        $fieldType = DB::table('field_types')->where('id', $fieldTypeId)->first();
    
        if (!$fieldType) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid field type ID.'
            ], 400);
        }
    
        // 获取关联表名
        $relatedTable = $fieldType->related_table;
    
        if (!$relatedTable) {
            return response()->json([
                'success' => false,
                'message' => 'No related table defined for this field type.'
            ], 400);
        }
    
        // 拼接真实表名，防止 SQL 注入
        $tableName = 'field_' . preg_replace('/[^a-zA-Z0-9_]/', '', $relatedTable);
    
        // 删除记录
        DB::table($tableName)
            ->where('content_type_id', $contentTypeId)
            ->where('field_type_id', $fieldTypeId)
            ->where('id', $fieldId)
            ->delete();
            
        DB::table('fields_order')
            ->where('content_type_id', $contentTypeId)
            ->where('field_type_id', $fieldTypeId)
            ->where('field_id', $fieldId)
            ->delete();
    
        return response()->json([
            'success' => true,
            'message' => 'Field deleted successfully.'
        ]);
    }
    
    /**
     * Insert or update a field permission rule by id.
     *
     * @param  array  $params Expected keys:
     *                         - id (int, optional, 0 or missing means insert)
     *                         - field_id (int, required)
     *                         - content_type_id (int, required)
     *                         - field_type_id (int, required)
     *                         - action (array, required) e.g. ['schema'=>[], 'data'=>[]]
     *                         - user_id (int|null)
     *                         - group_id (int|null)
     * @return \App\Models\FieldPermissionRule
     * @throws \Illuminate\Validation\ValidationException
     */
    public function saveFieldPermissionRule(array $params)
    {
        $validator = Validator::make($params, [
            'id'               => 'sometimes|integer|min:0',
            'field_id'         => 'required|integer',
            'content_type_id'  => 'required|integer',
            'field_type_id'    => 'required|integer',
            'action'           => 'required|array',
            'action.schema'    => 'required|array',
            'action.data'      => 'required|array',
            'user_id'          => 'nullable|integer',
            'group_id'         => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $id             = $params['id'] ?? 0;
        $fieldId        = $params['field_id'];
        $contentTypeId  = $params['content_type_id'];
        $fieldTypeId    = $params['field_type_id'];
        $action         = $params['action'];
        $userId         = $params['user_id'] ?? null;
        $groupId        = $params['group_id'] ?? null;

        if ($id > 0) {
            // Update existing by id
            $rule = FieldPermissionRule::find($id);
            if (!$rule) {
                throw new \Exception("FieldPermissionRule with id {$id} not found.");
            }

            $rule->field_id         = $fieldId;
            $rule->content_type_id  = $contentTypeId;
            $rule->field_type_id    = $fieldTypeId;
            $rule->action           = $action;

            $rule->allowed_user_ids  = array_values(array_unique(
                array_merge($rule->allowed_user_ids ?? [], $userId ? [$userId] : [])
            ));

            $rule->allowed_group_ids = array_values(array_unique(
                array_merge($rule->allowed_group_ids ?? [], $groupId ? [$groupId] : [])
            ));

            $rule->save();
        } else {
            // Insert new record
            $rule = FieldPermissionRule::create([
                'field_id'          => $fieldId,
                'content_type_id'   => $contentTypeId,
                'field_type_id'     => $fieldTypeId,
                'action'            => $action,
                'allowed_user_ids'  => $userId  ? [$userId]  : [],
                'allowed_group_ids' => $groupId ? [$groupId] : [],
            ]);
        }

        return $rule;
    }


    /**
     * Get permission rules keys by filters.
     *
     * @param  int                $contentTypeId
     * @param  int                $fieldTypeId
     * @param  array|null          $userIds     Array of user IDs, optional
     * @param  array|null          $groupIds    Array of group IDs, optional
     * @param  int|null            $fieldId     Field ID, optional
     * @return array               List of strings formatted as "content_type_id-field_type_id-field_id"
     */
    public function getPermissionRuleKeys(
        int $contentTypeId,
        ?int $fieldTypeId = null,
        ?array $userIds = null,
        ?array $groupIds = null,
        ?int $fieldId = null
    ): array {
        $query = FieldPermissionRule::query();

        // Base filters
        $query->where('content_type_id', $contentTypeId);

        if ($fieldId !== null) {
            $query->where('field_id', $fieldId);
        }
        if ($fieldTypeId !== null) {
            $query->where('field_type_id', $fieldTypeId);
        }

        // User ID filter - any of the user_ids
        if (!empty($userIds)) {
            $query->where(function ($q) use ($userIds) {
                foreach ($userIds as $userId) {
                    $q->orWhereJsonContains('allowed_user_ids', $userId);
                }
            });
        }

        // Group ID filter - any of the group_ids
        if (!empty($groupIds)) {
            $query->where(function ($q) use ($groupIds) {
                foreach ($groupIds as $groupId) {
                    $q->orWhereJsonContains('allowed_group_ids', $groupId);
                }
            });
        }

        $rules = $query->get(['content_type_id', 'field_type_id', 'field_id']);

        // 格式化输出
        return $rules->map(function ($rule) {
            return "{$rule->content_type_id}-{$rule->field_type_id}-{$rule->field_id}";
        })->unique()->values()->all();
    }





}
