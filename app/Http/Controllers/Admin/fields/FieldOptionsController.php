<?php

namespace App\Http\Controllers\Admin\fields;

use App\Models\Fields\FieldOption;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;


class FieldOptionsController extends Controller
{
    public function modelClass(): string
    {
        return \App\Models\Fields\FieldOption::class;
    }



    /**
     * Create or update a field option.
     * If 'id' is present, update. Otherwise, insert.
     */
    public function rules(Request $request): array
    {
        
        return [
                'id' => 'nullable|integer',
                'label_json' => 'required|array',
                'display_style' => 'required|string|in:select,radio,checkbox',
                'options' => 'nullable|array',
                'options.*.label' => 'required_with:default_value|array',  // 多语言label，数组
                'options.*.label.*' => 'string',  // label各语言内容是字符串
                'options.*.value' => 'required_with:default_value|string',
                'options.*.enabled' => 'nullable|boolean',
                'options.*.sort_order' => 'nullable|integer',
                'max_select_count' => 'nullable|integer|min:0',
                'is_required' => 'nullable|boolean',
                'prefix_text_json' => 'nullable|array',
                'suffix_text_json' => 'nullable|array',
                'content_type_id' => 'required|integer',
                'field_type_id' => 'required|integer',
            ];


        
    }
    
    

  

    public function checkboxFields(): array
    {
        return [ 'is_required'];
    }
}
