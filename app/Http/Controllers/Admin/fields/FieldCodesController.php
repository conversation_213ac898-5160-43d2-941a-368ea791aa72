<?php

namespace App\Http\Controllers\Admin\fields;

use App\Models\Fields\FieldCode;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FieldCodesController extends Controller
{
    public function modelClass(): string
    {
        return \App\Models\Fields\FieldCode::class;
    }
    /**
     * Create or update a field code.
     */
    public function rules(Request $request): array
    {
        return [
            'id' => 'nullable|integer',
            'field_type_id' => 'required|integer',
            'content_type_id' => 'required|integer',
            'label_json' => 'required|array',
            'html_code_json' => 'required|array',
            'description_json' => 'nullable|array',
            'is_enabled' => 'boolean',
            // 'variables' => 'nullable|array',
        ];
    }


    public function checkboxFields(): array
    {
        return [ 'is_enabled'];
    }
}
