<?php

namespace App\Http\Controllers\Admin\fields;

use App\Http\Controllers\Controller;
use App\Models\FieldSubtypeLabel;
use Illuminate\Http\Request;

class FieldSubtypeLabelController extends Controller
{
    // 列出所有 subtype 标签
    public function index()
    {
        return response()->json(FieldSubtypeLabel::all());
    }

    // 创建新的 subtype 标签
    public function store(Request $request)
    {
        $validated = $request->validate([
            'subtype_code' => 'required|string|unique:field_subtype_labels,subtype_code',
            'label_json' => 'required|json',
        ]);

        $item = FieldSubtypeLabel::create($validated);

        return response()->json($item, 201);
    }

    // 显示指定 subtype 标签
    public function show($id)
    {
        $item = FieldSubtypeLabel::findOrFail($id);
        return response()->json($item);
    }

    // 更新指定 subtype 标签
    public function update(Request $request, $id)
    {
        $item = FieldSubtypeLabel::findOrFail($id);

        $validated = $request->validate([
            'subtype_code' => 'sometimes|string|unique:field_subtype_labels,subtype_code,' . $id,
            'label_json' => 'sometimes|json',
        ]);

        $item->update($validated);

        return response()->json($item);
    }

    // 删除指定 subtype 标签
    public function destroy($id)
    {
        $item = FieldSubtypeLabel::findOrFail($id);
        $item->delete();

        return response()->json(null, 204);
    }
}
