<?php

namespace App\Http\Controllers\Admin\fields;

use App\Http\Controllers\Controller;
use App\Models\Fields\FieldText;
use Illuminate\Http\Request;
use App\Rules\DefaultLangJsonNotEmpty;

class FieldTextController extends Controller
{
    public function modelClass(): string
    {
        return \App\Models\Fields\FieldText::class;
    }





  

    public function rules(Request $request): array
    {

        return [
            'id' => 'nullable|integer',
            'field_type_id' => 'required|integer',
            'content_type_id' => 'required|integer',
            'subtype' => 'required|string',
            'display_color' => 'nullable|string',
            'label_json' => ['required', 'array', new DefaultLangJsonNotEmpty()],
            'placeholder_json' => 'nullable|array',
            'default_value_json' => 'nullable|array',
            'prefix_text_json' => 'nullable|array',
            'suffix_text_json' => 'nullable|array',
            'help_text_json' => 'nullable|array',
            'is_required' => 'nullable|boolean',
            'min_length' => 'nullable|integer',
            'max_length' => 'nullable|integer',
            'is_seo' => 'nullable|boolean',
            'seo_type' => 'nullable|string|in:title,meta_description,keywords,slug',
        ];
    }

    public function checkboxFields(): array
    {
        return ['is_seo', 'is_required'];
    }
}
