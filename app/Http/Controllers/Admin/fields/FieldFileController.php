<?php

namespace App\Http\Controllers\Admin\fields;

use App\Models\Fields\FieldFile;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FieldFileController extends Controller
{
    public function modelClass(): string
    {
        return \App\Models\Fields\FieldFile::class;
    }


    public function rules(Request $request): array
    {
        return [
            'id' => 'nullable|integer',
            'content_type_id' => 'required|integer',
            'field_type_id' => 'required|integer',
            'label_json' => 'required|array',
            'help_text_json' => 'nullable|array',
            'is_required' => 'boolean',
            'allowed_file_types' => 'nullable|string',
            'max_upload_count' => 'nullable|integer|min:1',
            'max_file_size_mb' => 'nullable|integer|min:1',
            'enable_image_preview' => 'boolean',
            'rename_on_upload' => 'required|in:original,uuid,timestamp',
            'display_as_gallery' => 'boolean',
            'auto_compress_images' => 'boolean',
        ];
    }
    

    public function checkboxFields(): array
    {
        return [ 's_required','enable_image_preview','auto_compress_images','display_as_gallery'];
    }
    //s_required,enable_image_preview,auto_compress_images,display_as_gallery
}
