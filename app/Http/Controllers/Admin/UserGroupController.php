<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use App\Models\KazcmsLanguage;

class UserGroupController extends Controller
{
    /**
     * 显示用户组列表
     */
    public function index()
    {
        $groups = UserGroup::orderBy('sort_order')->get()->keyBy('id');

        $sorted = [];
    
        $addChildren = function ($parentId) use (&$addChildren, $groups, &$sorted) {
            foreach ($groups as $group) {
                if ($group->parent_id == $parentId) {
                    $sorted[] = $group;
                    $addChildren($group->id);
                }
            }
        };
    
        $addChildren(0);
    
        return view('admin.user_groups.index', ['groups' => $sorted]);
    }

    /**
     * 显示新增/编辑表单
     * 
     * @param int|null $id
     */

    public function form(?string $id = null)
    {
        $group = $id ? UserGroup::findOrFail($id) : new UserGroup();
    
        // 编辑时排除自身，用于父组选择
        $allGroups = UserGroup::when($id, fn ($q) => $q->where('id', '!=', $id))
            ->orderBy('path')->get();
    
        // 取启用语言
        $languages = KazcmsLanguage::where('enabled', true)->orderBy('sort_order')->get();
        return view('admin.user_groups.form', compact('group', 'allGroups', 'languages'));
    }
    

    /**
     * 新增或更新用户组
     * 判断 $request->id 是否存在来区分新增或更新
     */
    public function save( Request $request)
    {
        $data = $request->validate([
            'id' => 'nullable|integer|exists:user_groups,id',
            'name' => 'required|array',
            'name.*' => 'required|string|max:60',
            'parent_id' => 'nullable|integer',
            'sort_order' => 'nullable|integer',
        ]);

        $id = $data['id'] ?? null;

        if ($id) {
            // 更新
            $group = UserGroup::findOrFail($id);
        } else {
            // 新增
            $group = new UserGroup();
        }

        // 检查父组是否有效（防止循环）
        if ($this->isInvalidParent($data['parent_id'] ?? 0, $id)) {
            return back()->withErrors('Invalid parent group selected')->withInput();
        }

        // 赋值
        $group->name = $data['name'];
        $group->parent_id = $data['parent_id'] ?? 0;
        $group->sort_order = $data['sort_order'] ?? 0;

        // 保存，生成 id
        $group->save();

        // 更新 path 和 level
        $this->updateGroupPathLevel($group);

        return redirect(localized_route('admin.user_groups.index'))
            ->with('success', $id ? 'User group updated successfully.' : 'User group created successfully.');

    }

    /**
     * 删除用户组
     * 
     * @param int $id
     */
    public function destroy(int $id)
    {
        $group = UserGroup::findOrFail($id);

        if ($group->children()->count() > 0) {
            return back()->withErrors('Cannot delete a group with child groups.');
        }

        if ($group->users()->count() > 0) {
            return back()->withErrors('Cannot delete a group with associated users.');
        }

        $group->delete();

        return redirect()->route('admin.user_groups.index')->with('success', 'User group deleted.');
    }

    /**
     * 验证父组是否有效，防止选自己或子孙作为父组导致循环
     * 
     * @param int|null $parentId
     * @param int|null $currentId
     * @return bool true=非法，false=合法
     */
    protected function isInvalidParent(?int $parentId, ?int $currentId): bool
    {
        if (!$parentId || $parentId === 0) {
            return false;
        }
        if ($currentId && $parentId == $currentId) {
            return true;
        }

        $parentGroup = UserGroup::find($parentId);
        if (!$parentGroup) {
            return true;
        }

        // 检查path，path格式 |父ID|...|当前ID|
        if ($currentId && strpos($parentGroup->path, "|{$currentId}|") !== false) {
            return true;
        }

        return false;
    }

    /**
     * 更新用户组 path 和 level 字段
     * 
     * @param UserGroup $group
     * @return void
     */
    protected function updateGroupPathLevel(UserGroup $group): void
    {
        if ($group->parent_id == 0) {
            $group->path = '|' . $group->id . '|';
            $group->level = 0;
        } else {
            $parent = UserGroup::find($group->parent_id);
            $group->path = $parent->path . $group->id . '|';
            $group->level = $parent->level + 1;
        }
        $group->save();

        // 更新父组的 is_leaf 字段
        if ($group->parent_id > 0) {
            $parent = UserGroup::find($group->parent_id);
            if ($parent->is_leaf) {
                $parent->is_leaf = 0;
                $parent->save();
            }
        }
    }
}
