<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\Field;
use App\Models\ContentType;
use App\Models\Fields\FieldsOrder;
use App\Models\Fields\FieldType;
use App\Models\Fields\FieldSubtypeLabel;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Support\Str;
use App\Http\Controllers\Admin\fields\FieldsController;
use App\Http\Controllers\Admin\AttachmentController;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Admin\CategoryController;
use App\Services\ContentDataService;

class FieldSubmitController extends Controller
{
    public function submit($content_type_id, $data_id = 0)
    {
        // 获取字段列表
        $fc = app(FieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($content_type_id);

        $data = [];
        if($data_id){
            $data = $this->filterFieldData($fieldsResponse, $data_id);
        }

        //print_r($data);exit();

        // 获取字段类型数据
        $fieldTypes = FieldType::all()->keyBy('id');

        $fields = $fieldsResponse->getData(); // JsonResponse 转为对象数组

        // 获取内容类型数据
        $contentType = ContentType::find($content_type_id);

        // 返回 Blade 视图

        //get allowed search fields

        $allowedSearchFields = $fc->getAllowedSearchFields($content_type_id);

        return view('admin.fields.submit', [
            'fields' => $fields,
            'content_type_id' => $content_type_id,
            'content_type' => $contentType,
            'fieldTypes' => $fieldTypes,
            'data_id' => $data_id,
            'data' => $data,
            'allowedSearchFields' => $allowedSearchFields,
        ]);
    }
    public function del_save(Request $request)
    {
        dd($request->all());
        //dd($_FILES);
        exit();
        $request->validate([
            'content_type_id' => 'required|integer',
            'data_id' => 'required|integer',
        ]);

        $contentTypeId = $request->input('content_type_id');
        $dataId = $request->input('data_id');

        // 获取字段列表
        $fc = app(FieldsController::class);
        $fieldsResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId);

        $fields = $fieldsResponse->getData(); // JsonResponse 转为对象数组
    }

    public function save(Request $request)
    {

        
    $input = $request->all();
    if ($this->isFormDataEmpty($input)) {
        return redirect()->back()->withInput()->withErrors(['msg' => '请至少填写一个字段内容']);
    }
        $dataId = app(ContentDataService::class)->saveOrUpdateFields($request);
        return view('admin.fields.data_save_success', [
            'content_type_id' => $request->input('content_type_id'),
            'success' => 'Data saved successfully.',
            'data_id' => $dataId
        ]);
    }
    


    



    /**
     * Get only those field‑data rows that match the given $fieldsResponse list.
     *
     * @param  \Illuminate\Http\JsonResponse  $fieldsResponse
     * @param  int                            $dataId
     * @return array                          e.g. ['data_3_7' => [...], 'data_1_19' => [...]]
     */
   /**
 * Return field‑data values for $dataId, limited to $fieldsResponse,
 * and bundle all attachments (field_type_id == 3) under "attachments".
 *
 * @param  \Illuminate\Http\JsonResponse  $fieldsResponse
 * @param  int                            $dataId
 * @return array
 */
public function filterFieldData(JsonResponse $fieldsResponse, int $dataId): array
{
    // ---------- 1. build allowed lookup ----------
    $fields  = collect($fieldsResponse->original ?? $fieldsResponse->getData(true));
   
    $allowed = $fields->map(fn ($f) =>
        ($f->content_type_id ?? $f['content_type_id']) . '-' .
        ($f->field_type_id   ?? $f['field_type_id'])   . '-' .
        ($f->id              ?? $f['id'])
    )->unique()->flip();
    if ($allowed->isEmpty()) {
        return [];
    }

    // ---------- 2. query once ----------
    $rows = DB::table('field_data')
        ->where('data_id', $dataId)
        ->get();

    // ---------- 3. build result ----------
    $result         = [];
    $attachmentIds  = [];   // ① 收集附件 ID
    $categoryIds    = [];   // ② 收集分类 ID
    foreach ($rows as $row) {
        $triplet = "{$row->content_type_id}-{$row->field_type_id}-{$row->field_id}";

        if (!$allowed->has($triplet)) {
            continue;
        }

        $key      = "fields_{$row->field_type_id}_{$row->field_id}";
        $decoded  = json_decode($row->data, true);

        // ---- A. 附件：field_type_id == 3 ----
        if ($row->field_type_id == 3 && is_array($decoded)) {
            foreach ($decoded as $langVal) {
                $ids = is_array($langVal) ? $langVal : [$langVal];
                $attachmentIds = array_merge($attachmentIds, $ids);
            }
        }

        // ---- B. 分类：field_type_id == 4 ----
        if ($row->field_type_id == 4 && is_array($decoded)) {
            foreach ($decoded as $langVal) {
                $ids = is_array($langVal) ? $langVal : [$langVal];
                $categoryIds = array_merge($categoryIds, $ids);
            }
        }

        $result[$key] = $decoded;
    }

    // ---------- 4. fetch attachments ----------
    $attachmentIds = array_values(array_unique($attachmentIds));
    if ($attachmentIds) {
        $attachCtrl = app(AttachmentController::class);
        $result['attachments'] = $attachCtrl->fetchAttachmentsByIds($attachmentIds);
    }

    // ---------- 5. fetch categories ----------
    $categoryIds = array_values(array_unique($categoryIds));
    if ($categoryIds) {
        $catResponse = app(CategoryController::class)->getCategoriesByIds($categoryIds);

        // 如果 getCategoriesByIds 返回 JsonResponse
        $result['categories'] = $catResponse->getData(true);
    }
    return $result;
}

/**
 * 判断提交字段数组是否都是空值
 * @param array $input 所有请求参数
 * @return bool true = 全空，false = 有内容
 */
function isFormDataEmpty(array $input): bool
{
    foreach ($input as $key => $value) {
        if (!str_starts_with($key, 'fields_') &&
            !str_starts_with($key, 'existing_images_fields_') &&
            !str_starts_with($key, 'image_order_fields_')) {
            continue; // 跳过非字段数据
        }

        if (is_null($value)) {
            continue; // null算空
        }

        if (is_array($value)) {
            $allLangEmpty = true;
            foreach ($value as $langVal) {
                if (is_null($langVal)) {
                    continue;
                }
                if (is_string($langVal) && (trim($langVal) === '' || $langVal === '[]')) {
                    continue;
                }
                if (is_array($langVal) && count($langVal) === 0) {
                    continue;
                }
                // 发现非空内容
                $allLangEmpty = false;
                break;
            }
            if (!$allLangEmpty) {
                return false;
            }
            // 如果全部语言都空，继续检查下一个字段
            continue;
        }

        if (is_string($value) && trim($value) !== '' && $value !== '[]') {
            return false; // 字符串非空且非空数组字符串
        }
    }
    return true; // 都是空
}


}