<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DocumentContent;
use Illuminate\Http\Request;
use App\Models\Fields\FieldData;
use App\Models\DocumentContentVersion;

class DocumentContentController extends Controller
{
    /**
     * 显示指定 content_type_id 和 data_id 的文档章节树及默认编辑区
     * URL: GET /admin/document-content/{content_type_id}/{data_id}?chapter_id=...
     */
    public function index(Request $request, $content_type_id, $data_id)
    {
        $chapterId = $request->query('chapter_id', 0);
        $versionId = $request->query('version', 0);

        // 获取章节树（只顶层+递归子章节）
        $fieldData = FieldData::where('data_id', 0)
        ->where('content_type_id', $content_type_id)
        ->where('id', $data_id)
        ->first();
        $expandLevel = 3; // 默认展开一级

        if ($fieldData && $fieldData->extra_config) {
            $config = json_decode($fieldData->extra_config, true);
            if (isset($config['expand_level']) && is_numeric($config['expand_level'])) {
                $expandLevel = (int) $config['expand_level'];
            }
        }

        $tree = DocumentContent::getDocumentTreeFlat($data_id, $expandLevel,$versionId);

        // 默认选中章节内容，如果 chapter_id=0，右侧编辑区为空
        
        $chapter = null;
        if ($chapterId && $chapterId != 0) {
            $chapter = DocumentContent::find($chapterId);
        }
        $versions = DocumentContentVersion::select('id', 'version', 'is_active')
        ->where('content_type_id', $content_type_id)
        ->where('data_id', $data_id)
        ->orderByDesc('version')
        ->get();

        return view('admin.document_content.index', compact('content_type_id', 'data_id', 'tree', 'chapter', 'chapterId', 'versions','versionId'));
    }

    /**
     * AJAX: 创建新章节节点
     * POST /admin/document-content/{content_type_id}/{data_id}/chapter
     * 需要传入 name(多语言json), slug(多语言json), parent_id, show_order, final_category
     */
    public function updateChapterFields(Request $request)
    {
        $data = $request->validate([
            'id' => 'required|integer',
            'content_type_id' => 'required|integer',
            'data_id' => 'required|integer',
            'update_fields' => 'required|array',
            'version_id' => 'required|integer',
            'title' => 'nullable|array',
            'slug' => 'nullable|array',
            'parent_id' => 'required|integer',
            'show_order' => 'nullable|integer',
            'content' => 'nullable|array',
            'is_draft' => 'nullable|boolean',
        ]);
    
        if (empty($data['id']) || $data['id'] == 0) {
            // 新建章节
            $chapter = new DocumentContent();
            $chapter->content_type_id = $data['content_type_id'];
            $chapter->data_id = $data['data_id'];
            $chapter->parent_id = $data['parent_id'] ?? 0;
            $chapter->is_draft = $data['is_draft'] ?? false;
            $chapter->version_id = $data['version_id'] ?? 0;
    
            // 计算层级与路径
            list($chapter->level, $chapter->parent_path_json) = $this->calcLevelAndPath($chapter->parent_id);
        } else {
            // 更新章节
            $chapter = DocumentContent::find($data['id']);
            if (!$chapter) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chapter not found',
                ]);
            }
        }
    
        $fields = $data['update_fields'];
    
        if (in_array('title', $fields) && isset($data['title'])) {
            $chapter->name = $data['title'];
        }
    
        if (in_array('slug', $fields) && isset($data['slug'])) {
            $chapter->slug = $data['slug'];
        }
    
        if (in_array('show_order', $fields) && isset($data['show_order'])) {
            $chapter->show_order = $data['show_order'];
        }
    
        if (in_array('content', $fields) && isset($data['content'])) {
            $chapter->content = $data['content'];
        }
    
        if (in_array('is_draft', $fields)) {
            $chapter->is_draft = $data['is_draft'] ?? false;
        }
    
        if (in_array('parent_id', $fields)) {
            $oldParentId = $chapter->parent_id;
            $newParentId = $data['parent_id'] ?? 0;
            $chapter->parent_id = $newParentId;
    
            // 计算层级与路径
            list($chapter->level, $chapter->parent_path_json) = $this->calcLevelAndPath($newParentId);
    
            // 更新旧父类final_category
            if ($oldParentId !== $newParentId && $oldParentId > 0) {
                $siblingsCount = DocumentContent::where('parent_id', $oldParentId)
                    ->where('id', '!=', $chapter->id)
                    ->count();
                if ($siblingsCount === 0) {
                    DocumentContent::where('id', $oldParentId)->update(['final_category' => 0]);
                }
            }
    
            // 更新新父类final_category
            if ($newParentId > 0) {
                DocumentContent::where('id', $newParentId)->update(['final_category' => 1]);
            }
        }
    
        // 判断是否有子章节，设置 final_category
        $hasChildren = DocumentContent::where('parent_id', $chapter->id)->exists();
        $chapter->final_category = $hasChildren ? 1 : 0;
    
        $chapter->save();
    
        return response()->json([
            'success' => true,
            'message' => (empty($data['id']) || $data['id'] == 0) ? 'Chapter created successfully.' : 'Chapter updated successfully.',
            'chapter_id' => $chapter->id,
        ]);
    }
    
    
    
    private function calcLevelAndPath($parentId)
    {
        if ($parentId > 0) {
            $parent = DocumentContent::find($parentId);
            if ($parent) {
                $level = $parent->level + 1;
                $path = $parent->parent_path_json ?? [];
                $path[] = $parent->id;
                return [$level, $path];
            }
        }
        return [1, []];
    }
    
    
    

    /**
     * AJAX: 读取章节内容
     * GET /admin/document-content/{content_type_id}/{data_id}/chapter?chapter_id=...
     */
    public function readChapter(Request $request, $content_type_id, $data_id)
    {
        $chapterId = $request->query('chapter_id');
        if (!$chapterId) {
            return response()->json(['error' => 'chapter_id required'], 400);
        }

        $chapter = DocumentContent::find($chapterId);
        if (!$chapter || $chapter->data_id != $data_id) {
            return response()->json(['error' => 'Chapter not found'], 404);
        }

        return response()->json([
            'id' => $chapter->id,
            'name' => $chapter->name,
            'slug' => $chapter->slug,
            'content' => $chapter->content,
            'parent_id' => $chapter->parent_id,
            'show_order' => $chapter->show_order,
            'final_category' => $chapter->final_category,
        ]);
    }

    /**
     * AJAX: 更新章节内容
     * POST /admin/document-content/{content_type_id}/{data_id}/chapter/{chapter_id}
     */
    public function updateChapter(Request $request, $content_type_id, $data_id, $chapter_id)
    {
        $chapter = DocumentContent::find($chapter_id);
        if (!$chapter || $chapter->data_id != $data_id) {
            return response()->json(['error' => 'Chapter not found'], 404);
        }

        $data = $request->validate([
            'name' => 'required|json',
            'slug' => 'required|json',
            'content' => 'nullable|json',
            'parent_id' => 'nullable|integer',
            'show_order' => 'nullable|integer',
            //'final_category' => 'nullable|boolean',
        ]);

        $chapter->name = json_decode($data['name'], true);
        $chapter->slug = json_decode($data['slug'], true);
        $chapter->content = isset($data['content']) ? json_decode($data['content'], true) : $chapter->content;
        $chapter->show_order = $data['show_order'] ?? $chapter->show_order;
        //$chapter->final_category = $data['final_category'] ?? $chapter->final_category;

        if (isset($data['parent_id']) && $data['parent_id'] != $chapter->parent_id) {
            $parent = DocumentContent::find($data['parent_id']);
            $chapter->parent_id = $data['parent_id'];
            if ($parent) {
                $chapter->level = $parent->level + 1;
                $path = $parent->parent_path_json ?? [];
                $path[] = $parent->id;
                $chapter->parent_path_json = $path;
            } else {
                $chapter->level = 1;
                $chapter->parent_path_json = [];
            }
        }

        $chapter->save();

        return response()->json([
            'success' => true,
            'message' => 'Chapter updated successfully',
        ]);
    }

    /**
     * AJAX: 删除章节
     * DELETE /admin/document-content/{content_type_id}/{data_id}/chapter/{chapter_id}
     */
    public function deleteChapter(Request $request, $content_type_id, $data_id, $chapter_id)
    {
        $chapter = DocumentContent::find($chapter_id);
        if (!$chapter || $chapter->data_id != $data_id) {
            return response()->json(['error' => 'Chapter not found'], 404);
        }

        // 递归删除所有子章节
        $this->deleteChapterRecursive($chapter);

        return response()->json([
            'success' => true,
            'message' => 'Chapter deleted successfully',
        ]);
    }

    /**
     * 递归删除章节和所有子章节
     */
    protected function deleteChapterRecursive(DocumentContent $chapter)
    {
        foreach ($chapter->children as $child) {
            $this->deleteChapterRecursive($child);
        }
        $chapter->delete();
    }


    public function updateOrderSingle(Request $request, $content_type_id, $data_id)
    {
        $data = $request->validate([
            'id' => 'required|integer',
            'parent_id' => 'required|integer',
            'previous_sibling_id' => 'nullable|integer',
        ]);

        $chapter = DocumentContent::where('id', $data['id'])->where('data_id', $data_id)->firstOrFail();

        // 先更新 parent_id
        $chapter->parent_id = $data['parent_id'];

        // 计算新的 level 和 parent_path_json
        if ($chapter->parent_id > 0) {
            $parent = DocumentContent::find($chapter->parent_id);
            $chapter->level = $parent ? $parent->level + 1 : 1;
            $chapter->parent_path_json = $parent ? array_merge($parent->parent_path_json ?? [], [$parent->id]) : [];
        } else {
            $chapter->level = 1;
            $chapter->parent_path_json = [];
        }

        $chapter->save();

        // 更新同级排序
        // 1. 获取所有同级且不包括当前节点的兄弟节点，按 show_order 升序
        $siblings = DocumentContent::where('data_id', $data_id)
            ->where('parent_id', $chapter->parent_id)
            ->where('id', '!=', $chapter->id)
            ->orderBy('show_order')
            ->get();

        $newOrder = 0;
        $updatedChapters = [];

        foreach ($siblings as $sibling) {
            if ($sibling->id == $data['previous_sibling_id']) {
                // 放当前节点紧跟它后面
                $newOrder++;
                $chapter->show_order = $newOrder;
                $chapter->save();

                $newOrder++;
                $sibling->show_order = $newOrder;
                $sibling->save();
            } else {
                $newOrder++;
                // 如果已经给当前节点赋order，后续兄弟节点顺延
                if ($chapter->show_order && $newOrder == $chapter->show_order) {
                    $newOrder++;
                }
                $sibling->show_order = $newOrder;
                $sibling->save();
            }
        }

        // 如果 previous_sibling_id 不在 siblings 中（比如是第一个），把当前节点放在第一
        if (!$chapter->show_order) {
            // 放最前面，其他兄弟顺延
            $chapter->show_order = 0;
            $chapter->save();

            $order = 1;
            foreach ($siblings as $sibling) {
                $sibling->show_order = $order++;
                $sibling->save();
            }
        }

        return response()->json(['success' => true]);
    }
    public function saveUserConfig(Request $request, $fieldDataId)
    {
        $config = $request->validate([
            'default_expanded' => 'required|boolean',
        ]);

        $fieldData = FieldData::findOrFail($fieldDataId);
        $extra = $fieldData->extra_config ? json_decode($fieldData->extra_config, true) : [];
        $extra['default_expanded'] = $config['default_expanded'];
        $fieldData->extra_config = json_encode($extra);
        $fieldData->save();

        return response()->json(['success' => true]);
    }


    public function getDocumentContentTreeByDataId(
        int $dataId,
        int $depth = 1,
        bool $onlyExactLevel = false,
        array $selectFields = ['id', 'title', 'slug', 'parent_id', 'level', 'data_id', 'final_chapter'],
        ?string $lang = null
    ) {
        // 获取该文档下的所有目录项
        $allContents = DocumentContent::where('data_id', $dataId)
            ->select($selectFields)
            ->get();
    
        if ($allContents->isEmpty()) {
            return response()->json([]);
        }
    
        // 多语言处理
        if ($lang) {
            $allContents->transform(function ($item) use ($lang) {
                foreach ($item->getAttributes() as $key => $value) {
                    if (
                        is_string($value) &&
                        json_decode($value) !== null &&
                        json_last_error() === JSON_ERROR_NONE
                    ) {
                        $decoded = json_decode($value, true);
                        $item->$key = $decoded[$lang] ?? null;
                    }
                }
                return $item;
            });
        }
    
        // 按 parent_id 分组
        $contentsByParent = $allContents->groupBy('parent_id');
    
        // 获取顶层节点（parent_id 为空或为 0）
        $rootItems = $contentsByParent[null] ?? $contentsByParent[0] ?? collect();
    
        $result = collect();
    
        foreach ($rootItems as $item) {
            if ($depth === 0 || $depth === 1 || !$onlyExactLevel) {
                $result->push($item);
            }
    
            // 递归获取子章节
            $children = $this->collectSubtreeChapters(
                $item->id,
                $contentsByParent,
                $depth === 0 ? null : $depth,
                $onlyExactLevel,
                2 // level 2 for children
            );
    
            $result = $result->merge($children);
        }
    
        return response()->json($result->unique('id')->values());
    }
    
    protected function collectSubtreeChapters(
        int $parentId,
        $contentsByParent,
        int $maxDepth,
        bool $onlyExactLevel,
        int $currentLevel
    ) {
        if ((!is_null($maxDepth) && $currentLevel > $maxDepth) || !isset($contentsByParent[$parentId])) {
            return collect();
        }

        $collected = collect();

        foreach ($contentsByParent[$parentId] as $child) {
            if (!$onlyExactLevel || ($onlyExactLevel && $currentLevel === $maxDepth)) {
                $collected->push($child);
            }

            // Recurse into children
            $collected = $collected->merge(
                $this->collectSubtreeChapters(
                    $child->id,
                    $contentsByParent,
                    $maxDepth,
                    $onlyExactLevel,
                    $currentLevel + 1
                )
            );
        }

        return $collected;
    }
}
