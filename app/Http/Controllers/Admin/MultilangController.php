<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MultilangController extends Controller
{
    // 显示前端页面
    public function showCheckerPage($checkerId)
    {
        $checker = DB::table('multilang_checker')->where('id', $checkerId)->first();
        if (!$checker) abort(404, 'Checker not found');

        $langs = getActiveLanguages(); // 返回语言列表（含 code、name）
        $defaultLang = getDefaultLanguage()->code ?? 'en';

        return view('admin.multilang.checker', compact('checker', 'langs', 'defaultLang'));
    }

    // 后端扫描接口
    public function scanMissingTranslations(Request $request, $checkerId)
    {
        $sourceLang = $request->input('source_lang') ?? getDefaultLanguage()->code;

        $checker = DB::table('multilang_checker')->where('id', $checkerId)->first();
        if (!$checker) return response()->json(['error' => 'Invalid checker ID'], 404);

        $tableName     = $checker->table_name;
        $jsonColumn    = $checker->json_column_name;
        $tableIdColumn = $checker->table_id;

        $langs = getActiveLanguages()->pluck('code')->toArray();
        $rows = DB::table($tableName)->select($tableIdColumn, $jsonColumn)->get();

        $missing = [];

        foreach ($rows as $row) {
            $id = $row->{$tableIdColumn};
            $json = json_decode($row->{$jsonColumn}, true);
            if (!is_array($json)) $json = [];

            foreach ($langs as $lang) {
                if ($lang === $sourceLang) continue;
                if (empty($json[$lang])) {
                    $sourceText = $json[$sourceLang] ?? '';
                    if ($sourceText) {
                        $missing[] = [
                            'checker_id' => $checker->id,
                            'id'         => $id,
                            'lang'       => $lang,
                            'text'       => $sourceText,
                        ];
                    }
                }
            }
        }

        return response()->json([
            'missing' => $missing,
            'total_missing' => count($missing),
        ]);
    }

    public function showBulkCheckerPage()
{
    $langs = getActiveLanguages();
    $defaultLang = getDefaultLanguage()->code ?? 'en';
    $checkers = DB::table('multilang_checker')->orderBy('table_name')->get();
    $locale = app()->getLocale();
    // 按 table_name 分组
    $groupedCheckers = $checkers->groupBy('table_name');
    
    return view('admin.multilang.checker', [
        'groupedCheckers' => $groupedCheckers, 
        'langs' => $langs, 
        'defaultLang' => $defaultLang,
        'locale' => $locale,
    ]);
}

public function bulkScan(Request $request)
{
    $sourceLang = $request->input('source_lang') ?? getDefaultLanguage()->code;
    $targetLangs = $request->input('target_langs', []);
    $checkerIds = $request->input('checker_ids', []);

    $result = [];

    foreach ($checkerIds as $checkerId) {
        $checker = DB::table('multilang_checker')->where('id', $checkerId)->first();
        if (!$checker) continue;

        $table = $checker->table_name;
        $column = $checker->json_column_name;
        $idField = $checker->table_id;

        if (!\Schema::hasTable($table)) continue;

        $rows = DB::table($table)->select($idField, $column)->get();
        $missing = [];
        $sourceMissing = [];

        foreach ($rows as $row) {
            $id = $row->{$idField};
            $json = json_decode($row->{$column}, true);
            if (!is_array($json)) $json = [];

            if (empty($json[$sourceLang])) {
                $sourceMissing[] = [
                    'id' => $id,
                    'lang' => $sourceLang,
                    'note' => '源语言缺失，跳过目标语言检测'
                ];
                continue;
            }

            foreach ($targetLangs as $lang) {
                if ($lang === $sourceLang) continue;
                if (empty($json[$lang])) {
                    $missing[] = [
                        'checker_id' => $checkerId,  // 加上checker_id
                        'id' => $id,
                        'lang' => $lang,
                        'text' => $json[$sourceLang],
                    ];
                }
            }
        }

        $result[] = [
            'checker_id' => $checkerId,
            'table' => $table,
            'column' => $column,
            'source_missing' => $sourceMissing,
            'source_missing_count' => count($sourceMissing),
            'missing' => $missing,
            'missing_count' => count($missing),
        ];
    }

    return response()->json(['data' => $result]);
}
public function saveTranslations(Request $request)
{
    $translations = $request->input('translations', []);

    foreach ($translations as $item) {
        // 必须包含checker_id, record_id, lang_code, text
        if (!isset($item['checker_id'], $item['record_id'], $item['lang_code'], $item['text'])) {
            continue;  // 跳过无效数据
        }

        $checkerId = $item['checker_id'];
        $recordId = $item['record_id'];
        $langCode = $item['lang_code'];
        $text = $item['text'];

        $checker = DB::table('multilang_checker')->where('id', $checkerId)->first();
        if (!$checker) {
            continue;
        }

        $table = $checker->table_name;
        $column = $checker->json_column_name;
        $idField = $checker->table_id;

        if (!\Schema::hasTable($table)) {
            continue;
        }

        $row = DB::table($table)->where($idField, $recordId)->first([$idField, $column]);
        if (!$row) {
            continue;
        }

        $json = json_decode($row->{$column}, true);
        if (!is_array($json)) {
            $json = [];
        }

        // 更新指定语言字段
        $json[$langCode] = $text;

        // 保存回数据库
        DB::table($table)
            ->where($idField, $recordId)
            ->update([
                $column => json_encode($json, JSON_UNESCAPED_UNICODE),
            ]);
    }

    return response()->json(['success' => true, 'message' => '翻译保存成功']);
}


}
