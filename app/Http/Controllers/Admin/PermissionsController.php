<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PermissionsController extends Controller
{
    /**
     * Show permission assignment form for user or group
     */
    public function showAssignForm($type, $ids)
    {
        if (!in_array($type, ['group', 'user'])) {
            abort(400, 'Invalid assignee type.');
        }

        $ids = explode(',', $ids);
        $firstId = $ids[0] ?? null;

        $permissions = DB::table('permissions')->orderBy('id')->get();

        $assigned = DB::table('permission_assignments')
            ->where('assignee_type', $type)
            ->whereIn('assignee_id', $ids)
            ->pluck('permission_id')
            ->toArray();

        return view('admin.permissions.assign', [
            'permissions' => $permissions,
            'assigned' => $assigned,
            'assignee_ids' => $ids,
            'assignee_type' => $type,
            'assignee_id' => $firstId,
        ]);
    }

    /**
     * Save assigned permissions
     */
    public function saveAssignments(Request $request)
    {
        $request->validate([
            'assignee_type' => 'required|in:group,user',
            'assignee_id' => 'required',
            'permission_ids' => 'array',
        ]);

        $type = $request->input('assignee_type');
        $ids = explode(',', $request->input('assignee_id')); // 支持多个 ID
        $permissions = $request->input('permission_ids', []);
        $uid = Auth::id();

        DB::transaction(function () use ($type, $ids, $permissions, $uid) {
            foreach ($ids as $id) {
                DB::table('permission_assignments')
                    ->where('assignee_type', $type)
                    ->where('assignee_id', $id)
                    ->delete();

                if (!empty($permissions)) {
                    $data = array_map(fn ($pid) => [
                        'assignee_type' => $type,
                        'assignee_id' => $id,
                        'permission_id' => $pid,
                        'created_by' => $uid,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ], $permissions);

                    DB::table('permission_assignments')->insert($data);
                }
            }
        });

        return response()->json(['status' => 'ok']);
    }
}
