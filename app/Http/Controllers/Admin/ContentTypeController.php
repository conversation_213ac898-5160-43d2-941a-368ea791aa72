<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ContentTypeController extends Controller
{
    // 列表展示
    public function index()
    {
        $contentTypes = ContentType::orderBy('display_order')->paginate(20);
        return view('admin.content_types.index', compact('contentTypes'));
    }

    // 添加/编辑页面（如果id传了就是编辑）
    public function edit($id = null)
    {
        $contentType = $id ? ContentType::findOrFail($id) : new ContentType();
        return view('admin.content_types.edit', compact('contentType'));
    }

    // 保存（添加或更新）

    public function save(Request $request, $id = null)
    {
        $languages = getActiveLanguages()->pluck('code')->toArray();
        $locale = app()->getLocale();
    
        $rules = [
            'name' => ['required', 'array'],
            'slug' => ['required', 'array'],
            'display_order' => ['nullable', 'integer', 'min:0'],
            'show_on_frontend' => ['sometimes', 'boolean'],
            'has_form' => ['sometimes', 'boolean'],
            'icon' => ['nullable', 'string', 'max:50'],
            'keywords' => ['nullable', 'array'],
            'description' => ['nullable', 'array'],
            'render_type' => ['required', Rule::in(['normal', 'form', 'document', ''])],
            'form_start_time' => ['nullable', 'date'],
            'form_end_time' => ['nullable', 'date'],
        ];
    
        foreach ($languages as $code) {
            $rules["slug.$code"] = [
                'nullable',
                'regex:/^[a-zA-Z0-9_]+$/'
            ];
    
            if ($code === $locale) {
                array_unshift($rules["slug.$code"], 'required');
                $rules["slug.$code"][] = Rule::unique('content_types', "slug->$code")
                    ->ignore($id)
                    ->whereNull('deleted_at');
            }
        }
    
        $data = $request->validate($rules);
    
        // 填充所有语言为空值
        foreach (['name', 'slug', 'keywords', 'description'] as $field) {
            foreach ($languages as $lang) {
                $data[$field][$lang] = $data[$field][$lang] ?? '';
            }
        }
    
        $data['user_id'] = Auth::id();
    
        // 新增部分：保存 form_start_time 和 form_end_time 到 config 字段中
        $formStart = $request->input('form_start_time') ?: 0;
        $formEnd = $request->input('form_end_time') ?: 0;
    
        $data['config'] = [
            'form_start_time' => $formStart,
            'form_end_time' => $formEnd,
        ];
    //dd($data);
        // 保存
        $contentType = $id ? ContentType::findOrFail($id) : new ContentType();
        $contentType->fill($data)->save();
    
        return redirect(localized_route('admin.content_types.index'))
            ->with('success', 'Content Type saved successfully.');
    }
    
    
    // 删除
    public function destroy($id)
    {
        $contentType = ContentType::findOrFail($id);
        $contentType->delete();

        return redirect()->route('admin.content_types.index')->with('success', 'Content Type deleted successfully.');
    }
}
