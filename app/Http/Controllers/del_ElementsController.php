<?php

namespace App\Http\Controllers;

use App\Models\Element;
use App\Models\ElementTranslation;
use Illuminate\Http\Request;

class ElementsController extends Controller
{
    public function index($slug)
    {
        $elements = kazcms_get_elements_cache();
    
        // Get current language code
        echo $slug;
        $currentLang = app()->getLocale();
    
        // Find if any element has a slug field value equal to $slug in the current language
        $found = $elements->contains(function ($element) use ($slug, $currentLang) {
            $slugData = json_decode($element->slug, true);
            return isset($slugData[$currentLang]) && $slugData[$currentLang] === $slug;
        });
    
        var_dump($found);
        exit();
        $element = Element::where('slug', $slug)->firstOrFail();
        $translation = ElementTranslation::where('element_id', $element->id)
            ->where('locale', app()->getLocale())
            ->firstOrFail();

        return view('welcome', [
            'element' => $element,
            'translation' => $translation,
        ]);
    }
}
