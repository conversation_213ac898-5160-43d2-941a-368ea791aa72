<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContentListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 如需权限可自行实现
    }

    public function rules(): array
    {
        return [
            'content_type_id'   => 'required|integer|min:1',
            'fields_list'       => 'array',
            'fields_list.*'     => 'array|min:3',
            'order'             => 'array',
            'perpage'           => 'integer|min:1|max:100',
            'page'              => 'integer|min:1',
            'keywords'          => 'string|nullable',
            'user_ids'          => 'array',
            'user_ids.*'        => 'integer',
            'options'           => 'array',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'perpage' => $this->input('perpage', 20),
            'page'    => $this->input('page', 1),
            'fields_list' => $this->input('fields_list', []),
            'order'       => $this->input('order', []),
            'user_ids'    => $this->input('user_ids', []),
            'langKw'=> $this->input('langKw',[]),
            'options'     => $this->input('options', []),
        ]);
    }
}