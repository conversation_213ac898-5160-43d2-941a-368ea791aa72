<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        // 判断是否启用多语言
        $isMultilang = function_exists('is_multilang_site') && is_multilang_site();

        // 获取 URL 中的 lang 参数
        $lang = $request->route('lang');

        if ($isMultilang) {
            // 多语言站点必须有 lang 前缀
            if (!$lang) {
                // 没有 lang，重定向到默认语言的前缀版本
                $defaultLang = function_exists('getDefaultLanguage') ? getDefaultLanguage() : null;
                $defaultLangCode = $defaultLang?->code ?? config('app.fallback_locale');

                $uri = ltrim($request->getRequestUri(), '/');
                $redirectUrl = '/' . $defaultLangCode . '/' . $uri;

                // 避免 // 重复
                $redirectUrl = preg_replace('#/+#', '/', $redirectUrl);

                return redirect($redirectUrl);
            }

            // 如果 lang 存在但不合法，返回 404
            if (function_exists('isValidLocale') && !isValidLocale($lang)) {
                abort(404);
            }

            // 设置语言环境
            app()->setLocale($lang);
        } else {
            // 单语言站点，强制使用 fallback_locale
            app()->setLocale(config('app.fallback_locale'));
        }

        if ($request->route('lang')) {
            // 设置语言
            app()->setLocale($request->route('lang'));
    
            // 从路由参数中剥离掉 lang，避免传给控制器
            $request->route()->forgetParameter('lang');
        }

        return $next($request);
    }
}
