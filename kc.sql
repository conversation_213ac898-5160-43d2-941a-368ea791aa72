-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 16, 2025 at 03:16 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.1.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `kc`
--

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_elements`
--

CREATE TABLE `kazcms_elements` (
  `id` int NOT NULL,
  `name` json NOT NULL COMMENT 'Elements name',
  `slug` json NOT NULL COMMENT 'Elements slug',
  `seo_keywords` json NOT NULL,
  `seo_description` json NOT NULL,
  `created_at` int UNSIGNED NOT NULL COMMENT 'Unix timestamp of creation',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'Order for sorting in UI',
  `show_on_front` tinyint NOT NULL DEFAULT '0' COMMENT 'Visible on frontend',
  `user_id` int UNSIGNED NOT NULL COMMENT 'Creator user ID',
  `has_form` tinyint NOT NULL DEFAULT '0' COMMENT 'Should display form',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Icon name (e.g., FontAwesome)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Content Elements Table';

--
-- Dumping data for table `kazcms_elements`
--

INSERT INTO `kazcms_elements` (`id`, `name`, `slug`, `seo_keywords`, `seo_description`, `created_at`, `sort_order`, `show_on_front`, `user_id`, `has_form`, `icon`) VALUES
(1, '{\"en\": \"News\", \"zh\": \"新闻\"}', '{\"en\": \"news\", \"zh\": \"xinwen\"}', 'null', 'null', 1748828918, 5, 1, 2, 0, 'newspaper'),
(2, 'null', 'null', 'null', 'null', 1748828950, 10, 1, 2, 0, 'box'),
(3, 'null', 'null', 'null', 'null', 1748829116, 15, 1, 2, 0, 'images'),
(4, 'null', 'null', 'null', 'null', 1748829145, 20, 1, 2, 0, 'journal-text'),
(5, 'null', 'null', 'null', 'null', 1748829280, 25, 1, 2, 0, 'info-circle'),
(6, 'null', 'null', 'null', 'null', 1749480519, 40, 1, 2, 1, 'bar-chart'),
(7, 'null', 'null', 'null', 'null', 1749480554, 60, 1, 2, 1, 'clipboard-check');

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_failed_jobs`
--

CREATE TABLE `kazcms_failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_languages`
--

CREATE TABLE `kazcms_languages` (
  `id` int UNSIGNED NOT NULL,
  `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language code (e.g., en, fr, zh)',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Language name (e.g., English, Français)',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Is the language enabled',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT 'For ordering in UI',
  `is_default` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Available languages';

--
-- Dumping data for table `kazcms_languages`
--

INSERT INTO `kazcms_languages` (`id`, `code`, `name`, `enabled`, `sort_order`, `is_default`) VALUES
(1, 'en', 'English', 1, 1, 1),
(2, 'fr', 'Français', 1, 2, 0),
(3, 'zh', '中文', 1, 3, 0),
(4, 'ar', 'العربية', 1, 4, 0),
(5, 'ru', 'Русский', 1, 5, 0),
(6, 'es', 'Español', 0, 6, 0);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_migrations`
--

CREATE TABLE `kazcms_migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_migrations`
--

INSERT INTO `kazcms_migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_password_reset_tokens`
--

CREATE TABLE `kazcms_password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_personal_access_tokens`
--

CREATE TABLE `kazcms_personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_site_settings`
--

CREATE TABLE `kazcms_site_settings` (
  `id` bigint UNSIGNED NOT NULL,
  `site_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'KAZCMS',
  `site_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `favicon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `is_maintenance_mode` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_multilang` tinyint NOT NULL DEFAULT '0' COMMENT '0 = Single language, 1 = Multi-language '
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_site_settings`
--

INSERT INTO `kazcms_site_settings` (`id`, `site_name`, `site_url`, `meta_title`, `meta_keywords`, `meta_description`, `favicon`, `logo`, `language`, `is_maintenance_mode`, `created_at`, `updated_at`, `is_multilang`) VALUES
(1, 'KAZCMS', 'http://localhost/abc', 'KAZCMS', 'a,c,c,c', 'fdsfds', 'images/eb4bc1d9-ebff-481b-86ed-dd13a9571a81.png', 'images/88ecc2fd-c556-48d0-a943-464cb1b9e1ba.png', 'en', 0, NULL, '2025-05-31 05:41:14', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kazcms_users`
--

CREATE TABLE `kazcms_users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kazcms_users`
--

INSERT INTO `kazcms_users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'kz', '<EMAIL>', NULL, '$2y$12$AnbhjL4U2XpuBStWDbV4uuTj7E595K1Mo.QzDivfKdKo66/XsCv36', '218w4Rb8tqmQFpEcTG2HDfPArX5oxoyexSY697HYs5GW0MLU4bVJL3bqvd2G', '2025-06-14 22:17:10', '2025-06-14 22:17:10');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `kazcms_elements`
--
ALTER TABLE `kazcms_elements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user_id` (`user_id`);

--
-- Indexes for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_code` (`code`);

--
-- Indexes for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_password_reset_tokens`
--
ALTER TABLE `kazcms_password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `kazcms_elements`
--
ALTER TABLE `kazcms_elements`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `kazcms_failed_jobs`
--
ALTER TABLE `kazcms_failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_languages`
--
ALTER TABLE `kazcms_languages`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `kazcms_migrations`
--
ALTER TABLE `kazcms_migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `kazcms_personal_access_tokens`
--
ALTER TABLE `kazcms_personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kazcms_site_settings`
--
ALTER TABLE `kazcms_site_settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `kazcms_users`
--
ALTER TABLE `kazcms_users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
