class ImageUploader {
    constructor(fileInput, previewContainer, form, orderInputName = 'image_order') {
      this.fileInput = fileInput;
      this.previewContainer = previewContainer;
      this.form = form;
      this.orderInputName = orderInputName;
      this.uploadedImages = []; // { id, file, previewUrl }
      this.dragSrcEl = null;
      this._init();
    }
  
    _init() {
      this.fileInput.addEventListener('change', e => this._handleFiles(e.target.files));
      this.form.addEventListener('submit', e => this._injectFiles());
      this._renderPreview();
    }
  
    _handleFiles(files) {
      const newFiles = Array.from(files);
      newFiles.forEach(file => {
        if (!file.type.startsWith('image/')) return;
        const duplicate = this.uploadedImages.some(img => img.file && img.file.name === file.name && img.file.size === file.size);
        if (duplicate) {
          alert(`图片 "${file.name}" 已添加过`);
          return;
        }
        const id = crypto.randomUUID();
        const previewUrl = URL.createObjectURL(file);
        this.uploadedImages.push({ id, file, previewUrl });
      });
      this._renderPreview();
      this.fileInput.value = '';
    }
  
    _renderPreview() {
      this.previewContainer.innerHTML = '';
      this.uploadedImages.forEach((img, idx) => {
        const div = document.createElement('div');
        div.className = 'image-preview-item border border-gray-200 rounded p-1 cursor-move';
        div.dataset.id = img.id;
        div.style.cssText = 'display:inline-block; margin:5px; position:relative; width:110px; user-select:none;';
        div.setAttribute('draggable', 'true');
        div.innerHTML = `
          <img src="${img.previewUrl}" alt="预览" style="width:100px; height:100px; object-fit:cover; border-radius:4px;">
          <button type="button" aria-label="删除图片" style="
            position:absolute; top:2px; right:2px; background:#f44336; color:#fff; border:none; 
            border-radius:50%; width:20px; height:20px; cursor:pointer; line-height:18px; font-weight:bold;">×</button>
        `;
        div.querySelector('button').addEventListener('click', () => {
          this.uploadedImages.splice(idx, 1);
          this._renderPreview();
        });
        div.addEventListener('dragstart', e => this._handleDragStart(e));
        div.addEventListener('dragover', e => this._handleDragOver(e));
        div.addEventListener('dragleave', e => this._handleDragLeave(e));
        div.addEventListener('drop', e => this._handleDrop(e));
        div.addEventListener('dragend', e => this._handleDragEnd(e));
        this.previewContainer.appendChild(div);
      });
      this._updateOrderInputs();
    }
  
    _updateOrderInputs() {
      this.form.querySelectorAll(`input[name^="${this.orderInputName}"]`).forEach(el => el.remove());
      this.uploadedImages.forEach((img, idx) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = `${this.orderInputName}[]`;
        input.value = `new:${idx}`;
        this.form.appendChild(input);
      });
    }
  
    _injectFiles() {
      this.form.querySelectorAll('.temp-file-input').forEach(el => el.remove());
      this.uploadedImages.forEach(img => {
        if (!img.file) return;
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.name = this.fileInput.name;
        fileInput.classList.add('temp-file-input');
        fileInput.style.display = 'none';
        const dt = new DataTransfer();
        dt.items.add(img.file);
        fileInput.files = dt.files;
        this.form.appendChild(fileInput);
      });
    }
  
    _handleDragStart(e) {
      this.dragSrcEl = e.currentTarget;
      this.dragSrcEl.classList.add('opacity-50');
      e.dataTransfer.effectAllowed = 'move';
      e.dataTransfer.setData('text/plain', '');
    }
  
    _handleDragOver(e) {
      e.preventDefault();
      const el = e.currentTarget;
      if (el !== this.dragSrcEl) {
        el.classList.add('border-blue-500');
        el.classList.remove('border-gray-200');
      }
    }
  
    _handleDragLeave(e) {
      e.currentTarget.classList.remove('border-blue-500');
      e.currentTarget.classList.add('border-gray-200');
    }
  
    _handleDrop(e) {
      e.preventDefault();
      const el = e.currentTarget;
      if (el !== this.dragSrcEl) {
        const parent = this.previewContainer;
        const dragIndex = Array.from(parent.children).indexOf(this.dragSrcEl);
        const dropIndex = Array.from(parent.children).indexOf(el);
        if (dragIndex < dropIndex) {
          parent.insertBefore(this.dragSrcEl, el.nextSibling);
        } else {
          parent.insertBefore(this.dragSrcEl, el);
        }
        this._reorderImagesByDOM();
      }
      el.classList.remove('border-blue-500');
      el.classList.add('border-gray-200');
      this.dragSrcEl.classList.remove('opacity-50');
      this.dragSrcEl = null;
    }
  
    _handleDragEnd(e) {
      this.previewContainer.querySelectorAll('.image-preview-item').forEach(item => {
        item.classList.remove('opacity-50', 'border-blue-500');
        item.classList.add('border-gray-200');
      });
      this.dragSrcEl = null;
    }
  
    _reorderImagesByDOM() {
      const newOrder = [];
      this.previewContainer.querySelectorAll('.image-preview-item').forEach(div => {
        const id = div.dataset.id;
        const img = this.uploadedImages.find(i => i.id === id);
        if (img) newOrder.push(img);
      });
      this.uploadedImages = newOrder;
      this._updateOrderInputs();
    }
  }
  
  function initImageUploaders(fileInputClass, formClass) {
    document.querySelectorAll(`form.${formClass}`).forEach(form => {
      form.querySelectorAll(`input.${fileInputClass}[type="file"]`).forEach(input => {
        const previewId = input.dataset.preview;
        const previewContainer = previewId ? document.getElementById(previewId) : form.querySelector('.kaz-preview-container');
        if (!previewContainer) {
          console.warn('找不到对应预览容器 for input:', input);
          return;
        }
        const orderInputName = 'image_order_' + input.name.replace(/\W+/g, '_');
        new ImageUploader(input, previewContainer, form, orderInputName);
      });
    });
  }