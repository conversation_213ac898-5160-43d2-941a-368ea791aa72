// sidebar.js

// 切换左侧菜单展开/收起
export function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarTexts = document.querySelectorAll('.sidebar-text');
    if (sidebar.classList.contains('sidebar-expanded')) {
        sidebar.classList.remove('sidebar-expanded');
        sidebar.classList.add('sidebar-collapsed');
        sidebarTexts.forEach(text => text.style.display = 'none');
    } else {
        sidebar.classList.remove('sidebar-collapsed');
        sidebar.classList.add('sidebar-expanded');
        sidebarTexts.forEach(text => text.style.display = 'block');
    }
}

// 移动端切换菜单
export function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobile-overlay');
    sidebar.classList.toggle('open');
    overlay.classList.toggle('hidden');
}

// 子菜单切换
export function toggleSubmenu(menuId) {
    const menu = document.getElementById(menuId);
    const icon = document.getElementById(menuId + '-icon');
    if (menu) menu.classList.toggle('hidden');
    if (icon) icon.classList.toggle('rotate-180');
}

// 用户菜单切换（带阻止冒泡）
export function toggleUserMenu() {
    const userMenu = document.getElementById('user-menu');
    if (userMenu) {
        userMenu.classList.toggle('hidden');
    }
}

// 注册全局事件：点击外部隐藏用户菜单、窗口 resize 时关闭移动菜单
export function setupGlobalEvents() {
    document.addEventListener('click', (e) => {
        const userMenu = document.getElementById('user-menu');
        const userMenuBtn = document.getElementById('user-menu-btn');

        if (!userMenu || !userMenuBtn) return;

        if (!userMenu.contains(e.target) && !userMenuBtn.contains(e.target)) {
            userMenu.classList.add('hidden');
        }
    });

    window.addEventListener('resize', () => {
        if (window.innerWidth >= 768) {
            document.getElementById('sidebar')?.classList.remove('open');
            document.getElementById('mobile-overlay')?.classList.add('hidden');
        }
    });
}
