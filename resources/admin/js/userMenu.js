export function toggleUserMenu() {
    const menu = document.getElementById('user-menu');
    menu.classList.toggle('hidden');
  }
  
  window.toggleUserMenu = toggleUserMenu;
  
  document.addEventListener('click', (event) => {
    const userMenu = document.getElementById('user-menu');
    const userButton = event.target.closest('button');
  
    if (!userButton || !userButton.onclick || !userButton.onclick.toString().indexOf('toggleUserMenu') === -1) {
      userMenu.classList.add('hidden');
    }
  });
  