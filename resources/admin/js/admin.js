import {
    toggleSidebar,
    toggleMobileSidebar,
    toggleSubmenu,
    toggleUserMenu,
    setupGlobalEvents
  } from './sidebar.js';
  
  setupGlobalEvents();
  
  document.addEventListener('DOMContentLoaded', () => {
    const sidebarBtn = document.getElementById('toggle-sidebar-btn');
    if (sidebarBtn) {
      sidebarBtn.addEventListener('click', toggleSidebar);
    }
  
    const userMenuBtn = document.getElementById('user-menu-btn');
    if (userMenuBtn) {
      userMenuBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // 关键：阻止冒泡
        toggleUserMenu();
      });
    }
  
    document.querySelectorAll('.submenu-toggle').forEach(el => {
      el.addEventListener('click', event => {
        event.preventDefault();
        const menuId = el.getAttribute('data-toggle-submenu');
        toggleSubmenu(menuId);
      });
    });
  
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    if (mobileMenuBtn) {
      mobileMenuBtn.addEventListener('click', () => {
        toggleMobileSidebar();
      });
    }
  
    const mobileOverlay = document.getElementById('mobile-overlay');
    if (mobileOverlay) {
      mobileOverlay.addEventListener('click', () => {
        toggleMobileSidebar();
      });
    }
  });


  