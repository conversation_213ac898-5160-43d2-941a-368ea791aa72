import { validatePublishFields } from './validatePublish';



document.addEventListener('DOMContentLoaded', () => {
    KazImageCraft._init('kaz-file-input', 'kaz-upload-form');

    document.querySelectorAll('.kazcms-data-submit-btn').forEach(btn => {
      btn.addEventListener('click', e => {
        KazImageCraft.injectAllFiles();
  
        if (btn.dataset.action === 'publish') {
          const isValid = validatePublishFields();
          if (!isValid) return; // 阻止提交
        }
  
        document.getElementById('user_action').value = btn.dataset.action;
        document.getElementById('kazcms-data-form').submit();
      });
    });

/* ---------- chip delete (static + dynamic) ---------- */
document.addEventListener('click', e => {
    if (!e.target.matches('.kazcms-remove-category')) return;

    e.preventDefault();
    const btn       = e.target;
    const inputId   = btn.dataset.inputid;     // hidden input id
    const categoryId= btn.dataset.categoryid;  // 要移除的分类ID

    /* 1. 取消隐藏字段里的 ID */
    const hid = document.getElementById(inputId);
    if (hid) {
        const ids = hid.value.split(',').filter(Boolean).filter(id => id !== categoryId);
        hid.value = ids.join(',');
    }

    /* 2. 移除按钮所在的 chip */
    const chip = btn.parentElement;            // <span …>…</span>
    const wrap = chip.parentElement;           // 选中类别容器
    chip.remove();

    /* 3. 若已无 chip，则隐藏容器 */
    if (wrap && wrap.children.length === 0) {
        wrap.classList.add('hidden');
    }
});


    /* ---------- language tabs ---------- */
    document.querySelectorAll('button[role="tab"][data-lang][id^="tab-"]')
        .forEach(tab => {
            tab.addEventListener('click', () => {
                const { lang, field: fieldIds } = tab.dataset;
                if (!fieldIds) return;

                /* panels */
                document.querySelectorAll(`.tabpanel-${fieldIds}`)
                    .forEach(p => p.classList.toggle('hidden', true));
                document
                    .getElementById(`tab-panel-${lang}-${fieldIds}`)
                    ?.classList.toggle('hidden', false);

                /* buttons */
                document
                    .querySelectorAll(`button[id$="-${fieldIds}"][role="tab"]`)
                    .forEach(t => {
                        t.setAttribute('aria-selected', 'false');
                        t.classList.remove('border-b-2', 'border-blue-600', 'text-blue-600');
                        t.classList.add('text-gray-500', 'hover:text-gray-700');
                    });

                tab.setAttribute('aria-selected', 'true');
                tab.classList.add('border-b-2', 'border-blue-600', 'text-blue-600');
                tab.classList.remove('text-gray-500', 'hover:text-gray-700');
            });
        });

    /* ---------- category modal ---------- */
    let currentCategoryButtonId = null;
    const modalElm   = document.getElementById('categoryModal');
    const treeElm    = document.getElementById('categoryTree');

    document.getElementById('closeCategoryModal')?.addEventListener('click', () => {
        modalElm.classList.add('hidden');
    });

    function openCategoryModal(btn) {
        currentCategoryButtonId = btn.id;
        treeElm.innerHTML = '';
        modalElm.classList.remove('hidden');

        // 解析当前字段已选 ID
        const [ , typeId, fieldId ] = btn.id.split('_');
        const hiddenVal  = document.getElementById(`field_${typeId}_${fieldId}`)?.value || '';
        const selectedIds = hiddenVal.split(',').filter(Boolean);   // ['12','35',...]

        loadCategoryTree(btn.dataset.categoryid, treeElm, btn.dataset.displaystyle, selectedIds);
    }

    async function loadCategoryTree(parentId, container, displayStyle, selectedIds = []) {
        const resp = await fetch(`/${window.kazcms.locale}/admin/categories/children/${parentId}`);
        const cats = await resp.json();

        cats.forEach(cat => {
            const wrapper = document.createElement('div');
            wrapper.className = 'ml-4 py-2 border-l border-gray-200 pl-4 hover:bg-gray-50 rounded-r-md';

            /* left side */
            const left = document.createElement('div');
            left.className = 'flex items-center flex-1';

            /* 非终结分类：生成 checkbox/radio */
            if (cat.final_category == 0) {
                const inp = document.createElement('input');
                inp.type  = displayStyle === 'checkbox' ? 'checkbox' : 'radio';
                inp.name  = 'category_selection';
                inp.value = cat.id;
                inp.id    = `category_${cat.id}`;
                inp.className = 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded';

                /* 预勾选 */
                if (selectedIds.includes(String(cat.id))) inp.checked = true;

                const label = document.createElement('label');
                label.htmlFor = inp.id;
                label.className =
                    'ml-3 text-gray-700 font-medium cursor-pointer hover:text-blue-700 flex-1 select-none';
                label.textContent = cat.name[window.kazcms.locale];

                left.append(inp, label);
            } else {  /* 终结分类：只显示名称 */
                const span = document.createElement('span');
                span.className = 'ml-7 text-gray-700 font-medium select-none';
                span.textContent = cat.name[window.kazcms.locale];
                left.append(span);
            }

            /* 可展开节点 */
            if (cat.final_category == 1) {
                const more = document.createElement('button');
                more.type  = 'button';
                more.textContent = '▶';
                more.className =
                    'ml-2 px-2 py-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-all';

                more.onclick = e => {
                    e.preventDefault();
                    const existed = wrapper.querySelector('.children');
                    if (existed) {
                        existed.remove();
                        more.style.transform = 'rotate(0deg)';
                        more.classList.remove('text-blue-800');
                    } else {
                        const childBox = document.createElement('div');
                        childBox.className = 'children ml-4 mt-2 border-l-2 border-blue-200 pl-3';
                        wrapper.appendChild(childBox);
                        loadCategoryTree(cat.id, childBox, displayStyle, selectedIds);
                        more.style.transform = 'rotate(90deg)';
                        more.classList.add('text-blue-800');
                    }
                };
                left.appendChild(more);
            }

            const flexWrap = document.createElement('div');
            flexWrap.className = 'flex items-center justify-between w-full';
            flexWrap.append(left);
            wrapper.appendChild(flexWrap);
            container.appendChild(wrapper);
        });
    }

    function confirmCategoryModal() {
        const btn = document.getElementById(currentCategoryButtonId);
        if (!btn) return;

        const display  = btn.dataset.displaystyle;
        const inputs   = treeElm.querySelectorAll('input:checked');
        const [ , typeId, fieldId ] = btn.id.split('_');

        const wrap = document.getElementById(`selectedCategories_${typeId}_${fieldId}`);
        wrap.innerHTML = '';

        const ids = [];
        inputs.forEach((inp, i) => {
            ids.push(inp.value);

            /* chip */
            const labelText = document.querySelector(`label[for="${inp.id}"]`).textContent;
            const chip = document.createElement('span');
            chip.className =
                'inline-flex items-center px-3 py-1 mr-2 mb-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full';
            chip.textContent = labelText;

            /* remove btn */
            const rm = document.createElement('button');
            rm.className = 'ml-2 w-4 h-4 text-blue-600 hover:text-red-600 kazcms-remove-category';
            rm.dataset.inputid    = `field_${typeId}_${fieldId}`;
            rm.dataset.categoryid = inp.value;
            rm.textContent = '×';
            rm.onclick = e => {
                e.preventDefault();
                inp.checked = false;
                chip.remove();
                updateHidden();
            };

            chip.appendChild(rm);
            wrap.appendChild(chip);

            if (display === 'checkbox' && i < inputs.length - 1) {
                wrap.appendChild(Object.assign(document.createElement('span'), {
                    textContent: ',',
                    className: 'text-gray-400 text-sm mr-1'
                }));
            }
        });

        function updateHidden() {
            const hid = document.getElementById(`field_${typeId}_${fieldId}`);
            if (hid) {
                const sel = [...treeElm.querySelectorAll('input:checked')].map(i => i.value);
                hid.value = sel.join(',');
            }
            wrap.classList.toggle('hidden', wrap.children.length === 0);
        }
        updateHidden();
        modalElm.classList.add('hidden');
    }

    /* bind buttons */
    document.querySelectorAll('.category-select-button')
        .forEach(btn => btn.addEventListener('click', () => openCategoryModal(btn)));
    document.getElementById('confirmCategoryBtn')
        ?.addEventListener('click', confirmCategoryModal);

    /* ---------- option max‑select limiter ---------- */
    document.querySelectorAll('.option-group[data-max-select]')
        .forEach(group => {
            const max    = +group.dataset.maxSelect || 0;
            const inputs = group.querySelectorAll('input.option-input[type="checkbox"]');
            if (!max || !inputs.length) return;

            const enforce = () => {
                const checked = [...inputs].filter(i => i.checked).length;
                inputs.forEach(i => {
                    i.disabled = checked >= max && !i.checked;
                });
            };
            enforce();
            inputs.forEach(i => i.addEventListener('change', enforce));
        });

});

