document.getElementById('scanForm').addEventListener('submit', function (e) {
    e.preventDefault();

    const selected = Array.from(document.querySelectorAll('.checker-box:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('请选择至少一个模块');
        return;
    }
    if (selected.length > 5) {
        showConfirmModal(
          '你选择了超过 5 个模块，可能会花费很长时间。\n是否确认继续？建议最多选择 5 个模块。',
          () => {
            doScan(selected);
          },
          () => {
            // 用户取消
          }
        );
        return;
    }

    doScan(selected);
});

function doScan(selected) {
    const lang = document.getElementById('sourceLang').value;
    const targetLangs = Array.from(document.querySelectorAll('input[name="targetLangs[]"]:checked')).map(cb => cb.value);
    const progressBar = document.getElementById('progressBar');
    const progressContainer = document.getElementById('progressContainer');
    const progressText = document.getElementById('progressText');
    const resultBox = document.getElementById('resultBox');

    // 显示进度条，隐藏结果
    progressContainer.classList.remove('hidden');
    progressBar.style.width = '0%';
    progressText.textContent = '0%';

    // 模拟进度
    let progress = 0;
    const simulate = setInterval(() => {
        progress += 5;
        if (progress >= 90) clearInterval(simulate);
        progressBar.style.width = progress + '%';
        progressText.textContent = progress + '%';
    }, 100);

    fetch('/' + window.kazcms.locale + '/admin/multilang/bulk-scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            source_lang: lang,
            target_langs: targetLangs,
            checker_ids: selected
        })
    })
    .then(res => res.json())
    .then(data => {
        clearInterval(simulate);
        progressBar.style.width = '100%';
        progressText.textContent = '100%';

        setTimeout(() => {
            displayMissingTarget(data);
        }, 500);
    })
    .catch(error => {
        clearInterval(simulate);
        progressContainer.classList.add('hidden');
        alert('扫描过程中出现错误，请重试');
        console.error('Error:', error);
    });
}

function displayMissingTarget(scanResult) {
    const missingDataContainerTarget = document.getElementById('missingDataContainerTarget');
    const missingDataContainerSource = document.getElementById('missingDataContainerSource');
    const missingItemsList = document.getElementById('missingItemsList');
    const sourceMissingList = document.getElementById('sourceMissingList');
    const aiPrompt = document.getElementById('aiPrompt');
    const noMissingDataContainer = document.getElementById('noMissingDataContainer');

    missingItemsList.innerHTML = '';
    if (sourceMissingList) sourceMissingList.innerHTML = '';

    // 取真正数组
    const dataArray = scanResult.data || [];

    const missingItems = [];
    const sourceMissingItems = [];

    dataArray.forEach(item => {
        item.missing.forEach(miss => {
            missingItems.push({
                checker_id: item.checker_id,   // 加上 checker_id
                record_id: miss.id,
                lang_code: miss.lang,
                source_data: miss.text
            });
        });
        if (item.source_missing) {
            item.source_missing.forEach(srcMiss => {
                sourceMissingItems.push({
                    record_id: srcMiss.id,
                    lang_code: srcMiss.lang,
                    note: srcMiss.note || '源语言内容缺失，请先补充该项'
                });
            });
        }
    });

    if (missingItems.length === 0) {
        noMissingDataContainer.classList.remove('hidden');
        aiPrompt.textContent = '';
        missingDataContainerTarget.classList.add('hidden');
    } else {
        noMissingDataContainer.classList.add('hidden');
        missingItems.forEach(i => {
            const pre = document.createElement('pre');
            pre.className = 'bg-white p-2 rounded border border-gray-300 font-mono text-sm break-words';
            pre.textContent = JSON.stringify(i, null, 0);
            missingItemsList.appendChild(pre);
        });
        const prompt = `请将以下 JSON 数据中的 source_data 字段翻译成对应的 lang_code 语言，返回一个 JSON 数组：

- record_id、checker_id 和 lang_code 保持不变；
- 添加一个字段 text，填入翻译后的内容；
- 每行为一个 JSON 对象；
- 不要添加任何额外内容，只返回 JSON 数组本身。`;

        aiPrompt.textContent = prompt;
        missingDataContainerTarget.classList.remove('hidden');
    }

    if (sourceMissingList) {
        if (sourceMissingItems.length === 0) {
            sourceMissingList.innerHTML = '<p class="text-green-600">没有缺失的源语言内容。</p>';
        } else {
            const title = document.createElement('h4');
            title.className = 'font-semibold mb-2 text-red-600';
            title.textContent = '🔴 源语言缺失项';
            sourceMissingList.appendChild(title);

            sourceMissingItems.forEach(i => {
                const pre = document.createElement('pre');
                pre.className = 'bg-white p-2 rounded border border-gray-300 font-mono text-sm break-words';
                pre.textContent = JSON.stringify(i, null, 0);
                sourceMissingList.appendChild(pre);
            });
            missingDataContainerSource.classList.remove('hidden');
        }
    }
}



document.getElementById('submitTranslation').addEventListener('click', () => {
    const textarea = document.getElementById('translatedResults');
    const val = textarea.value.trim();

    if (!val) {
        alert('请先粘贴 AI 翻译结果 JSON');
        return;
    }

    // 尝试从文本中提取 JSON 数组（防止 AI 输出前后加了多余的解释性内容）
    const jsonMatches = val.match(/\[\s*{[\s\S]*?}\s*]/);

    if (!jsonMatches) {
        alert('未找到有效的 JSON 数组，请检查粘贴内容是否正确');
        return;
    }

    let parsed;
    try {
        parsed = JSON.parse(jsonMatches[0]);
        if (!Array.isArray(parsed)) throw new Error('必须是数组');
    } catch (e) {
        alert('翻译结果格式错误，请确保是有效的 JSON 数组');
        return;
    }

    const valid = parsed.every(item =>
        item.record_id !== undefined &&
        typeof item.lang_code === 'string' &&
        typeof item.source_data === 'string' &&
        typeof item.text === 'string'
    );

    if (!valid) {
        alert('每条翻译项必须包含 record_id, lang_code, source_data 和 text 字段');
        return;
    }

    fetch('/' + window.kazcms.locale + '/admin/multilang/save-translations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ translations: parsed })
    })
    .then(res => res.json())
    .then(data => {
        alert('翻译补全成功！');
        textarea.value = '';
        document.getElementById('missingDataContainerTarget').classList.add('hidden');
    })
    .catch(err => {
        console.error(err);
        alert('提交失败，请稍后重试');
    });
});

document.addEventListener('DOMContentLoaded', function () {
    const sourceSelect = document.getElementById('sourceLang');
    const checkboxes = document.querySelectorAll('input[name="targetLangs[]"]');

    function updateCheckboxes() {
        const selected = sourceSelect.value;
        checkboxes.forEach(cb => {
            cb.checked = false;
            const wrapper = cb.closest('label') || cb.parentElement;
            if (cb.value === selected) {
                wrapper.classList.add('hidden');
            } else {
                wrapper.classList.remove('hidden');
            }
        });
    }

    sourceSelect.addEventListener('change', updateCheckboxes);
    updateCheckboxes();
});

function showConfirmModal(message, onConfirm, onCancel) {
    const modal = document.getElementById('confirmModal');
    const msg = document.getElementById('confirmMessage');
    const okBtn = document.getElementById('okBtn');
    const cancelBtn = document.getElementById('cancelBtn');

    msg.textContent = message;

    modal.classList.remove('hidden');

    const cleanup = () => {
        modal.classList.add('hidden');
        okBtn.onclick = null;
        cancelBtn.onclick = null;
    };

    okBtn.onclick = () => {
        cleanup();
        onConfirm();
    };

    cancelBtn.onclick = () => {
        cleanup();
        if (onCancel) onCancel();
    };
}
