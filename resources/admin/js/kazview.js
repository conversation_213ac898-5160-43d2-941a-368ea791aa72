// 1. 条件策略基类
class BaseCondition {
    constructor(field) {
        this.field = field;
    }
    getOperators() { return []; }
    renderInput(currentValue) { return ''; }
    toQuery(operator, value) { return ''; }
}

// 2. 数字类型条件
class NumberCondition extends BaseCondition {
    getOperators() {
        return ['=', '>', '<', '>=', '<='];
    }
    renderInput(value = '') {
        return `<input type="number" value="${value}" />`;
    }
    toQuery(operator, value) {
        return `${this.field} ${operator} ${value}`;
    }
}

// 3. 文本类型条件
class TextCondition extends BaseCondition {
    getOperators() {
        return ['contains', 'equals', 'startsWith', 'endsWith'];
    }
    renderInput(value = '') {
        return `<input type="text" value="${value}" />`;
    }
    toQuery(operator, value) {
        switch(operator) {
            case 'contains': return `${this.field} LIKE '%${value}%'`;
            case 'startsWith': return `${this.field} LIKE '${value}%'`;
            case 'endsWith': return `${this.field} LIKE '%${value}'`;
            default: return `${this.field} = '${value}'`;
        }
    }
}

// 4. 文件类型条件
class FileCondition extends BaseCondition {
    getOperators() {
        return ['extension', 'size>', 'size<', 'nameContains'];
    }
    renderInput(value = '') {
        return `<input type="text" value="${value}" placeholder="Enter value"/>`;
    }
    toQuery(operator, value) {
        switch(operator) {
            case 'extension': return `${this.field}->>'extension' = '${value}'`;
            case 'size>': return `${this.field}->>'size' > ${value}`;
            case 'size<': return `${this.field}->>'size' < ${value}`;
            case 'nameContains': return `${this.field}->>'name' LIKE '%${value}%'`;
            default: return '';
        }
    }
}

// 5. 条件管理器
class ConditionManager {
    constructor() {
        this.conditions = [];
        this.strategies = {
            number: NumberCondition,
            text: TextCondition,
            file: FileCondition
        };
    }

    addCondition(field, type, operator, value, logic = 'AND') {
        const id = crypto.randomUUID();
        this.conditions.push({ id, field, type, operator, value, logic });
        return id;
    }

    editCondition(id, newData) {
        const index = this.conditions.findIndex(c => c.id === id);
        if (index >= 0) {
            this.conditions[index] = { ...this.conditions[index], ...newData };
        }
    }

    removeCondition(id) {
        this.conditions = this.conditions.filter(c => c.id !== id);
    }

    buildQuery() {
        return this.conditions.map(c => {
            const strategy = new this.strategies[c.type](c.field);
            return strategy.toQuery(c.operator, c.value);
        }).join(' ');
    }
}
