document.addEventListener('DOMContentLoaded', function () {
    const modal = document.getElementById('permissionModal');
    const permissionTree = document.getElementById('permissionTree');
    const assigneeTypeInput = document.querySelector('input[name="assignee_type"]');
    const assigneeIdsInput = document.getElementById('modalAssigneeIds');
    const addPermissionsBtn = document.getElementById('addPermissionsBtn');
    const checkboxes = document.querySelectorAll('input[name="group_select[]"]');
    const closeModalBtn = document.getElementById('closeModal');
    const permissionForm = document.getElementById('permissionForm');

    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateAddPermissionsButton);
    });

    function updateAddPermissionsButton() {
        const anyChecked = Array.from(checkboxes).some(cb => cb.checked);
        addPermissionsBtn.classList.toggle('hidden', !anyChecked);
    }

    addPermissionsBtn.addEventListener('click', function () {
        const selectedIds = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
            console.log('Selected groups:', selectedIds);
        openPermissionModal(selectedIds);
    });

    // ✅ 新增：为每个“权限管理”按钮绑定事件
    document.querySelectorAll('.openPermissionModalBtn').forEach(btn => {
        btn.addEventListener('click', function () {
            const groupId = this.dataset.groupId;
            openPermissionModal([groupId]);
        });
    });

    // ✅ 抽出统一函数：打开权限 modal 并加载数据
    function openPermissionModal(groupIds) {
        if (!groupIds || groupIds.length === 0) return;
        console.log('Opening modal for groups:', groupIds);
        assigneeTypeInput.value = 'group';
        assigneeIdsInput.value = groupIds.join(',');
        modal.classList.remove('hidden');
        permissionTree.innerHTML = '<p class="text-gray-500">加载中...</p>';

        fetch(`/${window.Laravel.locale}/admin/permissions/assign/group/${groupIds.join(',')}`)
            .then(res => res.text())
            .then(html => {
                permissionTree.innerHTML = html;
            })
            .catch(err => {
                console.error('加载权限失败：', err);
                permissionTree.innerHTML = '<p class="text-red-500">加载失败，请稍后再试。</p>';
            });
    }

    closeModalBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    permissionForm.addEventListener('submit', function (e) {
        e.preventDefault();

        const form = document.getElementById('permissionForm');
        const formData = new FormData(form);

        const assigneeType = formData.get('assignee_type');
        const assigneeIdsRaw = formData.get('assignee_id'); // e.g., "1,2,3"
        const permissionIds = formData.getAll('permission_ids[]');

        fetch(`/${window.Laravel.locale}/admin/permissions/save`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                assignee_type: assigneeType,
                assignee_id: assigneeIdsRaw, // already comma-joined string from hidden input
                permission_ids: permissionIds
            })
        })
        .then(res => res.json())
        .then(result => {
            if (result.status === 'ok') {
                alert('权限保存成功！');
                modal.classList.add('hidden');
            } else {
                alert('权限保存失败。');
            }
        })
        .catch(err => {
            alert('请求出错，请稍后再试。');
        });

    });

   
});

