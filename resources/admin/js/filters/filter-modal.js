// filters/filter-modal.js
function openFilterModal(fieldType, subType, existingCondition = null, saveCallback) {
    const modal = document.getElementById("filterModal");
    const form = modal.querySelector("form");

    form.innerHTML = FilterUIFactory.create(fieldType, subType, existingCondition);

    modal.classList.remove("hidden");

    form.onsubmit = (e) => {
        e.preventDefault();
        const formData = new FormData(form);
        saveCallback({
            fieldType,
            subType,
            operator: formData.get("operator"),
            value: formData.get("value")
        });
        modal.classList.add("hidden");
    };
}
