// filters/filter-manager.js
class FilterManager {
    constructor() {
        this.conditions = [];
    }

    addCondition(condition) {
        this.conditions.push(condition);
    }

    editCondition(index, newCondition) {
        this.conditions[index] = newCondition;
    }

    removeCondition(index) {
        this.conditions.splice(index, 1);
    }

    getConditions() {
        return this.conditions;
    }
}
