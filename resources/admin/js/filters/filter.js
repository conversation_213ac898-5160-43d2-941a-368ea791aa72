// filter.js

// =======================
// 1. 条件配置
// =======================
const ConditionConfig = {
    text: {
        default: ["contains", "equals", "starts_with", "ends_with"],
        email: ["equals", "contains"],
        url: ["contains", "equals"],
        number: {
            default: ["=", ">", "<", ">=", "<="]
        },
    },
  
    file: {
        default: ["filename_contains", "extension_is", "size_gt", "size_lt"]
    },
    date: {
        default: ["before", "after", "on"]
    },
    textarea: {
        default: ["contains", "equals"]
    },
    options: {
        default: ["equals", "not_equals", "in", "not_in"]
    },
    categories: {
        default: ["in", "not_in", "is_descendant_of"]
    },
    code: {
        default: ["contains", "equals", "regex"]
    }
};

// =======================
// 2. 条件管理器
// =======================
class FilterManager {
    constructor() {
        this.storageKey = "filterConditions";
        this.conditions = this.loadAllConditions(); // 加载所有 content_type_id 的
    }

    // 加载本地存储
    loadAllConditions() {
        const raw = localStorage.getItem(this.storageKey);
        if (!raw) return {};
        try {
            return JSON.parse(raw);
        } catch {
            return {};
        }
    }

    // 保存到本地存储
    saveAllConditions() {


        localStorage.setItem(this.storageKey, JSON.stringify(this.conditions));
    }

    // 解析 fieldId 获取 content_type_id
    getContentTypeId(fieldId) {
        return fieldId.split("-")[0];
    }

    addCondition(condition) {
        const id = crypto.randomUUID ? crypto.randomUUID() : Date.now().toString();
        const newCondition = { id, logic: "AND", ...condition };
        const contentTypeId = this.getContentTypeId(condition.fieldId);

        if (!this.conditions[contentTypeId]) {
            this.conditions[contentTypeId] = {};
        }
        if (!this.conditions[contentTypeId][condition.fieldId]) {
            this.conditions[contentTypeId][condition.fieldId] = [];
        }

        this.conditions[contentTypeId][condition.fieldId].push(newCondition);
        this.saveAllConditions();

        console.log("addCondition", newCondition);

        // UI 渲染
        const container = document.getElementById(`conditions-${condition.fieldId}`);
        if (container) {
            const item = document.createElement("div");
            item.className = "flex items-center justify-between p-1 bg-gray-100 rounded";
            item.dataset.id = id;
            item.innerHTML = `
                <span>${condition.label || condition.value}</span>
                <button class="text-xs text-red-500 hover:underline" onclick="filterManager.removeCondition('${condition.fieldId}','${id}')">x</button>
            `;
            container.appendChild(item);
        }

        return id;
    }

    editCondition(fieldId, id, newCondition) {
        const contentTypeId = this.getContentTypeId(fieldId);
        if (!this.conditions[contentTypeId] || !this.conditions[contentTypeId][fieldId]) return;

        const index = this.conditions[contentTypeId][fieldId].findIndex(c => c.id === id);
        if (index >= 0) {
            this.conditions[contentTypeId][fieldId][index] = { ...this.conditions[contentTypeId][fieldId][index], ...newCondition };
            this.saveAllConditions();
        }
    }

    removeCondition(fieldId, id) {
        const contentTypeId = this.getContentTypeId(fieldId);
        if (!this.conditions[contentTypeId] || !this.conditions[contentTypeId][fieldId]) return;

        this.conditions[contentTypeId][fieldId] = this.conditions[contentTypeId][fieldId].filter(c => c.id !== id);
        if (this.conditions[contentTypeId][fieldId].length === 0) {
            delete this.conditions[contentTypeId][fieldId];
        }
        this.saveAllConditions();

        // 删除UI
        const el = document.querySelector(`#conditions-${fieldId} [data-id="${id}"]`);
        if (el) el.remove();
    }

    getConditions(fieldId = null, contentTypeId = null) {
        if (fieldId) {
            contentTypeId = this.getContentTypeId(fieldId);
            return this.conditions[contentTypeId]?.[fieldId] || [];
        }
        if (contentTypeId) {
            return this.conditions[contentTypeId] || {};
        }
        return this.conditions;
    }

    buildQuery(contentTypeId) {
        let queries = [];
        const fields = this.conditions[contentTypeId] || {};
        Object.values(fields).forEach(fieldConds => {
            fieldConds.forEach(c => {
                const strategy = new FilterUIFactory(c.fieldType, c.subType);
                queries.push(strategy.toQuery(c.operator, c.value, c.fieldName));
            });
        });
        return queries.join(" AND ");
    }
}



// =======================
// 3. UI 工厂
// =======================
class FilterUIFactory {
    constructor(fieldType, subType = "default") {
        this.fieldType = fieldType;
        this.subType = subType;
        this.operators = (ConditionConfig[fieldType] && ConditionConfig[fieldType][subType])
            || ConditionConfig[fieldType]?.default || [];
    }

    renderInput(condition = {}) {
        condition = condition || {}; 
        let html = `<select name="operator" class="bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">`;
        this.operators.forEach(op => {
            html += `<option value="${op}" ${op === condition.operator ? "selected" : ""}>${op}</option>`;
        });
        html += `</select>`;
    
        // 根据类型生成输入控件
        let inputType = "text";
        if (this.fieldType === "number") inputType = "number";
        if (this.fieldType === "date") inputType = "date";
    
        const value = condition.value || "";
        html += `<input type="${inputType}" name="value" value="${value}" class="bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 ml-3"/>`;
        return html;
    }

    /**
     * 渲染字段别名输入框
     * @param {string} fieldKey - API 返回的字段 key
     * @param {object|string} aliasConfig - 已保存的别名配置
     *        单语言时: string
     *        多语言时: { "en-us": "...", "fr-ca": "...", "zh-cn": "..." }
     */
    renderAliasInput(fieldKey, aliasConfig = {}) {
        const labelEl = document.getElementById(`field-label-${fieldKey}`);
        const labelText = labelEl ? labelEl.innerHTML : fieldKey;
    
        // 默认配置
        const perLanguage = aliasConfig.perLanguage || false;
        const returnAllLanguages = aliasConfig.returnAllLanguages || false;
        const name = aliasConfig.name || { all: "" };
        
        let html = `<div class="mt-6 border-t border-gray-200 pt-6">`;
        html += `
            <div class="text-sm font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <span class="text-gray-600">Alias for</span>
                <span class="inline-flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-3 py-1.5 rounded-full text-sm font-medium border border-blue-200">${labelText}</span>
            </div>
        `;
    
        // 统一输入框
        const unifiedValue = perLanguage ? "" : (name.all || "");
        html += `
            <div class="space-y-3" id="alias-unified-${fieldKey}">
                <div class="space-y-2">
                    <label class="block text-xs font-medium text-gray-600 uppercase tracking-wide">Unified Alias</label>
                    <input type="text" name="aliasUnified[${fieldKey}]" 
                           value="${unifiedValue}" 
                           placeholder="Same label for all languages"
                           class="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                           oninput="applyUnifiedAlias('${fieldKey}', this.value)"/>
                </div>
                <button type="button" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200 group"
                        onclick="toggleLanguageInputs('${fieldKey}')">
                    <span>${perLanguage ? 'Hide per-language inputs' : 'Edit per language'}</span>
                    <svg class="ml-1 w-4 h-4 transform transition-transform duration-200 group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        `;
    
        // 多语言输入框
        html += `<div id="alias-multi-${fieldKey}" class="mt-4 space-y-4 ${perLanguage ? '' : 'hidden'}">`;
        if (window.isMultiLanguage) {
            window.availableLanguages.forEach(lang => {
                const langValue = perLanguage ? (name[lang] || "") : unifiedValue;
                html += `
                    <div class="space-y-2">
                        <label class="block text-xs font-medium text-gray-600 uppercase tracking-wide">${lang}</label>
                        <input type="text" name="alias[${fieldKey}][${lang}]" 
                               value="${langValue}" 
                               placeholder="Custom label for ${lang}"
                               class="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"/>
                    </div>`;
            });
        }
        html += `</div>`;
    
        // 返回值策略
        html += `
            <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div class="flex items-center gap-6">
                    <span class="text-sm font-medium text-gray-700">Return value:</span>
                    <div class="flex gap-4">
                        <label class="flex items-center gap-2 text-sm text-gray-700 cursor-pointer group">
                            <input type="radio" name="returnAllLanguages[${fieldKey}]" value="false" ${!returnAllLanguages ? 'checked' : ''} class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"/>
                            <span class="group-hover:text-gray-900 transition-colors duration-200">Current language only</span>
                        </label>
                        <label class="flex items-center gap-2 text-sm text-gray-700 cursor-pointer group">
                            <input type="radio" name="returnAllLanguages[${fieldKey}]" value="true" ${returnAllLanguages ? 'checked' : ''} class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"/>
                            <span class="group-hover:text-gray-900 transition-colors duration-200">All languages</span>
                        </label>
                    </div>
                </div>
            </div>
        `;
    
        html += `</div>`;
        return html;
    }
    
    

    toQuery(operator, value, fieldName) {
        // 生成简单 SQL 片段
        switch (operator) {
            case "contains":
                return `${fieldName} LIKE '%${value}%'`;
            case "starts_with":
                return `${fieldName} LIKE '${value}%'`;
            case "ends_with":
                return `${fieldName} LIKE '%${value}'`;
            case "=":
            case ">":
            case "<":
            case ">=":
            case "<=":
                return `${fieldName} ${operator} ${value}`;
            case "equals":
                return `${fieldName} = '${value}'`;
            case "filename_contains":
                return `${fieldName}->>'name' LIKE '%${value}%'`;
            case "extension_is":
                return `${fieldName}->>'extension' = '${value}'`;
            case "size_gt":
                return `${fieldName}->>'size' > ${value}`;
            case "size_lt":
                return `${fieldName}->>'size' < ${value}`;
            default:
                return `${fieldName} ${operator} '${value}'`;
        }
    }
}


// =======================
// 4. Modal 控制
// =======================
class FilterModal {
    constructor(manager, modalId = "filterModal") {
        this.manager = manager;
        this.modal = document.getElementById(modalId);
        this.form = this.modal.querySelector("form");
    }

    open(fieldId, fieldType, subType, fieldName, existingCondition = null) {
        existingCondition = existingCondition || {};  
    
        const ui = new FilterUIFactory(fieldType, subType);
    
        // 渲染输入控件
        let html = ui.renderInput(existingCondition);
    
        // 渲染别名输入控件
        html += ui.renderAliasInput(fieldId, existingCondition.alias || {});
    
        // 塞入表单
        this.form.innerHTML = html;
    
        // 保存按钮逻辑
        const saveCallback = (e) => {
            e.preventDefault();
            const formData = new FormData(this.form);
    
            const cond = {
                fieldType,
                subType,
                fieldName,
                operator: formData.get("operator"),
                value: formData.get("value"),
                fieldId,
                alias: {}
            };
    
            if (!window.isMultiLanguage) {
                // 单语言模式
                cond.alias = formData.get(`alias[${fieldId}]`) || "";
            } else {
                // 多语言模式
                const unified = formData.get(`aliasUnified[${fieldId}]`) || "";
                const perLanguageContainer = document.getElementById(`alias-multi-${fieldId}`);
                if (perLanguageContainer) {
                    perLanguageContainer.querySelectorAll("input").forEach(input => {
                        // name = alias[fieldKey][lang]
                        const match = input.name.match(/\[([^\]]+)\]$/);
                        if (match) {
                            const lang = match[1];
                            cond.alias[lang] = input.value || unified;
                        }
                    });
                }
                // 保存统一值和返回策略
                cond.alias.unified = unified;
                cond.alias.returnAllLanguages = formData.get(`returnAllLanguages[${fieldId}]`) === "true";
            }
    
            // 调用 FilterManager 保存
            if (existingCondition && existingCondition.id) {
                this.manager.editCondition(existingCondition.id, cond);
            } else {
                this.manager.addCondition(cond);
            }
    
            this.close();
        };
    
        // 确保字段 checkbox 被选中
        const checkbox = document.getElementById(`field-checkbox-${fieldId}`);
        if (checkbox && !checkbox.checked) {
            checkbox.click();
        }
    
        this.form.onsubmit = saveCallback;
        this.modal.classList.remove("hidden");
    }
    

    close() {
        this.modal.classList.add("hidden");
    }
}

// =======================
// 5. 单例初始化
// =======================
document.addEventListener("DOMContentLoaded", () => {
    window.filterManager = new FilterManager();   
    window.filterModal = new FilterModal(window.filterManager); 
});


// 使用方法示例
// 1. 点击字段调用
// filterModal.open('number', 'default', 'price');
// 2. 获取条件
// console.log(filterManager.getConditions());
// 3. 生成查询
// console.log(filterManager.buildQuery());
// 切换多语言输入显示/隐藏
window.toggleLanguageInputs = function(fieldKey) {
    const container = document.getElementById(`alias-multi-${fieldKey}`);
    const unifiedInputContainer = document.getElementById(`alias-unified-${fieldKey}`);
    if (!container || !unifiedInputContainer) return;

    const isHidden = container.classList.toggle("hidden");

    // 切换按钮文字
    const button = unifiedInputContainer.querySelector("button");
    if (button) button.textContent = isHidden ? "Edit per language" : "Hide per-language inputs";

    // 根据需要显示/隐藏统一输入框
    unifiedInputContainer.querySelector("input").style.display = isHidden ? "block" : "none";
};



// 统一别名更新所有语言输入框
window.applyUnifiedAlias = function applyUnifiedAlias(fieldKey, value) {
    const container = document.getElementById(`alias-multi-${fieldKey}`);
    if (!container) return;
    const inputs = container.querySelectorAll("input");
    inputs.forEach(input => input.value = value);
}
