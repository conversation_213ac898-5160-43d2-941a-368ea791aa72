// filters/filter-ui-factory.js
class FilterUIFactory {
    static create(fieldType, subType = "default", condition = {}) {
        const operators = (ConditionConfig[fieldType] && ConditionConfig[fieldType][subType]) 
            || ConditionConfig[fieldType]?.default || [];

        let html = `<select name="operator">`;
        operators.forEach(op => {
            html += `<option value="${op}" ${op === condition.operator ? 'selected' : ''}>${op}</option>`;
        });
        html += `</select>`;

        // Input 根据类型变化
        if (fieldType === "number") {
            html += `<input type="number" name="value" value="${condition.value || ''}">`;
        } else if (fieldType === "date") {
            html += `<input type="date" name="value" value="${condition.value || ''}">`;
        } else {
            html += `<input type="text" name="value" value="${condition.value || ''}">`;
        }

        return html;
    }
}
