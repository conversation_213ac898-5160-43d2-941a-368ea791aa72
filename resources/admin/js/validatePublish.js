/* 统一发布按钮校验 – 适配 5 种字段
 * 0. HTML 约定
 *    <div id="req_fields_1_19" class="need_require"
 *         data-required="1"
 *         data-minlength="20"
 *         data-maxlength="130"
 *         data-maxselect=""            ← 无上限留空
 *    ></div>
 *
 * 1. 字段类型说明（fieldName = "fields_{type}_{id}"）
 *      type = 1 → text (多语言)
 *      type = 2 → textarea (多语言)
 *      type = 3 → file
 *      type = 4 → category（隐藏 input，value=逗号分隔 ID）
 *      type = 5 → option/radio/checkbox/select
 *
 * 2. 规则
 *   · required      至少选 / 填 1 项
 *   · minlength     仅对文本输入生效，输入后才检查
 *   · maxlength     同上
 *   · maxselect     仅对 type 4、5 的多选生效
 */

export function validatePublishFields() {
  let hasError = false;

  const warn = msg => `
    <div class="bg-yellow-50 border border-yellow-300 rounded-md p-2 mt-2">
      <span class="inline-flex items-center text-sm font-medium text-yellow-800">${msg}</span>
    </div>
  `;

  document.querySelectorAll('.need_require').forEach(div => {
    const fieldName = div.id.replace(/^req_/, '');
    const [, typeStr] = fieldName.split('_');
    const fieldType = parseInt(typeStr, 10);

    const required = div.dataset.required === '1';
    const minLen = div.dataset.minlength ? parseInt(div.dataset.minlength, 10) : null;
    const maxLen = div.dataset.maxlength ? parseInt(div.dataset.maxlength, 10) : null;
    const maxSelect = div.dataset.maxselect ? parseInt(div.dataset.maxselect, 10) : null;

    let inputs, selectedCount = 0;

    switch (fieldType) {
      case 1:
      case 2:
        inputs = document.querySelectorAll(
          `input[name^="${fieldName}["],
           textarea[name^="${fieldName}["]`
        );
        break;

      case 3:
        inputs = Array.from(document.querySelectorAll(`input.temp-file-input[name^="${fieldName}["][type="file"]`));
        break;

      case 4:
        inputs = [document.querySelector(`input[name="${fieldName}"]`)];
        break;

      case 5:
        inputs = document.querySelectorAll(
          `input[name="${fieldName}${maxSelect ? '[]' : ''}"],
           select[name="${fieldName}${maxSelect ? '[]' : ''}"]`
        );
        break;

      default:
        inputs = [];
    }

    inputs.forEach(el => el?.classList?.remove('border-red-500'));
    div.innerHTML = '';

    if (fieldType === 1 || fieldType === 2) {
      selectedCount = Array.from(inputs).filter(el => el.value.trim() !== '').length;

    } else if (fieldType === 3) {
      selectedCount = 0;
      const maxUpload = parseInt(div.dataset.maxupload || 0, 10);
      if (inputs.length > 0) {
        const langFileCount = {};
        inputs.forEach(input => {
          const match = input.name.match(/\[([a-z-]+)\]/i);
          const lang = match ? match[1] : 'unknown';
          const count = input.files?.length || 0;
          langFileCount[lang] = (langFileCount[lang] || 0) + count;
          selectedCount += count;
        });

        if (maxUpload > 0) {
          for (const [lang, count] of Object.entries(langFileCount)) {
            if (count > maxUpload) {
              hasError = true;
              div.innerHTML = warn(`⚠️ 语言 ${lang} 最多只能上传 ${maxUpload} 个文件`);
              inputs[0]?.classList?.add('border-red-500');
              return;
            }
          }
        }
      }

    } else if (fieldType === 4) {
      selectedCount = inputs[0]?.value.split(',').filter(Boolean).length;

    } else if (fieldType === 5) {
      if (inputs[0]?.type === 'checkbox' || inputs[0]?.type === 'radio') {
        selectedCount = Array.from(inputs).filter(el => el.checked).length;
      } else {
        const selectEl = inputs[0];
        selectedCount = Array.from(selectEl?.selectedOptions || []).length;
      }
    }

    // 校验 required
    if (required && selectedCount === 0) {
      hasError = true;
      div.innerHTML = warn('⚠️ 此字段为必填');
      inputs[0]?.classList?.add('border-red-500');
      return;
    }

    // 校验 maxselect
    if (maxSelect && (fieldType === 4 || fieldType === 5) && selectedCount > maxSelect) {
      hasError = true;
      div.innerHTML = warn(`⚠️ 最多只能选择 ${maxSelect} 项`);
      inputs[0]?.classList?.add('border-red-500');
      return;
    }

    // 校验文本长度
    if ((fieldType === 1 || fieldType === 2) && selectedCount > 0) {
      for (const el of inputs) {
        const val = el.value.trim();
        if (!val) continue;

        if (minLen && val.length < minLen) {
          hasError = true;
          div.innerHTML = warn(`⚠️ 输入不能少于 ${minLen} 个字符`);
          el.classList.add('border-red-500');
          break;
        }
        if (maxLen && val.length > maxLen) {
          hasError = true;
          div.innerHTML = warn(`⚠️ 输入不能超过 ${maxLen} 个字符`);
          el.classList.add('border-red-500');
          break;
        }
      }
    }
  });

  if (hasError) {
    document.querySelector('.border-red-500')?.scrollIntoView({ behavior: 'smooth' });
  }

  return !hasError;
}
