@tailwind base;
@tailwind components;
@tailwind utilities;


/* RTL support */
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

html[dir="rtl"] body {
    font-family: 'Inter', sans-serif;
}

/* Adjust layout elements if needed */
html[dir="rtl"] .some-class {
    /* For example, float right instead of left */
}



.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 菜单项动画 */
.menu-item {
    transition: all 0.3s ease;
}
.menu-item:hover {
    transform: translateX(4px);
}

/* 头部阴影动画 */
.header-shadow {
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 侧边栏展开动画 */
.sidebar-collapsed {
    width: 4rem;
}
.sidebar-expanded {
    width: 16rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar-mobile {
        width: 16rem;
        position: fixed; /* 让它浮在页面上 */
        height: 100vh;
        top: 0;
        left: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 50; /* 保证覆盖内容 */
    }
    .sidebar-mobile.open {
        transform: translateX(0);
    }
}

[x-cloak] { display: none !important; }
/* 灰化上传区域并覆盖提示 */
.kaz-inherited {
    position: relative;
    opacity: .45;
    pointer-events: none;  /* 禁用一切点击和拖拽 */
}

.kaz-inherited::after {
    content: "继承主语言图片";
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .75rem;
    background: rgba(255,255,255,.6);
    color: #333;
    pointer-events: none;
}

/* 可选：语言 Tab 链条图标 */
.tab-inherited::after {
    content: "🔗";
    margin-left: 4px;
    font-size: .75rem;
}