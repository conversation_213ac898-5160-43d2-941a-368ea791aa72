<?php
return [
    'dashboard' => 'Dashboard',
    'welcome' => 'Welcome to KAZCMS!',
    'Group Name' => 'Group Names',
    'Parent Group' => 'Parent Group',
    'Sort Order' => 'Sort Order',
    'Actions' => 'Actions',
    'Edit' => 'Edit',
    'Delete' => 'Delete',
    'Create User Group' => 'Create User Group',
    'Update User Group' => 'Update User Group',
    '-- None (Root) --' => '-- None (Root) --',
    'User Group Created' => 'User Group Created',
    'User Group Updated' => 'User Group Updated',
    'User Group Deleted' => 'User Group Deleted',
    'Invalid parent group selected' => 'Invalid parent group selected',
    'Cannot delete a group with child groups.' => 'Cannot delete a group with child groups.',
    'Cannot delete a group with associated users.' => 'Cannot delete a group with associated users.',
    'Back to List' => 'Back to List',
    'Cancel' => 'Cancel',
    'Create' => 'Create',
    'Update' => 'Update',
    'User Management' => 'User Management',
    'Content Management' => 'Content Management',
    'Order Management' => 'Order Management',
    'Finance' => 'Finance',
    'Language Settings' => 'Language Settings',
    'Plugin Management' => 'Plugin Management',
    'Cache' => 'Cache',
    'System Settings' => 'System Settings',
    'Basic Settings' => 'Basic Settings',
    'Role & Permission' => 'Role & Permission',
    'Logs' => 'Logs',
    'Loading...' => 'Loading...',
    'Assign Permissions' => 'Assign Permissions',
    'Save Permissions' => 'Save Permissions',
    'Create New Group' => 'Create New Group',
    'Update Group' => 'Update Group',
    'Group Name' => 'Group Name',
    'Parent Group' => 'Parent Group',
    'Sort Order' => 'Sort Order',
    'Actions' => 'Actions',
    'Edit' => 'Edit',
    'Delete' => 'Delete',
    'Create User Group' => 'Create User Group',
    'Update User Group' => 'Update User Group',
    '-- None (Root) --' => '-- None (Root) --',
    'User Group Created' => 'User Group Created',
    'User Group Updated' => 'User Group Updated',
    'User Group Deleted' => 'User Group Deleted',
    'Invalid parent group selected' => 'Invalid parent group selected',
    'Cannot delete a group with child groups.' => 'Cannot delete a group with child groups.',
    'Cannot delete a group with associated users.' => 'Cannot delete a group with associated users.',
    'Back to List' => 'Back to List',
    'Cancel' => 'Cancel',
    'Create' => 'Create',
    'Update' => 'Update',
    'Name' => 'Name',
    'Frontend' => 'Frontend',
    'Order' => 'Order',
    'Entries' => 'Entries',
    'SEO' => 'SEO',
    'Templates' => 'Templates',
    'Fields' => 'Fields',
    'Fields List'=> 'Fields List',
    'Category Management' => 'Category Management',
    'Category Label Management' => 'Category Label Management',
    'Categories' => 'Categories',
    'Category Labels' => 'Category Labels',
    'Category Tree' => 'Category Tree',
    'Label Tree' => 'Label Tree',
    'Add Root :type' => 'Add Root :type',
    'No categories' => 'No categories',
    'No labels' => 'No labels',
    'Get started by creating your first category.' => 'Get started by creating your first category.',
    'Get started by creating your first label.' => 'Get started by creating your first label.',
    'Edit Category' => 'Edit Category',
    'Edit Label' => 'Edit Label',
    'Create Sub:type for' => 'Create Sub:type for',
    'Create New Root :type' => 'Create New Root :type',
    'Name' => 'Name',
    'Enter category name' => 'Enter category name',
    'Enter label name' => 'Enter label name',
    'URL Slug' => 'URL Slug',
    'Slug' => 'Slug',
    'auto-generated-slug' => 'auto-generated-slug',
    'Description' => 'Description',
    'Enter category description' => 'Enter category description',
    'Enter label description' => 'Enter label description',
    'Configuration' => 'Configuration',
    'Visible on Frontend' => 'Visible on Frontend',
    'Cancel' => 'Cancel',
    'Save Category' => 'Save Category',
    'Save Label' => 'Save Label',

    

];
