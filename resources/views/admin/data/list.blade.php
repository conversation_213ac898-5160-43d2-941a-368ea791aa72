@extends('admin.layouts.admin')

@section('content')
@php
    $locale = app()->getLocale();
    $statuses = ['published' => 'Published', 'draft' => 'Draft', 'deleted' => 'Deleted'];
    //$status = $status;

    // 当前多选筛选
    $selectedStatuses = request('status', ['published']);
    if (!is_array($selectedStatuses)) {
        $selectedStatuses = [$selectedStatuses];
    }

    // 当前 tab 状态，用于高亮 tab
    $tabStatus = request()->query('status');
    $tabStatus = is_array($tabStatus) ? $tabStatus[0] : $tabStatus;
    $tabStatus = $tabStatus ?: ($selectedStatuses[0] ?? 'published');
@endphp

<div class="container mx-auto p-4">

    <h1 class="text-xl font-semibold mb-4 flex items-center justify-between">
        内容列表
        <a href="{{ localized_route('admin.data.submit', ['content_type_id' => $contentTypeId, 'data_id' => 0]) }}"
           class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">
            添加数据
        </a>
    </h1>

    {{-- Tabs --}}
    <div class="mb-4 border-b border-gray-200">
        <nav class="flex space-x-4" aria-label="Tabs">
            @foreach ($statuses as $key => $label)
                <a href="{{ url()->current() }}?status={{ $key }}"
                   class="px-3 py-2 font-medium text-sm rounded-t-md
                   {{ $tabStatus === $key ? 'bg-blue-600 text-white' : 'text-gray-600 hover:text-blue-600' }}">
                    {{ $label }}
                </a>
            @endforeach
        </nav>
    </div>

    {{-- 多选筛选 --}}
    <form method="GET" action="{{ url()->current() }}" class="mb-6 flex flex-wrap gap-3 items-center">
        <input type="text" name="kw" value="{{ request('kw') }}" placeholder="搜索关键词..."
               class="border rounded px-3 py-1 w-64 focus:ring focus:border-blue-500 text-sm">

        <div class="flex items-center gap-4">
            @foreach ($statuses as $key => $label)
                <label class="inline-flex items-center cursor-pointer select-none text-sm">
                    <input type="checkbox" name="status[]" value="{{ $key }}"
                           class="form-checkbox"
                           {{ in_array($key, $selectedStatuses) ? 'checked' : '' }}>
                    <span class="ml-1">{{ $label }}</span>
                </label>
            @endforeach
        </div>

        <button type="submit"
                class="bg-blue-600 text-white text-sm px-4 py-1 rounded hover:bg-blue-700">
            🔍 搜索
        </button>
    </form>

    {{-- 列表显示 --}}
    <div class="bg-white shadow-md rounded-lg overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 text-sm">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-2 text-left w-12">ID</th>
                    <th class="px-4 py-2 text-left">内容</th>
                    <th class="px-4 py-2 text-left">更新时间</th>
                    <th class="px-4 py-2 text-left w-32">草稿</th>
                    <th class="px-4 py-2 text-left w-40">操作</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-100">
                @forelse ($contents['data'] as $dataId => $fields)
                    @php
                        $firstField = null;
                        foreach ($fields as $f) {
                            if ($f->field_type_id == 1) {
                                $firstField = $f;
                                break;
                            }
                        }
                        $data = json_decode($firstField->data ?? '', true) ?? [];
                        $hasDraft = !empty($contents['has_drafts'][$dataId]);
                    @endphp
                    <tr>
                        <td class="px-4 py-2 text-gray-600">{{ $dataId }}</td>
                        <td class="px-4 py-2">
                            @if ($firstField && !empty($data[$locale]))
                                <a href="/"
                                   class="{{ $status === 'deleted' ? 'line-through text-red-400' : '' }}">
                                    {{ Str::limit(strip_tags($data[$locale]), 60) }}
                                </a>
                                <span class="ml-2 px-2 py-0.5 rounded text-xs
                                    {{ $status === 'published' ? 'bg-green-100 text-green-700' :
                                       ($status === 'draft' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700') }}">
                                    {{ ucfirst($status) }}
                                </span>
                            @else
                                <span class="text-gray-400 italic">无内容</span>
                            @endif
                        </td>
                        <td class="px-4 py-2 text-gray-500">
                            {{ \Carbon\Carbon::parse($firstField?->updated_at ?? now())->diffForHumans() }}
                        </td>
                        <td class="px-4 py-2">
                            @if ($hasDraft)
                                <a href="{{ localized_route('admin.data.submit', ['content_type_id' => $contentTypeId, 'data_id' => $dataId]) }}?status=draft"
                                   class="text-orange-600 font-semibold">
                                    有草稿
                                </a>
                            @else
                                <span class="text-gray-400">—</span>
                            @endif
                        </td>
                        <td class="px-4 py-2 space-x-2">
                            <a href="{{ localized_route('admin.data.submit', ['content_type_id' => $contentTypeId, 'data_id' => $dataId]) }}?status={{ $status }}"
                               class="text-blue-600 hover:underline">
                                编辑
                            </a>

                            @if ($status === 'deleted')
                                <a href="/"
                                   class="text-green-600 hover:underline">
                                    还原
                                </a>
                            @endif
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="text-center py-6 text-gray-400">暂无内容</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    {{-- 分页 --}}
    <div class="mt-4">
        {{ $paginator->appends(request()->query())->links() }}
    </div>
</div>
@endsection
