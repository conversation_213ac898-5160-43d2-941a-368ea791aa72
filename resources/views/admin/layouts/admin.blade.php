@php
    $availableLanguages = getActiveLanguages();
    $locale = app()->getLocale();
@endphp
<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ getDirection() }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel')</title>
    
    
 

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    @vite(['resources/admin/css/admin.css'])
    @stack('css')
</head>
<body class="bg-gray-50 font-sans antialiased">
    <div class="flex h-screen overflow-hidden">
        <div id="sidebar" class="sidebar-expanded bg-gradient-to-b from-gray-900 to-gray-800 text-white flex-shrink-0 transition-all duration-300 ease-in-out sidebar-mobile">
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cube text-white text-xl"></i>
                    </div>
                    <div class="sidebar-text">
                        <h1 class="text-xl font-bold">KAZCMS</h1>
                        <p class="text-gray-400 text-sm">Admin Panel</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <img src="https://via.placeholder.com/40x40/4F46E5/FFFFFF?text=U" alt="用户头像" class="w-10 h-10 rounded-full">
                    <div class="sidebar-text">
                        <p class="font-medium">{{ auth()->user()->name ?? 'Admin' }}</p>
                        <p class="text-gray-400 text-sm">Super Admin</p>
                    </div>
                </div>
            </div>
            
            <!-- 菜单导航 -->
            <x-admin.sidebar />

            
            <!-- 收起菜单按钮 -->
            <div class="p-4 border-t border-gray-700">
            <button id="toggle-sidebar-btn" class="w-full flex items-center justify-center space-x-2 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-bars"></i>
                <span class="sidebar-text">收起菜单</span>
            </button>
            </div>

        </div>
        
        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航栏 -->
            <header class="header-shadow bg-white border-b border-gray-200 p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <!-- 移动端菜单按钮 -->
                        <button id="mobile-menu-btn" class="md:hidden p-2 rounded-lg hover:bg-gray-100">
                            <i class="fas fa-bars text-gray-600"></i>
                        </button>
                        
                        <!-- 面包屑导航 -->
                        <div class="flex items-center space-x-2 text-gray-600">
                            <i class="fas fa-home"></i>
                            <span class="text-gray-400">/</span>
                            <span>@yield('breadcrumb', '首页')</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- 搜索框 -->
                        <div class="relative hidden md:block">
                            <input type="text" placeholder="搜索..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        
                        <!-- 通知铃铛 -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100">
                            <i class="fas fa-bell text-gray-600"></i>
                            <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                        </button>
                        
                        <!-- 用户菜单 -->
                        <div class="relative">
                            <button id="user-menu-btn" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100">
                                <img src="https://via.placeholder.com/32x32/4F46E5/FFFFFF?text=U" alt="用户头像" class="w-8 h-8 rounded-full">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </button>

                            
                            <!-- 用户下拉菜单 -->
                            <div id="user-menu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                                <div class="p-3 border-b border-gray-200">
                                    <p class="font-medium">{{ auth()->user()->name ?? '管理员' }}</p>
                                    <p class="text-sm text-gray-500">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                                </div>
                                <ul class="py-2">
                                    <li><a href="#" class="block px-4 py-2 hover:bg-gray-50">个人资料</a></li>
                                    <li><a href="#" class="block px-4 py-2 hover:bg-gray-50">设置</a></li>
                                    <li class="border-t border-gray-200 mt-2 pt-2">
                                        <form method="POST" action="{{ route('logout') ?? '#' }}">
                                            @csrf
                                            <button type="submit" class="block w-full text-left px-4 py-2 text-red-600 hover:bg-gray-50">退出登录</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>

                        </div>
                        <!-- 语言选择 -->
                        @php
                        $currentLanguage = $availableLanguages[app()->getLocale()];
                        $currentLanguage['name'] = json_decode($currentLanguage['name'], true);
                        @endphp
                        <div class="relative group">
                                <button class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors rounded-md hover:bg-gray-50"
                                        aria-label="{{ __('Change Language') }}">
                                    <span class="text-lg mr-2">🌐</span>
                                    <span class="hidden sm:inline font-medium">{{ $currentLanguage['name'][app()->getLocale()] }}</span>
                                    <svg class="ml-1 w-4 h-4 transition-transform group-hover:rotate-180" 
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                
                                <!-- 下拉菜单 -->
                                <div class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                    <div class="py-1">
                                    
                                        @foreach ($availableLanguages as $language)
                                      
                                        @php
                                            if (is_string($language['name'])) {
                                                $language['name'] = json_decode($language['name'], true);
                                            } 
                                        @endphp
                                        
         
                                        <a href="{{ localized_current_url($language['code']) }}"
                                            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors {{ app()->getLocale() === $language['code']? 'bg-blue-50 text-blue-600 font-medium' : '' }}"
                                            @if(app()->getLocale() === $language['code']) aria-current="true" @endif>
                                            
                                                <span class="flex-1">{{ $language['name'][$language['code']]}}</span>
                                                @if(app()->getLocale() === $language['code'])
                                                    <i class="fas fa-check ml-auto text-blue-600"></i>
                                                @endif
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </header>
            
            <!-- 主要内容区域 -->
            <main class="flex-1 overflow-auto custom-scrollbar">
                <div class="p-6">              
                    <!-- 内容区域 -->
                    @yield('content')
                </div>
            </main>
            <!-- Help Text -->
            <!-- Help Text -->
            <div class="mt-8 text-center">
                <p class="text-sm text-gray-600">
                Need help? Check our 
                <a href="#" class="text-blue-600 hover:text-blue-500 font-semibold transition-colors">documentation</a>
                <span class="text-gray-400"> | © 2025 KAZCMS.COM. All rights reserved.</span>
                </p>
            </div>
        </div>
    </div>
    
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>
    <script>
        const l = console.log;
        window.kazcms = {
            locale: '{{ app()->getLocale() }}'
        };
        window.isMultiLanguage = true;  

// 如果是多语言，还要有一个语言列表
window.availableLanguages = @json($availableLanguages->pluck('code')->all());
    </script>
    <script src="//unpkg.com/alpinejs" defer></script>


    @vite('resources/lang/lang.js')
    @vite('resources/admin/js/admin.js')

    @stack('scripts')


</body>
</html>