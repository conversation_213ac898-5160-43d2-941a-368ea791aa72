@extends('admin.layouts.admin')

@section('content')
@php
    $languages = getActiveLanguages();
    $locale = app()->getLocale();
    $chapters = $tree;

@endphp



@if ($errors->any())
    <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        <ul class="list-disc list-inside text-sm">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif



<div class="flex h-screen">
    <!-- Sidebar: Chapter Tree -->
    <div class="w-80 bg-white border-r border-gray-200 shadow-sm">
        <!-- Header Controls -->
        <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="flex flex-col space-y-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">Chapters</h3>

                </div>
                
                <button id="add-root" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Add Chapter</span>
                </button>
                
                <select id="version-select" class="w-full border border-gray-200 rounded-lg px-3 py-2.5 text-sm text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    @foreach ($versions as $version)
                        <option value="{{ $version->id }}" {{ $version->id == $versionId? 'selected' : '' }}>
                            Version {{ $version->version }}
                        </option>
                    @endforeach
                    <option value="__new__">➕ Add New Version</option>
                </select>
            </div>
        </div>
        
        <!-- Chapter Tree -->
        <div class="flex-1 overflow-y-auto p-4">
            <ul id="chapter-tree" class="space-y-1">
            @foreach ($tree as $node)
                @include('admin.document_content._chapter_node', ['node' => $node, 'locale' => $locale])
            @endforeach
            </ul>
        </div>
    </div>

    <!-- Content Display -->
    <div class="flex-1 flex flex-col bg-white">
        <!-- Header -->
        <div class="px-8 py-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                {{ $chapter->name[$locale] ?? 'Unnamed Chapter' }}
            </h2>
            
            <!-- Language Tabs -->
            <div class="flex space-x-1 mt-4" id="language-tabs">
                @foreach ($languages as $lang)
                    <button type="button"
                        class="tab-button px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 {{ $loop->first ? 'bg-blue-500 text-white shadow-sm' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100' }}"
                        data-lang="{{ $lang->code }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </div>
        </div>

        <!-- Content Editor -->
        <div class="flex-1 p-8">
            @foreach ($languages as $lang)
                <textarea
                    class="lang-content w-full h-full border border-gray-200 rounded-xl p-6 bg-gray-50/50 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none {{ $loop->first ? '' : 'hidden' }}"
                    data-lang="{{ $lang->code }}"
                    placeholder="Start writing your content here...">{{ $chapter->content[$lang->code] ?? '' }}</textarea>
            @endforeach
        </div>

        <!-- Action Buttons -->
        <div class="px-8 py-6 border-t border-gray-100 bg-gray-50/50">
            <div class="flex justify-end space-x-3">
                <button id="save-chapter" class="px-6 py-2.5 bg-amber-500 text-white rounded-lg text-sm font-medium hover:bg-amber-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>Save Draft</span>
                </button>
                <button id="publish-chapter" class="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm hover:shadow-md flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    <span>Publish</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Add/Edit -->
<div id="chapter-modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white w-full max-w-2xl rounded-2xl shadow-2xl max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="px-8 py-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">Chapter Details</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Language Tabs in Modal -->
            <div class="flex space-x-1 mt-4" id="lang-tabs">
                @foreach ($languages as  $lang)
                    <button type="button"
                        class="lang-tab px-3 py-2 text-sm font-medium rounded-lg transition-all {{ $loop->first ? 'bg-blue-500 text-white' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100' }}"
                        data-lang="{{ $lang->code }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </div>
        </div>

        <!-- Modal Content -->
        <div class="p-8 overflow-y-auto max-h-[calc(90vh-200px)]">
            <form id="chapter-form" class="space-y-6">
                <input type="hidden" name="chapter_id" id="modal-id" value="{{ $chapterId ?? 0 }}">
                <input type="hidden" name="parent_id" id="modal-parent-id">
                <input type="hidden" name="content_type_id" value="{{ $content_type_id }}">
                <input type="hidden" name="data_id" value="{{ $data_id }}">
                <input type="hidden" name="is_draft" value="0">

                @foreach ($languages as $lang)
                    <div class="lang-panel {{ $loop->first ? '' : 'hidden' }}" data-lang="{{ $lang->code }}">
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Title ({{ strtoupper($lang->code) }})</label>
                                <input type="text" name="title[{{ $lang->code }}]" class="w-full border border-gray-200 rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" required>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Slug ({{ strtoupper($lang->code) }})</label>
                                <input type="text" name="slug[{{ $lang->code }}]" class="w-full border border-gray-200 rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" required>
                            </div>
                        </div>
                    </div>
                @endforeach
                
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Version</label>
                    <select name="version_id" class="w-full border border-gray-200 rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" required>
                        @foreach ($versions as $v)
                            <option value="{{ $v->id }}" {{ $v->id == $versionId ? 'selected' : '' }}>
                                Version {{ $v->version }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </form>
        </div>
        
        <!-- Modal Footer -->
        <div class="px-8 py-6 border-t border-gray-100 bg-gray-50/50 flex justify-end space-x-3">
            <button type="button" onclick="closeModal()" class="px-6 py-2.5 text-gray-700 bg-white border border-gray-200 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors">
                Cancel
            </button>
            <button type="submit" form="chapter-form" class="px-6 py-2.5 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg text-sm font-medium hover:from-green-700 hover:to-green-800 transition-all shadow-sm hover:shadow-md">
                Save Chapter
            </button>
        </div>
    </div>
</div>

<!-- Add Version Modal -->
<div id="add-version-modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
        <div class="px-6 py-6 border-b border-gray-100">
            <h2 class="text-xl font-bold text-gray-900">Add New Version</h2>
        </div>
        
        <form id="add-version-form" class="p-6">
            <div class="mb-6">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Version Number</label>
                <input type="text" name="version" placeholder="e.g. 1.01" class="w-full border border-gray-200 rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" required />
            </div>
            <div class="mb-6">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Show on Frontend</label>
                <input type="checkbox" name="is_active" value="1" class="w-5 h-5 border border-gray-300 rounded-md text-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors"/>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button type="button" id="cancel-add-version" class="px-6 py-2.5 text-gray-700 bg-white border border-gray-200 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-blue-800 transition-all shadow-sm hover:shadow-md">
                    Create Version
                </button>
            </div>
        </form>
    </div>
</div>



@endsection

@push('scripts')
<script>
        let currentVersion = {{ $versionId ?? 0 }};
    let currentChapterId = {{ $chapterId ?? 0 }};
    let contentTypeId = {{ $content_type_id ?? 0 }};
    let dataId = {{ $data_id ?? 0 }};
    let chapterId = {{ $chapterId ?? 0 }};

document.getElementById('save-chapter').addEventListener('click', function () {
    submitChapter(true); // isDraft = true
});

document.getElementById('publish-chapter').addEventListener('click', function () {
    submitChapter(false); // isDraft = false
});

function submitChapter(isDraft) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 收集多语言内容
    const content = {};
    document.querySelectorAll('.lang-content').forEach(textarea => {
        const lang = textarea.dataset.lang;
        content[lang] = textarea.value;
    });

    const updateFields = ['content', 'is_draft'];
    const data = {
        id: chapterId,
        content_type_id: contentTypeId,
        data_id: dataId,
        parent_id: 0,
        update_fields: updateFields,
        is_draft: isDraft ? 1 : 0,
        content: content,
        version_id: currentVersion,
        _token: csrfToken,
    };

    fetch(`/${window.kazcms.locale}/admin/document-content/chapter/update-fields`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify(data),
    })
    .then(res => res.json())
    .then(() => {
        location.reload();
    })
    .catch(err => {
        console.error('Error saving chapter:', err);
        alert('Failed to save. Please try again.');
    });
}

// Demo JavaScript for functionality
function closeModal() {
    document.getElementById('chapter-modal').classList.add('hidden');
}

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    // Language tabs in main content
    const tabButtons = document.querySelectorAll('#language-tabs .tab-button');
    const langContents = document.querySelectorAll('.lang-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetLang = this.dataset.lang;
            
            // Update tab appearance
            tabButtons.forEach(btn => {
                btn.classList.remove('bg-blue-500', 'text-white', 'shadow-sm');
                btn.classList.add('text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-100');
            });
            this.classList.remove('text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-100');
            this.classList.add('bg-blue-500', 'text-white', 'shadow-sm');
            
            // Update content visibility
            langContents.forEach(content => {
                if (content.dataset.lang === targetLang) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });
        });
    });
    
    // Modal language tabs
    const modalTabButtons = document.querySelectorAll('#lang-tabs .lang-tab');
    const langPanels = document.querySelectorAll('.lang-panel');
    
    modalTabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetLang = this.dataset.lang;
            
            // Update modal tab appearance
            modalTabButtons.forEach(btn => {
                btn.classList.remove('bg-blue-500', 'text-white');
                btn.classList.add('text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-100');
            });
            this.classList.remove('text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-100');
            this.classList.add('bg-blue-500', 'text-white');
            
            // Update panel visibility
            langPanels.forEach(panel => {
                if (panel.dataset.lang === targetLang) {
                    panel.classList.remove('hidden');
                } else {
                    panel.classList.add('hidden');
                }
            });
        });
    });
});


document.getElementById('version-select').addEventListener('change', function () {
    const selectedValue = this.value;

    if (selectedValue === '__new__') {
        // 打开添加版本的 modal
        document.getElementById('add-version-modal').classList.remove('hidden');
        this.value = ''; // 清空选择
        return;
    }

    const url = new URL(window.location.href);
    url.searchParams.delete('chapter_id');

    // 设置或替换 version 参数
    url.searchParams.set('version', selectedValue);

    // 跳转到新的 URL
    window.location.href = url.toString();
});


document.getElementById('version-select').addEventListener('click', function () {
    if (this.value === '__new__') {
        document.getElementById('add-version-modal').classList.remove('hidden');
        this.value = ''; // 清空选择
    } else {
        // 正常加载版本内容逻辑
        const versionId = this.value;
        loadVersion(versionId); // 你自己定义的函数
    }
});

document.getElementById('cancel-add-version').addEventListener('click', function () {
    document.getElementById('add-version-modal').classList.add('hidden');
});

document.getElementById('add-version-form').addEventListener('submit', function (e) {
    e.preventDefault();
    const version = this.version.value;
    const isActive = this.is_active.checked ? 1 : 0;

    fetch(`/${window.kazcms.locale}/admin/document-content/{{ $content_type_id }}/{{ $data_id }}/versions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ version: version, is_active: isActive })
    })
    .then(res => res.json())
    .then(data => {
        if (data.success) {
            location.reload(); // 简单做法：刷新页面加载新版本列表
        } else {
            alert('Failed to add version');
        }
    });
});






// Modal handling
function editChapter(id) {
    openModal(id, 0);
}

function addChildChapter(parentId) {
    openModal(0, parentId);
}

function openModal(id = 0, parentId = 0) {
    const modal = document.getElementById('chapter-modal');
    const form = document.getElementById('chapter-form');

    document.getElementById('modal-id').value = id || 0;
    document.getElementById('modal-parent-id').value = parentId || 0;
    modal.classList.remove('hidden');

    if (id) {
        fetch(`/admin/document-content/edit?chapter_id=${id}`)
            .then(res => res.json())
            .then(data => {
                for (const [lang, val] of Object.entries(data.title)) {
                    const input = form.querySelector(`[name="title[${lang}]"]`);
                    if (input) input.value = val || '';
                }
            });
    } else {
        form.reset();
    }
}



// Delete
function deleteChapter(id) {
    if (!confirm('Are you sure?')) return;
    fetch(`${window.kazcms.locale}/admin/document-content/{{ $content_type_id }}/{{ $data_id }}/delete-chapter/${id}`)
        //.then(() => location.reload());
}

// Save chapter
document.getElementById('chapter-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const updateFields = ['title', 'slug', 'is_draft','version_id'];
    const data = {
        id: parseInt(document.querySelector('[name="chapter_id"]').value),
        content_type_id: parseInt(document.querySelector('[name="content_type_id"]').value),
        data_id: parseInt(document.querySelector('[name="data_id"]').value),
        parent_id: parseInt(document.querySelector('[name="parent_id"]').value),
        update_fields: updateFields,
        _token: csrfToken,
        ...collectFieldData(updateFields)
    };

    fetch(`/${window.kazcms.locale}/admin/document-content/chapter/update-fields`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify(data),
    }).then(res => res.json())
      .then(() => location.reload());
});

function collectFieldData(fieldNames) {
    const data = {};
    fieldNames.forEach(field => {
        const inputs = document.querySelectorAll(`[name^="${field}["]`);
        if (inputs.length > 0) {
            const fieldObj = {};
            inputs.forEach(input => {
                const langMatch = input.name.match(new RegExp(`${field}\\[(.+)\\]`));
                if (langMatch) {
                    const lang = langMatch[1];
                    fieldObj[lang] = input.value;
                }
            });
            data[field] = fieldObj;
        } else {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) data[field] = input.value;
        }
    });
    return data;
}

// Add root
document.getElementById('add-root').addEventListener('click', () => {
    openModal(0, 0);
});
</script>

@endpush
