<li data-id="{{ $node->id }}" data-parent-id="{{ $node->parent_id }}" data-id="{{ $node->id }}" class="chapter-node">
    {{-- 第一行：章节标题和按钮，hover 灰色和 group 放一起 --}}
    <div class="group flex justify-between items-center hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-lg px-3 py-2.5 transition-all duration-200 border border-transparent hover:border-blue-100 hover:shadow-sm">
        <a href="?chapter_id={{ $node->id }}&version={{ $versionId }}" class="flex-1">
            <div class="flex items-center gap-3 cursor-pointer">
                <!-- Chapter icon -->
                <div class="w-2 h-2 bg-blue-400 rounded-full opacity-60 group-hover:opacity-100 group-hover:bg-blue-500 transition-all duration-200"></div>
                
                <span class="font-medium text-gray-800 group-hover:text-gray-900 transition-colors duration-200 text-sm leading-relaxed">
                    {{ $node->name[$locale] ?? 'Unnamed Chapter' }}
                </span>
            </div>
        </a>
        
        <div class="hidden group-hover:flex items-center gap-1.5 text-xs ml-3">
            <button class="text-blue-600 hover:text-blue-700 hover:bg-blue-100 px-2 py-1 rounded-md transition-all duration-200 font-medium" onclick="editChapter({{ $node->id }})">
                编辑
            </button>
            <button class="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100 px-2 py-1 rounded-md transition-all duration-200 font-medium" onclick="addChildChapter({{ $node->id }})">
                添加子章节
            </button>
            <button class="text-red-500 hover:text-red-600 hover:bg-red-100 px-2 py-1 rounded-md transition-all duration-200 font-medium" onclick="deleteChapter({{ $node->id }})">
                删除
            </button>
        </div>
    </div>
    
    {{-- 第二行：子章节 --}}
    @if($node->children->isNotEmpty())
        <ul class="ml-6 mt-2 space-y-1 border-l border-gray-200 pl-4 relative">
            <!-- Connector line styling -->
            <div class="absolute -left-px top-0 bottom-0 w-px bg-gradient-to-b from-gray-200 via-gray-100 to-transparent"></div>
            
            @foreach ($node->children as $child)
                @include('admin.document_content._chapter_node', ['node' => $child, 'locale' => $locale])
            @endforeach
        </ul>
    @endif
</li>