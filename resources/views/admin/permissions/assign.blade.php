<div class="container mx-auto px-4 py-6">
    <h2 class="text-xl font-bold mb-4">{{ __('messages.Assign Permissions') }} - {{ $assignee_type }} #{{ implode(',', $assignee_ids) }}</h2>

    <form id="permissionForm" method="POST" action="{{ localized_route('admin.permissions.save') }}">
        @csrf

        <input type="hidden" name="assignee_type" value="{{ $assignee_type }}">
        <input type="hidden" name="assignee_id" value="{{ implode(',', $assignee_ids) }}">

        @php
            $lang = app()->getLocale();

            $childrenMap = [];
            foreach ($permissions as $perm) {
                $childrenMap[$perm->parent_id ?? 0][] = $perm;
            }

            function getLabel($perm, $lang) {
                $labels = json_decode($perm->label, true);
                return $labels[$lang] ?? ($labels['en'] ?? $perm->name);
            }

            function renderPermissions($parentId, $childrenMap, $assigned, $lang, $level = 0) {
                if (!isset($childrenMap[$parentId])) return;

                echo '<div class="ml-' . ($level * 4) . ' border-l border-gray-200 pl-4">';
                foreach ($childrenMap[$parentId] as $perm) {
                    $checked = in_array($perm->id, $assigned) ? 'checked' : '';
                    echo '<div class="mb-1">';
                    echo '<label class="flex items-center space-x-2">';
                    echo '<input type="checkbox" name="permission_ids[]" value="' . $perm->id . '" ' . $checked . ' class="form-checkbox h-4 w-4 text-blue-600" autocomplete="off">';
                    echo '<span>' . getLabel($perm, $lang) . '</span>';
                    echo '</label>';

                    renderPermissions($perm->id, $childrenMap, $assigned, $lang, $level + 1);

                    echo '</div>';
                }
                echo '</div>';
            }
        @endphp

        @php renderPermissions(0, $childrenMap, $assigned, $lang); @endphp
    </form>
</div>

