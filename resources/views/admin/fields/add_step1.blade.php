@extends('admin.layouts.admin')

@section('title', __('Add Field'))
@section('breadcrumb', __('Add Field'))

@section('content')
<div class="max-w-full mx-auto p-3 bg-white shadow rounded mt-3">

    <h1 class="text-2xl font-bold mb-4">
        {{ __('Add Field for Content Type') }}: 
        {{ $contentType->name[$locale] ?? $contentType->name['en-us'] ?? 'Unnamed' }}
    </h1>

    <form id="fieldTypeForm" method="POST" action="{{ localized_route('admin.fields.add_step2', ['contentTypeId' => $contentType->id, 'fieldTypeId' => 'FIELD_TYPE_ID_PLACEHOLDER', 'fieldId' => '0']) }}" autocomplete="off">

        @csrf

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach($fieldTypes as $type)
            @php
                $typeName = $type->type_name_json;
                $description = $type->description_json;

            @endphp
            <label class="block cursor-pointer border rounded p-4 hover:shadow-lg transition-shadow duration-200">
            <input type="radio" name="field_type_id" value="{{ $type->id }}" class="sr-only peer" required  autocomplete="off">     
                <div class="peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-300 p-4">
                    <div class="text-lg font-semibold">{{ $typeName[$locale] }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ $description[$locale] }}</div>
                </div>
            </label>
            @endforeach
        </div>

        <div class="mt-6">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                {{ __('Next Step') }}
            </button>
        </div>
    </form>

</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('fieldTypeForm');
    
    // 保存最初的 action 模板
    const originalAction = form.getAttribute('action');

    form.addEventListener('submit', function(e) {
        const selected = document.querySelector('input[name="field_type_id"]:checked');
        if (!selected) {
            e.preventDefault();
            alert('Please select a field type');
            return;
        }

        const fieldTypeId = selected.value;

        // 每次提交前重置 action，避免使用被替换后的旧值
        form.action = originalAction.replace('FIELD_TYPE_ID_PLACEHOLDER', fieldTypeId);
    });
});
</script>
@endpush


