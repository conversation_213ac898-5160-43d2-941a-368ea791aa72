@extends('admin.layouts.admin')

@section('title', __('Submit Data'))

@section('content')
@php
//getActiveLanguages();
$enabledLanguages = getActiveLanguages();
@endphp 
@if ($errors->has('msg'))
    <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ $errors->first('msg') }}
    </div>
@endif
<form action="{{ localized_route('admin.fields.data.save', ['content_type_id' => $content_type_id, 'data_id' => $data_id]) }}" method="POST" enctype="multipart/form-data" class="kaz-upload-form" id="kazcms-data-form">
    @csrf
@php
 $show_category_modal = false;
@endphp 
    @foreach($fields as $field)
        @php
          $viewName = 'admin.fields.input.' . $fieldTypes[$field->field_type_id]['related_table']; // e.g., text, textarea, file, etc.
          if($field->field_type_id == 4) {
            $show_category_modal = true;
          }
          @endphp


        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex">
                @if(view()->exists($viewName))
                    @include($viewName, [
                        'field' => $field,
                    'enabledLanguages'=>$enabledLanguages,
                    'data_id'=>$data_id,
                    'data'=>$data,

                    ])
                @else
                    <p>{{ __('Unknown field type:') }} {{ $field->field_type_id }}</p>
                @endif
            </div>
        </div>
    @endforeach

    <div class="flex gap-4 mt-6">
  <!-- 保存草稿 -->
  <button
    id="save-draft-btn"
    type="button"
    class="inline-flex items-center justify-center px-6 py-2
           rounded-lg font-medium text-sm transition
           bg-gray-200 text-gray-700 hover:bg-gray-300
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400
           shadow-sm hover:shadow-md active:shadow-inner kazcms-data-submit-btn"
           data-action="draft">
    💾 保存草稿
  </button>

  <!-- 发布 -->
  <button
    id="publish-btn"
    type="button"
    class="inline-flex items-center justify-center px-6 py-2
           rounded-lg font-medium text-sm transition
           bg-blue-600 text-white hover:bg-blue-700
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
           shadow-sm hover:shadow-md active:shadow-inner kazcms-data-submit-btn"
   data-action="publish">
    🚀 发布
  </button>
</div>
<input type="hidden" name="user_action" id="user_action" value="">
</form>

@if($show_category_modal)
    <!-- Modal -->
    <div id="categoryModal" class="hidden fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white w-full max-w-lg p-4 rounded shadow relative">
            <h2 class="text-xl font-bold mb-4">选择分类</h2>
            <div id="categoryTree" class="space-y-2 max-h-[400px] overflow-auto"></div>
            <div class="mt-4 text-right">
                <button type="button"  id="closeCategoryModal" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">关闭</button>
                <button type="button"  id="confirmCategoryBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">确定</button>
            </div>
        </div>
    </div>
@endif
@endsection

@push('css')
<link rel="stylesheet" href="{{ asset('static/css/kaz-image-craft.css') }}?v=1.0.2">
@endpush

@push('scripts')
<script src="{{ asset('static/js/kaz-image-craft.js') }}?v=1.0.2"></script>
<script src="{{ asset('static/js/kaz-image-craft-lang-' . app()->getLocale() . '.js') }}?v=1.0.2"></script>

@vite('resources/admin/js/validatePublish.js')
@vite('resources/admin/js/content-form.js')

@endpush


