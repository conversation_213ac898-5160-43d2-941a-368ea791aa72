@extends('admin.fields.create.create')

@section('title', __('messages.multilang_checker'))
@section('breadcrumb', __('messages.multilang_checker'))

@section('create_form')

<!-- Basic Information Section -->
<div class="space-y-6">
    <div class="border-b border-gray-200 pb-4">
        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
        <p class="mt-1 text-sm text-gray-600">Configure the field category name and display settings</p>
    </div>

    <!-- Field Name Multi-language -->
    <div class="space-y-4">
        <label class="block text-sm font-semibold text-gray-900">Field Name (Multi-language)</label>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach($enabledLanguages as $lang)
                @php
                    $langCode = $lang->code;
                    $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                    $fieldVal = old("label_json.{$langCode}", ($field?->label_json)[$langCode] ?? '');
                @endphp
                <div class="space-y-2">
                    <label for="label_json_{{ $langCode }}" class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        {{ $langName }} ({{ $langCode }})
                    </label>
                    <input type="text" name="label_json[{{ $langCode }}]" id="label_json_{{ $langCode }}"
                           value="{{ $fieldVal }}"
                           placeholder="Enter field name for {{ $langName }}"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
            @endforeach
        </div>
    </div>

    <!-- Field Slug Multi-language -->
    <div class="space-y-4">
        <label class="block text-sm font-semibold text-gray-900">Field Slug (Multi-language)</label>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach($enabledLanguages as $lang)
                @php
                    $langCode = $lang->code;
                    $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                    $fieldVal = old("label_slug_json.{$langCode}", ($field?->label_slug_json)[$langCode] ?? '');
                @endphp
                <div class="space-y-2">
                    <label for="label_slug_json_{{ $langCode }}" class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        {{ $langName }} ({{ $langCode }})
                    </label>
                    <input type="text" name="label_slug_json[{{ $langCode }}]" id="label_slug_json_{{ $langCode }}"
                           value="{{ $fieldVal }}"
                           placeholder="Enter field slug for {{ $langName }}"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
            @endforeach
        </div>
    </div>

    <!-- Option Type -->
    <div class="space-y-2">
        <label for="display_style" class="block text-sm font-medium text-gray-900">Option Type</label>
        <select name="display_style" id="display_style" required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
            <option value="radio" {{ (old('display_style', $field?->display_style ?? '') == 'radio') ? 'selected' : '' }}>
                Radio (Single select)
            </option>
            <option value="checkbox" {{ (old('display_style', $field?->display_style ?? '') == 'checkbox') ? 'selected' : '' }}>
                Checkbox (Multiple select)
            </option>
        </select>
        <p class="text-xs text-gray-500">Choose how users can select options from this field</p>
    </div>

    <!-- Category Selection -->
    <div class="space-y-2">
        <label for="category_id" class="block text-sm font-medium text-gray-900">Select Category</label>
        <select name="category_id" id="category_id"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
            @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ (old('category_id', $field?->category_id ?? '') == $category->id) ? 'selected' : '' }}>
                    {{ ($category->name)[app()->getLocale()] }}
                </option>
            @endforeach
        </select>
        <p class="text-xs text-gray-500">Choose the parent category for this field</p>
    </div>
</div>

<!-- Field Configuration Section -->
<div class="space-y-6">
    <div class="border-b border-gray-200 pb-4">
        <h3 class="text-lg font-medium text-gray-900">Field Configuration</h3>
        <p class="mt-1 text-sm text-gray-600">Set up validation rules and display options</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Max Select Count -->
        <div class="space-y-2">
            <label for="max_select_count" class="block text-sm font-medium text-gray-900">
                Max Select Count
            </label>
            <input type="number" min="1" name="max_select_count" id="max_select_count"
                   value="{{ old('max_select_count', $field?->max_select_count ?? '') }}"
                   placeholder="Unlimited"
                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
            <p class="text-xs text-gray-500">For multiple select, leave empty for unlimited selections</p>
        </div>

        <div class="space-y-6">
            <!-- Field Options -->
            <div class="space-y-4">
                <!-- Show in Frontend -->
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input type="checkbox" id="show_in_frontend" name="show_in_frontend" value="1"
                               {{ old('show_in_frontend', $field?->show_in_frontend ?? true) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                    </div>
                    <div class="ml-3">
                        <label for="show_in_frontend" class="text-sm font-medium text-gray-900">
                            Show in Frontend
                        </label>
                        <p class="text-xs text-gray-500">Display this field in the frontend tree view</p>
                    </div>
                </div>

                <!-- Is Required -->
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input type="checkbox" id="is_required" name="is_required" value="1"
                               {{ old('is_required', $field?->is_required ?? false) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                    </div>
                    <div class="ml-3">
                        <label for="is_required" class="text-sm font-medium text-gray-900">
                            Required Field
                        </label>
                        <p class="text-xs text-gray-500">Make this field mandatory for form submission</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@php
    $formFields = [
        'label_json',
        'label_slug_json',
    ];
@endphp
@endsection
