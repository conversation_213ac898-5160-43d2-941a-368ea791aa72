@extends('admin.layouts.admin')

@section('title', __('messages.Fields List'))
@section('breadcrumb', __('messages.Fields List'))

@section('content')
@php

    $locale = app()->getLocale();
@endphp

<div class="max-w-full mx-auto p-3 bg-white shadow rounded mt-3" x-data="orderUpdater">
    <h1 class="text-2xl font-bold mb-4">
        {{ __('messages.Fields List') }}: 
        {{ $content_type->name[$locale] ?? $content_type->name['en-us'] ?? 'Unnamed Content Type' }}
        <a href="{{ localized_route('admin.fields.add_step1', ['contentTypeId' =>  $content_type->id]) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>Add Field</a>
    </h1>

    @if(empty($fields) === 0)
        <p class="text-gray-500">{{ __('messages.No fields found') }}</p>
    @else
        <table class="min-w-full border border-gray-200 divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-2 py-3">
                    <input type="checkbox" id="kazcms-fields-select-all" />
                </th>
                <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field ID</th>
                <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field Name</th>
                <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field Type</th>
                <th class="px-1 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort Order</th>
                <th class="px-1 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">searchable</th>
                <th class="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>

        <tbody class="bg-white divide-y divide-gray-200">
            @foreach($fields as $key => $field)
            @php
                $fieldNameJson = json_decode($field->label_json ?? '{}', true);
                $fieldName = $fieldNameJson[$locale] ?? $fieldNameJson['en-us'] ?? 'Unnamed Field';

                $fieldType = $fieldTypes[$field->field_type_id] ?? null;
                $typeNameJson = $fieldType->type_name_json ?? '{}';
                $typeNameArr = $typeNameJson;
                $typeName = $typeNameArr[$locale] ?? 'N/A';
            @endphp
            <tr id="field-row-{{ $field->id }}-{{ $field->field_type_id }}-{{ $field->content_type_id }}">
            <td class="px-2 py-4 text-center">
                    <input type="checkbox"
                           class="kazcms-fields-checkbox h-4 w-4"
                           value="{{ $field->field_type_id }}-{{ $field->id }}">
                </td>
                <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">{{ $field->field_id ?? $field->id ?? '-' }}</td>
                <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">{{ $fieldName }}</td>
                <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">{{ $typeName }}</td>
                <td>
                    
                <input type="number"
                x-ref="order_{{ $field->id }}"
                value="{{ $field->sort_order ?? '' }}"
                class="border rounded px-2 py-1 w-20"
                autocomplete="off"
                min="0"
            >

            <button type="button"
                class="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 ml-2"
                data-field="[{{ $field->content_type_id }}, {{ $field->field_type_id }}, {{ $field->id }}]"
                @click="submitSingle($event)">
                Update
            </button>
                </td>
                <td>
                @if(($field->is_seo ?? 0) === 0)
                    <div x-data="allowSearchComponent()">
                        <div class="flex items-center space-x-2 h-12">
                            <input
                            type="checkbox"
                            id="allow_search_{{ $field->id }}"
                            name="allow_search_{{ $field->id }}"
                            value="1"
                            class="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
                            data-field='[{{ $field->content_type_id }}, {{ $field->field_type_id }}, {{ $field->id }}]'
                            @change="updateSearch" 
                            {{ $allowedSearchFields[$key] ?? false ? 'checked' : '' }}
                            >
                            <span x-show="showSuccess" x-transition class="text-green-600 text-sm select-none" style="display:none;">更新成功</span>
                        </div>
                    </div>
                @endif


                    
                </td>
                <td class="px-2 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <a href="{{ localized_route('admin.fields.add_step2', [$field->content_type_id, $field->field_type_id, $field->id]) }}" class="text-blue-600 hover:underline">Edit</a>
                    <a href="javascript:void(0)"
                    class="text-red-600 hover:underline delete-field-btn"
                    data-id="{{ $field->id }}-{{ $field->field_type_id }}-{{ $field->content_type_id }}"
                    data-field-id="{{ $field->id }}"
                    data-field-type-id="{{ $field->field_type_id }}"
                    data-content-type-id="{{ $field->content_type_id }}"
                    data-field-name="{{ $fieldName }}">
                    Delete
                    </a>

                </td>
            </tr>
            @endforeach
            <tr>
                <td colspan="2">
                    <form method="POST" action="{{ localized_route('admin.fields.generateTemplates',['id'=>$content_type_id]) }}">
                        @csrf
                            <button type="submit"
                                    class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
                                    id="kazcms-templates-submit">
                                生成模板
                            </button>
                            <input type="text" name="selected_fields" id="selected_fields"value="">
                    </form>
                </td>
                <td colspan="4" class="text-center p-4">
                <button type="button"
                    class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                    @click="submitAll">
                    Update All Orders
                </button>
                </td>
            </tr>

      
        </tbody>

        </table>

    @endif
</div>
@endsection

@push('scripts')
<script>
document.getElementById('kazcms-fields-select-all').addEventListener('change', function(e) {
    const checked = e.target.checked;
    document.querySelectorAll('.kazcms-fields-checkbox').forEach(cb => {
      cb.checked = checked;
    });
  });

  document.getElementById('kazcms-templates-submit').addEventListener('click', function() {
    const selected = [];
    document.querySelectorAll('.kazcms-fields-checkbox').forEach(cb => {
      selected.push(cb.value);
    });
    document.getElementById('selected_fields').value = selected.join(',');
    console.log(selected); // 这里拿到所有选中的值

    // 如果你想通过 form 提交，可以把这个数组序列化到隐藏域
    // 或用 ajax 发给后台
  });

function allowSearchComponent() {
  return {
    showSuccess: false,
    async updateSearch(event) {
      const checkbox = event.target;
      const checked = checkbox.checked ? 1 : 0;
      const [contentTypeId, fieldTypeId, fieldId] = JSON.parse(checkbox.getAttribute('data-field'));

      try {
        const res = await fetch('{{ localized_route("admin.fields.allowedSearch") }}', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
          },
          body: JSON.stringify({
            content_type_id: contentTypeId,
            field_type_id: fieldTypeId,
            field_id: fieldId,
            allowed: checked,
          }),
        });

        if (!res.ok) throw new Error('网络错误');
        const data = await res.json();
        if (data.success) {
          this.showSuccess = true;
          setTimeout(() => { this.showSuccess = false }, 3000);
        } else {
          alert('更新失败');
        }
      } catch (e) {
        alert('更新失败');
      }
    }
  }
}
</script>



<script>
document.querySelectorAll('.delete-field-btn').forEach(btn => {
    btn.addEventListener('click', function () {
        const fieldId = this.dataset.fieldId;
        const fieldTypeId = this.dataset.fieldTypeId;
        const contentTypeId = this.dataset.contentTypeId;
        const fieldName = this.dataset.fieldName;

        if (!confirm(`确定要删除字段 “${fieldName}” 吗？`)) {
            return;
        }

        fetch("{{ localized_route('admin.fields.delete') }}", {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                field_id: fieldId,
                field_type_id: fieldTypeId,
                content_type_id: contentTypeId,
            }),
        })
        .then(res => {
            if (!res.ok) throw new Error('网络错误');
            return res.json();
        })
        .then(data => {
            if (data.success) {
                const row = document.getElementById(`field-row-${fieldId}-${fieldTypeId}-${contentTypeId}`);
                if (row) row.remove();
                alert('字段删除成功');
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败');
        });
    });
});


document.addEventListener('alpine:init', () => {

    Alpine.data('orderUpdater', () => ({
        endpoint: '{{ localized_route("admin.fields.updateOrder") }}',

        submitSingle(event) {
            const button = event.currentTarget;
            const fieldData = JSON.parse(button.getAttribute('data-field'));
            const fieldId = fieldData[2];
            const input = this.$refs[`order_${fieldId}`];
            const sortOrder = parseInt(input.value);

            const payload = [{
                content_type_id: fieldData[0],
                field_type_id: fieldData[1],
                field_id: fieldData[2],
                sort_order: sortOrder,
            }];

            this.send(payload);
        },

        submitAll() {
            const buttons = document.querySelectorAll('[data-field]');
            const payload = [];

            buttons.forEach(button => {
                const fieldData = JSON.parse(button.getAttribute('data-field'));
                const fieldId = fieldData[2];
                const input = this.$refs[`order_${fieldId}`];
                const sortOrder = parseInt(input.value);

                payload.push({
                    content_type_id: fieldData[0],
                    field_type_id: fieldData[1],
                    field_id: fieldData[2],
                    sort_order: sortOrder,
                });
            });

            this.send(payload);
        },

        async send(payload) {
            try {
                const response = await fetch(this.endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify({ orders: payload }),
                });

                if (!response.ok) {
                    console.error('Failed');
                    alert('Failed to update');
                    return;
                }

                location.reload();
            } catch (e) {
                console.error(e);
                alert('Error occurred');
            }
        }
    }));
});



</script>
@endpush

