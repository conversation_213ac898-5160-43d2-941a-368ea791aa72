@php
    $field_ids = "{$field->field_type_id}_{$field->id}";
    $field_name = "fields_{$field->field_type_id}_{$field->id}";
    $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';

    $required = $field->is_required ?? false;
    $maxSelectCount = $field->max_select_count ?? null;
    $help_texts = json_decode($field->help_text_json ?? '{}', true);
@endphp


    <div class="w-48 flex items-start pt-3">
      <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
        {{ $label }} @if($required)<span class="text-red-500 ml-1">*</span>@endif
      </label>
    </div>
    <div class="flex-1">
    <div class="relative flex items-center justify-between w-full px-4 py-3 bg-white transition-all duration-200">
    <span
            id="categorySelectButton_{{ $field->field_type_id }}_{{ $field->id }}"
            data-categoryid="{{ $field->category_id }}"
            data-displaystyle="{{ $field->display_style }}"
            class="category-select-button cursor-pointer text-gray-700 font-medium hover:text-blue-700 transition-colors duration-200 select-none"
        >
            选择分类
        </span>
        @php
                $selectedCategories = $data["fields_{$field->field_type_id}_{$field->id}"]['all'] ?? [];

            @endphp
        <div id="selectedCategories_{{ $field->field_type_id }}_{{ $field->id }}" class="@if(empty($selectedCategories)) hidden @endif flex-1 ml-4 text-lg text-gray-600 truncate">
            <!--分类列表用逗号分割显示-->
       
            @if(isset($selectedCategories) && is_array($selectedCategories))
                @foreach($selectedCategories as $cat_id)
                <span class="inline-flex items-center px-3 py-1 mr-2 mb-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                    {{ $data['categories'][$cat_id]['name'][app()->getLocale()] ?? 'Untitled Category' }}
                    <button class="ml-2 w-4 h-4 text-blue-600 hover:text-red-600 kazcms-remove-category" data-inputid="field_{{ $field->field_type_id }}_{{ $field->id }}" data-categoryid="{{ $cat_id }}">×</button></span>
                @endforeach
            @endif
        </div>
        <input type="text" id="field_{{ $field->field_type_id }}_{{ $field->id }}" name="{{ $field_name }}" value="{{ implode(',', $selectedCategories) }}" >
        
        
    </div>
    @if($required || $maxSelectCount)
            <div
                id="req_{{ $field_name }}"
                class="need_require text-sm mt-1 text-red-600"
                data-required="{{ $required ? '1' : '0' }}"
                data-maxselect="{{ $maxSelectCount ?? '' }}"
            ></div>
        @endif
    @if($help_texts)
        <p class="mt-1 text-sm text-gray-500">{{ $help_texts[app()->getLocale()] }}</p>
    @endif


    </div>




