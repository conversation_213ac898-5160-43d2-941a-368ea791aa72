@php
    $fieldId = "{$field->field_type_id}_{$field->id}";
    $style = $field->display_style; // radio / select / checkbox 
    $isMultiple = in_array($style, ['checkbox']);
    //$field_name = "fields_{$field->field_type_id}_{$field->id}";
   
    $inputName  = "fields_{$field->field_type_id}_{$field->id}" . ($isMultiple ? '[]' : '');
    $dataValue = $data["fields_{$field->field_type_id}_{$field->id}"]['all']??[];

    $selected = old("fields.{$fieldId}", $dataValue ?? []);
    $selected = is_array($selected) ? $selected : [$selected];
    $options = collect(json_decode($field->options, true) ?? [])
        ->filter(fn($opt) => $opt['enabled'] ?? false)
        ->sortBy('sort_order');
    $label = json_decode($field->label_json,true)[app()->getLocale()] ?? '';
    $required = $field->is_required ?? false;
    $prefixText = json_decode($field->prefix_text_json,true)[app()->getLocale()] ?? '';
    $suffixText = json_decode($field->suffix_text_json,true)[app()->getLocale()] ?? '';
    $maxSelectCount = $field->max_select_count ?? 0; // 0 表示不限
@endphp

<div class="mb-6 option-group"
     data-field-id="{{ $fieldId }}"
     data-max-select="{{ $maxSelectCount }}"
     data-multiple="{{ $isMultiple ? 'true' : 'false' }}"
>
    @if($label)
        <label class="block text-lg font-semibold text-gray-900 mb-2">
            {{ $label }}
            @if($required)
                <span class="text-red-600">*</span>
            @endif
        </label>
    @endif

    @if($isMultiple && $maxSelectCount > 0)
        <p class="text-xs text-gray-500 mb-3">
            最多可选 {{ $maxSelectCount }} 项
        </p>
    @endif

    @if($prefixText)
        <p class="text-sm text-gray-600 mb-2">{{ $prefixText }}</p>
    @endif

    @if(in_array($style, ['select', 'multi-select']))
        {{-- 下拉框 --}}

        <select name="{{ $inputName }}"
                id="field_select_{{ $fieldId }}"
                class="block w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500"
                {{ $isMultiple ? 'multiple' : '' }}
                >
            @foreach($options as $option)
                @php
                    $optVal = $option['value'] ?? '';
                    $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
                    $selectedAttr = in_array($optVal, $selected) ? 'selected' : '';
                @endphp
                <option value="{{ $optVal }}" {{ $selectedAttr }}>{{ $optLabel }}</option>
            @endforeach
        </select>

    @else
        {{-- 单选或多选框 --}}
        <div class="space-y-3">
            @foreach($options as $option)
                @php
                    $optVal = $option['value'] ?? '';
                    $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
                    $checked = in_array($optVal, $selected) ? 'checked' : '';
                    $type = $isMultiple ? 'checkbox' : 'radio';
                @endphp
                <div class="flex items-center">
                    <input type="{{ $type }}"
                           name="{{ $inputName }}"
                           value="{{ $optVal }}"
                           id="field_{{ $fieldId }}_{{ $loop->index }}"
                           class="option-input h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                           {{ $checked }}
                           >
                    <label for="field_{{ $fieldId }}_{{ $loop->index }}"
                           class="ml-2 block text-sm text-gray-700 select-none">
                        {{ $optLabel }}
                    </label>
                </div>
            @endforeach
        </div>
    @endif

    @if($suffixText)
        <p class="text-sm text-gray-600 mt-3">{{ $suffixText }}</p>
    @endif

    @error("fields.{$fieldId}")
        <p class="text-sm text-red-600 mt-2">{{ $message }}</p>
    @enderror
</div>
