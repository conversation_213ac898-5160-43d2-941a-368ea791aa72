@php
    $fieldId = $field->id;
    $inputName = 'fields[' . $fieldId . ']' . (in_array($field->display_style, ['checkbox', 'multi-select']) ? '[]' : '');
    $selected = old("fields.{$fieldId}", $value ?? []);
    $selected = is_array($selected) ? $selected : [$selected];
    $options = collect(json_decode($field->options, true) ?? [])
        ->filter(fn($opt) => $opt['enabled'] ?? false)
        ->sortBy('sort_order');
    $label = json_decode($field->label_json,true)[app()->getLocale()] ?? '';
    $isRequired = $field->is_required ?? false;
    $prefixText = json_decode($field->prefix_text_json,true)[app()->getLocale()] ?? '';
    $suffixText = json_decode($field->suffix_text_json,true)[app()->getLocale()] ?? '';
    $maxSelectCount = $field->max_select_count ?? 0; // 0 表示不限
    $isMultiple = in_array($field->display_style, ['checkbox', 'multi-select']);
@endphp

<div class="mb-6 option-group"
     data-field-id="{{ $fieldId }}"
     data-max-select="{{ $maxSelectCount }}"
     data-multiple="{{ $isMultiple ? 'true' : 'false' }}"
>
    <!-- 这里是之前的内容 -->
    @if($label)
        <label class="block text-lg font-semibold text-gray-900 mb-2">
            {{ $label }}
            @if($isRequired)
                <span class="text-red-600">*</span>
            @endif
        </label>
    @endif

    @if($isMultiple && $maxSelectCount > 0)
        <p class="text-xs text-gray-500 mb-3">
            最多可选 {{ $maxSelectCount }} 项
        </p>
    @endif
    @if($prefixText)
        <p class="text-sm text-gray-600 mb-2">{{ $prefixText }}</p>
    @endif

    <div class="space-y-3">
        @foreach($options as $option)
            @php
                $optionValue = $option['value'] ?? '';
                $optionLabel = $option['label'][app()->getLocale()] ?? $optionValue;
                $isChecked = in_array($optionValue, $selected);
                $inputType = $isMultiple ? 'checkbox' : 'radio';
            @endphp
            <div class="flex items-center">
                <input
                    type="{{ $inputType }}"
                    name="{{ $inputName }}"
                    value="{{ $optionValue }}"
                    id="field_{{ $fieldId }}_{{ $loop->index }}"


                    class="option-input h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                    {{ $isChecked ? 'checked' : '' }}
                    {{ $isRequired && !$isMultiple ? 'required' : '' }}
                >
                <label for="field_{{ $fieldId }}_{{ $loop->index }}" class="ml-2 block text-sm text-gray-700 select-none">
                    {{ $optionLabel }}
                </label>
            </div>
        @endforeach
    </div>

    @if($suffixText)
        <p class="text-sm text-gray-600 mt-3">{{ $suffixText }}</p>
    @endif

    @error("fields.{$fieldId}")
        <p class="text-sm text-red-600 mt-2">{{ $message }}</p>
    @enderror
</div>
