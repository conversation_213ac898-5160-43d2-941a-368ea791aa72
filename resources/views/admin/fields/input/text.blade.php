@php
  $field_ids = "{$field->field_type_id}_{$field->id}";
  $field_name = "fields_".$field->field_type_id."_".$field->id;
  $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Text Field';
  $placeholders = json_decode($field->placeholder_json, true);
  $help_texts = json_decode($field->help_text_json, true);
  $prefixes = json_decode($field->prefix_text_json, true);
  $suffixes = json_decode($field->suffix_text_json, true);
  $required = $field->is_required ?? false;
  $min = $field->min_length ?? null;
  $max = $field->max_length ?? null;
@endphp


    <!-- 左侧标签 -->
    <div class="w-48 flex items-start pt-3">
      <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
        {{ $label }} @if($required)<span class="text-red-500 ml-1">*</span>@endif
      </label>
    </div>

    <!-- 右侧 Tabs 内容 -->
    <div class="flex-1">
      <!-- Tabs 标题栏 -->
      <div class="border-b border-gray-200">
        <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs" role="tablist">
          @foreach($enabledLanguages as $index => $lang)
            <button
              type="button"
              role="tab"
              aria-selected="{{ $loop->first ? 'true' : 'false' }}"
              aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids}}"
              id="tab-{{ $lang->code }}-{{$field_ids}}"
              data-lang="{{ $lang->code }}"
              data-field="{{ $field_ids }}"
              class="py-2 px-4 text-sm font-medium rounded-t-lg
                focus:outline-none
                {{ $loop->first? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
              {{ strtoupper($lang->code) }}
            </button>
          @endforeach
        </nav>
      </div>

      <!-- Tabs 内容区域 -->
      <div>
        @foreach($enabledLanguages as $index => $lang)
          @php
            $langCode = $lang->code;
            $placeholder = $placeholders[$langCode] ?? '';
            $help = $help_texts[$langCode] ?? '';
            $prefix = $prefixes[$langCode] ?? '';
            $suffix = $suffixes[$langCode] ?? '';
            //$isActive = loop->first; // $index === 0;
          @endphp

          <div
            role="tabpanel"
            id="tab-panel-{{ $langCode }}-{{$field_ids}}"
            aria-labelledby="tab-{{ $langCode }}-{{$field_ids}}"
            class="{{ $loop->first ? 'block' : 'hidden' }} pt-4 tabpanel-{{$field_ids}}"
          >
            <div class="flex rounded-md shadow-sm">
              @if($prefix)
                <span
                  class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  {{ $prefix }}
                </span>
              @endif
             
              <input
                type="text"
                name="{{ $field_name }}[{{ $langCode }}]"
                value="{{ old("{$field_name}.{$langCode}", $data[$field_name][$langCode] ?? '') }}"
                placeholder="{{ $placeholder }}"
                class="block w-full px-3 py-2 border border-gray-300
                       {{ $prefix ? 'rounded-none rounded-r-md' : 'rounded-md' }}
                       {{ $suffix ? 'border-r-0' : '' }}
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />

              @if($suffix)
                <span
                  class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  {{ $suffix }}
                </span>
              @endif
            </div>

            @if($help)
              <p class="text-xs text-gray-500 mt-1">{{ $help }}</p>
            @endif
          </div>
        @endforeach
        @if($required || $min || $max)
          <div
            id="req_{{ $field_name }}"
            class="need_require text-sm mt-1 text-red-600"
            data-required="{{ $required ? '1' : '0' }}"
            data-minlength="{{ $min ?? '' }}"
            data-maxlength="{{ $max ?? '' }}"
          ></div>
        @endif

      </div>
    </div>

