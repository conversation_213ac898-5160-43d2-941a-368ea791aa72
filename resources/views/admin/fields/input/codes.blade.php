@php
    $fieldId = $field->id;
    $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Text Field';

    $htmlCode = json_decode($field->html_code_json, true)[app()->getLocale()] ?? '';
    $description = json_decode($field->description_json, true)[app()->getLocale()] ?? '';
    $isEnabled = $field->is_enabled ?? true;
@endphp

@if($isEnabled)
    <div class="mb-6">
        {{-- Label --}}
        @if($label)
        <div class="w-48 flex items-start pt-3">
            <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
                {{ $label }} 
            </label>
        </div>

        @endif

        {{-- Description --}}
        @if($description)
            <p class="text-sm text-gray-500 mb-2">{{ $description }}</p>
        @endif

        {{-- Render HTML Code --}}
        @if($htmlCode)
            <div class="prose max-w-full text-sm text-gray-700">
                {!! $htmlCode !!}
            </div>
        @else
            <p class="text-sm text-red-400 italic">[No code available for current language]</p>
        @endif
    </div>
@endif
