@extends('admin.layouts.admin')

@section('title', isset($fieldCode) ? __('Edit Field Code') : __('Add Field Code'))

@section('breadcrumb')
    <li><a href="{{ localized_route('admin.fields.save') }}">{{ __('Field Codes') }}</a></li>
    <li>{{ isset($fieldCode) ? __('Edit') : __('Add') }}</li>
@endsection

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50 py-8">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Main Form Card -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6">
        <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-white">Field Configuration</h2>
                <div id="kazcmsQuickFillContainer"></div>
            </div>
            <p class="text-indigo-100 mt-1">Configure your field code settings below</p>
        </div>

            @if ($errors->any())
                <div class="mb-4 p-4 bg-red-50 border border-red-300 rounded text-red-700">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->getMessages() as $field => $messages)
                            @foreach ($messages as $message)
                                <li><strong>{{ $field }}</strong>: {{ $message }}</li>
                            @endforeach
                        @endforeach
                    </ul>
                </div>
            @endif
            <form action="{{ localized_route('admin.fields.save') }}" method="POST" class="p-6 space-y-8" >
                @csrf
                <input type="hidden" name="id" value="{{ old('id', $field->id ?? 0) }}">
                <input type="hidden" name="field_type_id" value="{{ $field_type_id }}">
                <input type="hidden" name="content_type_id" value="{{ $content_type_id }}">
                @yield('create_form')
                            <!-- Action Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <div id="kazcmsQuickFillContainer"></div>    
                <button type="button" 
                            onclick="window.history.back()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        {{ isset($field->id) ? 'Update Field' : 'Create Field' }}
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
@endsection

@push('scripts')
@vite('resources/common/kazcms-form-helper.js')
<script>

@php
    $formFields = [
        'name', 'slug', 'description', 'is_virtual'
    ];
@endphp
    window.kazcmsFormFields = @json($formFields);
    window.kazcmsFormLanguages = @json(getActiveLanguages()->pluck('code')->toArray());

    document.addEventListener('DOMContentLoaded', () => {
        if(window.KazCMSFormHelper){
            KazCMSFormHelper.init({
                fields: window.kazcmsFormFields,
                languages: window.kazcmsFormLanguages,
                 buttonContainerId: 'kazcmsQuickFillContainer'
            });
        }
    });
</script>
@endpush