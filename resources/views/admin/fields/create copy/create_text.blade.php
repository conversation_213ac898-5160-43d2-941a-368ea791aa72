@extends('admin.fields.create.create')

@section('title', __('messages.multilang_checker'))
@section('breadcrumb', __('messages.multilang_checker'))

@section('create_form')

    <!-- Basic Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Basic Information
      </h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Multilingual Label -->
        <div class="lg:col-span-2">
          <label class="block text-sm font-semibold text-gray-900 mb-3">Label (multi-language)</label>
          <div class="space-y-3">
            @foreach($enabledLanguages as $lang)
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0 w-20">
                  <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                    {{ $lang->code }}
                  </span>
                </div>
                <div class="flex-1">
                  <input type="text" name="label_json[{{ $lang->code }}]"
                         value="{{ old("label_json.{$lang->code}", ($field->label_json)[$lang->code] ?? '') }}"
                         placeholder="{{ json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code }}"
                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" />
                </div>
              </div>
            @endforeach
          </div>
        </div>

        <!-- Field Type -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-2" for="subtype">
            {{ __('Field Subtype') }}
          </label>
          
          <select name="subtype" id="subtype" required
            class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">

            @foreach($subtypeLabels as $label)
              @php

                
                $nameJson = $label->label_json;
                
                $translatedName = $nameJson[app()->getLocale()] ?? ($nameJson['en-us'] ?? ''); // fallback to en-us
              @endphp
              <option value="{{ $label->subtype_code }}"
                {{ old('subtype', $field?->subtype ?? '') == $label->subtype_code ? 'selected' : '' }}>
                {{ $translatedName }}
              </option>
            @endforeach

          </select>
        </div>

        <!-- Display Color -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-2" for="display_color">Display Color</label>
          <div class="flex items-center space-x-3">
            <input type="color" name="display_color" id="display_color" 
                   value="{{ old('display_color', $field->display_color ?? '#3B82F6') }}" 
                   class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500">
            <span class="text-sm text-gray-600">Choose field accent color</span>
          </div>
        </div>
      </div>
    </div>

    <!-- SEO Configuration -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        SEO Configuration
      </h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Is for SEO -->
        <div class="lg:col-span-2">
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" name="is_seo" value="1" 
                   class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" 
                   {{ old('is_seo', $field->is_seo ?? false) ? 'checked' : '' }}>
            <span class="ml-3 text-sm font-medium text-gray-900">Enable SEO optimization for this field</span>
          </label>
          <p class="text-xs text-gray-500 mt-1 ml-7">This field will be used for search engine optimization</p>
        </div>

        <!-- SEO Type -->
        <div class="lg:col-span-2">
          <label class="block text-sm font-semibold text-gray-900 mb-2" for="seo_type">SEO Type</label>
          <select name="seo_type" id="seo_type" class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="" {{ (old('seo_type', $field->seo_type ?? '') == '') ? 'selected' : '' }}>-- Select SEO Type --</option>
            <option value="title" {{ (old('seo_type', $field->seo_type ?? '') == 'title') ? 'selected' : '' }}>Page Title</option>
            <option value="meta_description" {{ (old('seo_type', $field->seo_type ?? '') == 'meta_description') ? 'selected' : '' }}>Meta Description</option>
            <option value="keywords" {{ (old('seo_type', $field->seo_type ?? '') == 'keywords') ? 'selected' : '' }}>Keywords</option>
            <option value="slug" {{ (old('seo_type', $field->seo_type ?? '') == 'slug') ? 'selected' : '' }}>
                Slug (appears in the page URL, e.g. /blog/your-title)
            </option>
          </select>

        </div>
      </div>
    </div>

    <!-- Field Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
        </svg>
        Field Content
      </h2>

      <div class="space-y-6">
        <!-- Placeholder -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-3">Placeholder Text (multi-language)</label>
          <div class="space-y-3">
            @foreach($enabledLanguages as $lang)
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0 w-20">
                  <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                    {{ $lang->code }}
                  </span>
                </div>
                <div class="flex-1">
                  <input type="text" name="placeholder_json[{{ $lang->code }}]"
                         value="{{ old("placeholder_json.{$lang->code}", $field->placeholder_json[$lang->code] ?? '') }}"
                         placeholder="Enter placeholder for {{ json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code }}"
                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
            @endforeach
          </div>
        </div>

        <!-- Default Value -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-3">Default Value (multi-language)</label>
          <div class="space-y-3">
            @foreach($enabledLanguages as $lang)
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0 w-20">
                  <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                    {{ $lang->code }}
                  </span>
                </div>
                <div class="flex-1">
                  <input type="text" name="default_value_json[{{ $lang->code }}]"
                         value="{{ old("default_value_json.{$lang->code}", $field->default_value_json[$lang->code] ?? '') }}"
                         placeholder="Enter default value"
                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
            @endforeach
          </div>
        </div>

        <!-- Prefix and Suffix -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Prefix Text -->
          <div>
            <label class="block text-sm font-semibold text-gray-900 mb-3">Prefix Text (multi-language)</label>
            <div class="space-y-3">
              @foreach($enabledLanguages as $lang)
                <div class="flex items-center space-x-2">
                  <div class="flex-shrink-0 w-16">
                    <span class="inline-flex items-center justify-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                      {{ $lang->code }}
                    </span>
                  </div>
                  <div class="flex-1">
                    <input type="text" name="prefix_text_json[{{ $lang->code }}]"
                           value="{{ old("prefix_text_json.{$lang->code}", $field->prefix_text_json[$lang->code] ?? '') }}"
                           placeholder="Prefix text"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  </div>
                </div>
              @endforeach
            </div>
          </div>

          <!-- Suffix Text -->
          <div>
            <label class="block text-sm font-semibold text-gray-900 mb-3">Suffix Text (multi-language)</label>
            <div class="space-y-3">
              @foreach($enabledLanguages as $lang)
                <div class="flex items-center space-x-2">
                  <div class="flex-shrink-0 w-16">
                    <span class="inline-flex items-center justify-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                      {{ $lang->code }}
                    </span>
                  </div>
                  <div class="flex-1">
                    <input type="text" name="suffix_text_json[{{ $lang->code }}]"
                           value="{{ old("suffix_text_json.{$lang->code}", $field->suffix_text_json[$lang->code] ?? '') }}"
                           placeholder="Suffix text"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  </div>
                </div>
              @endforeach
            </div>
          </div>
        </div>

        <!-- Help Text -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-3">Help Text (multi-language)</label>
          <div class="space-y-3">
            @foreach($enabledLanguages as $lang)
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-20 pt-2">
                  <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                    {{ $lang->code }}
                  </span>
                </div>
                <div class="flex-1">
                  <textarea name="help_text_json[{{ $lang->code }}]" rows="2"
                            placeholder="Enter helpful instructions for users"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none">{{ old("help_text_json.{$lang->code}", ($field->help_text_json)[$lang->code] ?? '') }}</textarea>
                </div>
              </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>

    <!-- Validation Rules -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Validation Rules
      </h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Required -->
        <div class="lg:col-span-3">
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" name="is_required" value="1" 
                   class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 focus:ring-2" 
                   {{ old('is_required', $field->is_required ?? false) ? 'checked' : '' }}>
            <span class="ml-3 text-sm font-medium text-gray-900">This field is required</span>
          </label>
          <p class="text-xs text-gray-500 mt-1 ml-7">Users must fill this field before submitting</p>
        </div>

        <!-- Min Length -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-2" for="min_length">Minimum Length</label>
          <div class="relative">
            <input type="number" name="min_length" id="min_length" 
                   value="{{ old('min_length', $field->min_length ?? '') }}" 
                   min="0" placeholder="0"
                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span class="text-gray-500 text-sm">chars</span>
            </div>
          </div>
        </div>

        <!-- Max Length -->
        <div>
          <label class="block text-sm font-semibold text-gray-900 mb-2" for="max_length">Maximum Length</label>
          <div class="relative">
            <input type="number" name="max_length" id="max_length" 
                   value="{{ old('max_length', $field->max_length ?? '') }}" 
                   min="0" placeholder="Unlimited"
                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span class="text-gray-500 text-sm">chars</span>
            </div>
          </div>
        </div>
      </div>
    </div>



  @endsection
  @push('scripts')
    <script>
    // 简单的表单交互增强
    document.addEventListener('DOMContentLoaded', function() {
      // SEO checkbox interaction
      const seoCheckbox = document.querySelector('input[name="is_seo"]');
      const seoSelect = document.querySelector('select[name="seo_type"]');
      
      function toggleSeoSelect() {
        if (seoCheckbox.checked) {
          seoSelect.removeAttribute('disabled');
          seoSelect.classList.remove('bg-gray-100', 'cursor-not-allowed');
        } else {
          seoSelect.setAttribute('disabled', 'disabled');
          seoSelect.classList.add('bg-gray-100', 'cursor-not-allowed');
          seoSelect.value = '';
        }
      }
      
      seoCheckbox.addEventListener('change', toggleSeoSelect);
      toggleSeoSelect(); // 初始化状态
    });
    </script>
    @endpush
