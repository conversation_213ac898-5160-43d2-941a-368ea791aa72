@extends('admin.fields.create.create')

@section('title', isset($field) ? __('Edit Field Option') : __('Add Field Option'))

@section('breadcrumb')
    <li><a href="{{ localized_route('admin.fields.save') }}">{{ __('Field Options') }}</a></li>
    <li>{{ isset($field) ? __('Edit') : __('Add') }}</li>
@endsection

@section('create_form')

                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure the field option name and display settings</p>
                    </div>

                    <!-- Field Name Multilingual -->
                    <div class="space-y-4">
                        <label class="block text-sm font-semibold text-gray-900">Field Name (Multilingual)</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($enabledLanguages as $lang)
                                @php
                                    $code = $lang->code;
                                    $label = json_decode($lang->name, true)[app()->getLocale()] ?? $code;
                                    $value = old("label_json.{$code}", $field?->label_json[$code] ?? '');
                                @endphp
                                <div class="space-y-2">
                                    <label for="field_name_{{ $code }}" class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                        {{ $label }} ({{ $code }})
                                    </label>
                                    <input type="text" name="label_json[{{ $code }}]" id="label_json_{{ $code }}"
                                           value="{{ $value }}"
                                           placeholder="Enter field name for {{ $label }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Display Style -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-900">Display Style</label>
                        <select name="display_style" 
                                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                            @foreach ($displayStyles as $style)
                                <option value="{{ $style->key }}" {{ old('display_style', $field?->display_style ?? '') == $style->key ? 'selected' : '' }}>
                                    {{ $style->name }} - {{ $style->description }}
                                </option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500">Choose how this field option will appear to users</p>
                    </div>

                     <!-- Max Select Count -->
                    <div class="space-y-2" id="max-select-count-container">
                        <label class="block text-sm font-medium text-gray-900">Max Select Count</label>
                        <input type="number" name="max_select_count" min="0"
                                value="{{ old('max_select_count', $field->max_select_count ?? '0') }}"
                                placeholder="Unlimited"
                                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                        <p class="text-xs text-gray-500">Maximum number of options that can be selected (0 for unlimited)</p>
                    </div>
                </div>
                @php
                    $defaultOptions = collect(json_decode($field?->options, true) ?? [])
                        ->sortBy('sort_order')
                        ->values()
                        ->all();
                @endphp
                <!-- Default Options -->
                <div id="option-list" class="space-y-4">
                <label class="block text-sm font-semibold text-gray-900">Options</label>

                    @foreach($defaultOptions as $index => $option)
                    <div class="option-item bg-gray-50 p-4 rounded-lg border border-gray-200 space-y-3 relative">
                        <button type="button" class="remove-option absolute top-2 right-2 text-sm text-red-600 hover:underline">×</button>

                        <!-- Sort Order -->
                        <div>
                            <label class="block text-xs font-semibold text-gray-600">Sort Order</label>
                            <input type="number" min="0" name="options[{{ $index }}][sort_order]" 
                            
                                value="{{ $option['sort_order'] ?? $loop->index }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded">
                        </div>

                        <!-- Multilingual Labels -->
                        @foreach($enabledLanguages as $lang)
                        <div>
                            <label class="block text-xs font-semibold text-gray-600">
                                Label ({{ json_decode($lang->name, true)[app()->getLocale()] }})
                            </label>
                            <input type="text" name="options[{{ $index }}][label][{{ $lang->code }}]" 
                                value="{{ $option['label'][$lang->code] ?? '' }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded">
                        </div>
                        @endforeach

                        <!-- Value -->
                        <div>
                            <label class="block text-xs font-semibold text-gray-600">Value</label>
                            <input type="text" name="options[{{ $index }}][value]" 
                                value="{{ $option['value'] ?? '' }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded">
                        </div>

                        <!-- Enabled -->
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" name="options[{{ $index }}][enabled]" value="1" 
                                {{ ($option['enabled'] ?? false) ? 'checked' : '' }}
                                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <label class="text-sm text-gray-700 select-none">Enabled</label>
                        </div>
                    </div>
                    @endforeach
                </div>
                <button type="button" id="add-option" class="mt-2 text-indigo-600 hover:underline">+ Add Option</button>






                <!-- Configuration Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-medium text-gray-900">Field Configuration</h3>
                        <p class="mt-1 text-sm text-gray-600">Set up validation rules and selection options</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Left Column - Selection Settings -->
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <h4 class="text-sm font-medium text-gray-900">Selection Settings</h4>
                                
                              

                                <!-- Is Required -->
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="is_required" value="1"
                                               {{ old('is_required', $field->is_required ?? false) ? 'checked' : '' }}
                                               class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-2 focus:ring-indigo-500 transition-colors">
                                    </div>
                                    <div class="ml-3">
                                        <label class="text-sm font-medium text-gray-900">Required Field</label>
                                        <p class="text-xs text-gray-500">Make this field mandatory for form submission</p>
                                    </div>
                                </div>
                            </div>

                           
                        </div>

                    </div>
                </div>

                <!-- Configuration Section -->
                <div class="space-y-6">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">Field Display Customization</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        Configure prefix and suffix text for the option field. These texts will be displayed before or after the field on the frontend, and support multiple languages.
                    </p>
                </div>


                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Left Column - Selection Settings -->
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                
                                <!-- Prefix Text -->
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-900">Prefix Text</label>
                                    @foreach($enabledLanguages as $lang)
                                        @php
                                            $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                                        @endphp
                                        <div class="mb-2">
                                            <label class="block text-xs font-semibold text-gray-700" for="prefix_text_json_{{ $lang->code }}">
                                            ({{ $langName }})
                                            </label>
                                            <input type="text" 
                                                name="prefix_text_json[{{ $lang->code }}]"
                                                id="prefix_text_json_{{ $lang->code }}"
                                                value="{{ old('prefix_text_json.'.$lang->code, $field->prefix_text_json[$lang->code] ?? '') }}"
                                                placeholder="Text to display before the field in {{ $langName }}"
                                                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        </div>
                                    @endforeach
                                    <p class="text-xs text-gray-500">Optional text to display before the field per language</p>
                                </div>



                            </div>
                        </div>

                         <!-- Right Column - Text Settings -->
                         <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                

                                <!-- Suffix Text 多语言 -->
                                <div class="space-y-2 mt-4">
                                    <label class="block text-sm font-medium text-gray-900">Suffix Text</label>
                                    @foreach($enabledLanguages as $lang)
                                        @php
                                            $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                                        @endphp
                                        <div class="mb-2">
                                            <label class="block text-xs font-semibold text-gray-700" for="suffix_text_json_{{ $lang->code }}">
                                            ({{$langName }})

                                            </label>
                                            <input type="text" 
                                                name="suffix_text_json[{{ $lang->code }}]"
                                                id="suffix_text_json_{{ $lang->code }}"
                                                value="{{ old('suffix_text_json.'.$lang->code, $field->suffix_text_json[$lang->code] ?? '') }}"
                                                placeholder="Text to display after the field in {{ $langName }}"
                                                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        </div>
                                    @endforeach
                                    <p class="text-xs text-gray-500">Optional text to display after the field per language</p>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

@endsection

@push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        const displayStyleSelect = document.querySelector('select[name="display_style"]');
        const maxSelectContainer = document.getElementById('max-select-count-container');
        const maxSelectInput = document.querySelector('input[name="max_select_count"]');

        function updateMaxSelectVisibility() {
            const selected = displayStyleSelect.value;

            if (selected === 'select' || selected === 'radio') {
                maxSelectInput.value = 1;
                maxSelectContainer.classList.add('hidden');
            } else {
                maxSelectContainer.classList.remove('hidden');
            }
        }

        // 绑定事件
        displayStyleSelect.addEventListener('change', updateMaxSelectVisibility);

        // 初始执行
        updateMaxSelectVisibility();

        const optionList = document.getElementById('option-list');
        const addOptionBtn = document.getElementById('add-option');
        let optionIndex = {{ count($defaultOptions) }};

        addOptionBtn.addEventListener('click', () => {
            const newItem = document.createElement('div');
            newItem.className = 'option-item bg-gray-50 p-4 rounded-lg border border-gray-200 space-y-3 relative';
            newItem.innerHTML = `
                <button type="button" class="remove-option absolute top-2 right-2 text-sm text-red-600 hover:underline">×</button>

                <div>
                    <label class="block text-xs font-semibold text-gray-600">Sort Order</label>
                    <input type="number" min="0" name="options[${optionIndex}][sort_order]"

                     
                        value="${optionIndex}" 
                        class="w-full px-3 py-2 border border-gray-300 rounded">
                </div>

                ${(() => {
                    let html = '';
                    @foreach($enabledLanguages as $lang)
                    html += `
                    <div>
                        <label class="block text-xs font-semibold text-gray-600">
                            Label ({{ json_decode($lang->name, true)[app()->getLocale()] }})
                        </label>
                        <input type="text" name="options[${optionIndex}][label][{{ $lang->code }}]"
                            class="w-full px-3 py-2 border border-gray-300 rounded">
                    </div>`;
                    @endforeach
                    return html;
                })()}

                <div>
                    <label class="block text-xs font-semibold text-gray-600">Value</label>
                    <input type="text" name="options[${optionIndex}][value]" 
                        class="w-full px-3 py-2 border border-gray-300 rounded">
                </div>

                <div class="flex items-center space-x-2">
                    <input type="checkbox" name="options[${optionIndex}][enabled]" value="1" checked
                        class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                    <label class="text-sm text-gray-700 select-none">Enabled</label>
                </div>
            `;
            optionList.appendChild(newItem);
            optionIndex++;
        });

        // 删除按钮事件委托
        optionList.addEventListener('click', function (e) {
            if (e.target.classList.contains('remove-option')) {
                e.target.closest('.option-item').remove();
            }
        });
    });
    </script>
@endpush


