@extends('admin.fields.create.create')

@section('title', isset($field) ? __('Edit Field Code') : __('Add Field Code'))

@section('breadcrumb')
    <li><a href="{{ localized_route('admin.fields.save') }}">{{ __('Field Codes') }}</a></li>
    <li>{{ isset($field) ? __('Edit') : __('Add') }}</li>
@endsection

@section('create_form')

                <!-- Label Name Section -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3 pb-4 border-b border-gray-200">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Label Names</h3>
                        <span class="text-sm text-gray-500">Multilingual display labels</span>
                    </div>

                   
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach ($enabledLanguages as $lang)
                            @php
                                $langCode = $lang->code;
                                $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                                $fieldVal = old("label_json.{$langCode}", ($field?->label_json)[$langCode] ?? '');
                            @endphp
                            <div class="group">
                                <label class="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Label Name ({{ $langName }})
                                    </span>
                                </label>
                                <input type="text" name="label_json[{{ $langCode }}]" value="{{ $fieldVal }}"
                                       class="w-full px-4 py-3 rounded-xl border border-gray-300 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400"
                                       placeholder="Enter label name for {{ $langName }}">
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- HTML Code Section -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3 pb-4 border-b border-gray-200">
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">HTML Code</h3>
                        <span class="text-sm text-gray-500">Field template code</span>
                    </div>

                  
                    <div class="grid grid-cols-1 gap-6">
                        @foreach ($enabledLanguages as $lang)
                       
                        @php
                            $langCode = $lang->code;
                            $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                            $fieldVal = old("html_code.{$langCode}",($field?->html_code_json)[$langCode] ?? '');
                        @endphp
                            <div class="group">
                                <label class="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ strtoupper($langCode) }}
                                    </span>
                                    <span>HTML Code ({{ $langName }})</span>
                                </label>
                                <div class="relative">
                                    <textarea name="html_code_json[{{ $langCode }}]" rows="4"
                                              class="w-full px-4 py-3 rounded-xl border border-gray-300 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400 font-mono text-sm"
                                              placeholder="Enter HTML code for {{ $langName }} label">{{ $fieldVal }}</textarea>
                                    <div class="absolute top-3 right-3 text-xs text-gray-400 bg-white px-2 py-1 rounded">HTML</div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Description Section -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3 pb-4 border-b border-gray-200">
                        <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Description</h3>
                        <span class="text-sm text-gray-500">Field descriptions</span>
                    </div>

                    @php
                        $description = old('description', $field->description ?? []);
                    @endphp
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach ($enabledLanguages as $lang)
                        @php
                            $langCode = $lang->code;
                            $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                            $fieldVal = old("description_json.{$langCode}", ($field?->description_json)[$langCode] ?? '');
                        @endphp
                            <div class="group">
                                <label class="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        {{ strtoupper($langCode) }}
                                    </span>
                                    <span>Description  for ({{ $langName }}) label</span>
                                </label>
                                <input type="text" name="description_json[{{ $langCode }}]" value="{{ $fieldVal}}"
                                       class="w-full px-4 py-3 rounded-xl border border-gray-300 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400"
                                       placeholder="Enter description for {{ $langName }} label">
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Variables & Settings Section -->
                <div class="space-y-6">
                    

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">


                        <!-- Status Toggle -->
                        <div class="group">
                            <label class="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Status</span>
                            </label>
                            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <label class="flex items-center space-x-3 cursor-pointer">
                                    <input type="checkbox" name="is_enabled" value="1"
                                           class="w-5 h-5 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-2 focus:ring-indigo-500 transition-all duration-200"
                                           {{ old('is_enabled', $field?->is_enabled ?? true) ? 'checked' : '' }}>
                                    <div class="flex flex-col">
                                        <span class="text-sm font-medium text-gray-900">Enabled</span>
                                        <span class="text-xs text-gray-500">Activate this field code</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>



@endsection