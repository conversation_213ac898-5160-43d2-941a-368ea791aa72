@extends('admin.fields.create.create')
@section('title', isset($field) ? __('Edit Textarea Field') : __('Add Textarea Field'))

@section('breadcrumb')
    <li><a href="{{ localized_route('admin.fields.save') }}">{{ __('Textarea Fields') }}</a></li>
    <li>{{ isset($field) ? __('Edit') : __('Add') }}</li>
@endsection

@section('create_form')



                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                        <p class="mt-1 text-sm text-gray-600">Set up the field label and help text</p>
                    </div>

                    <!-- Label (multi-language) -->
                    <div class="space-y-4">
                        <label class="block text-sm font-semibold text-gray-900">Label (multi-language)</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($enabledLanguages as $lang)
                                @php
                                    $labelVal = old("label.{$lang->code}", $fieldFile->label[$lang->code] ?? '');
                                    $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                                @endphp
                                <div class="space-y-2">
                                    <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                        {{ $langName }} ({{ $lang->code }})
                                    </label>
                                    <input type="text" name="label_json[{{ $lang->code }}]" 
                                           value="{{ $labelVal }}" 
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" 
                                           placeholder="Enter label for {{ $langName }}" />
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Help Text (multi-language) -->
                    <div class="space-y-4">
                        <label class="block text-sm font-semibold text-gray-900">Help Text (multi-language)</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($enabledLanguages as $lang)
                                @php
                                    $helpVal = old("help_text_json.{$lang->code}", $fieldFile->help_text_json[$lang->code] ?? '');
                                    $langName = json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code;
                                @endphp
                                <div class="space-y-2">
                                    <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                        {{ $langName }} ({{ $lang->code }})
                                    </label>
                                    <input type="text" name="help_text_json[{{ $lang->code }}]" 
                                           value="{{ $helpVal }}" 
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" 
                                           placeholder="Enter help text for {{ $langName }}" />
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- File Settings Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-medium text-gray-900">File Upload Settings</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure file type restrictions and upload limits</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Allowed File Types -->
                        <div class="space-y-2">
                            <label for="allowed_file_types" class="block text-sm font-medium text-gray-900">
                                Allowed File Types
                            </label>
                            <input type="text" name="allowed_file_types" id="allowed_file_types"
                                   value="{{ old('allowed_file_types', $fieldFile->allowed_file_types ?? '') }}"
                                   placeholder="jpg,png,pdf,doc,docx"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <p class="text-xs text-gray-500">Separate multiple types with commas</p>
                        </div>

                        <!-- Max Upload Count -->
                        <div class="space-y-2">
                            <label for="max_upload_count" class="block text-sm font-medium text-gray-900">
                                Max Upload Count
                            </label>
                            <input type="number" name="max_upload_count" id="max_upload_count"
                                   value="{{ old('max_upload_count', $fieldFile->max_upload_count ?? '') }}"
                                   min="1"
                                   placeholder="5"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <p class="text-xs text-gray-500">Maximum number of files that can be uploaded</p>
                        </div>

                        <!-- Max File Size -->
                        <div class="space-y-2">
                            <label for="max_file_size_mb" class="block text-sm font-medium text-gray-900">
                                Max File Size (MB)
                            </label>
                            <input type="number" name="max_file_size_mb" id="max_file_size_mb"
                                   value="{{ old('max_file_size_mb', $fieldFile->max_file_size_mb ?? '') }}"
                                   min="1"
                                   placeholder="10"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <p class="text-xs text-gray-500">Maximum size per file in megabytes</p>
                        </div>

                        <!-- Rename on Upload -->
                        <div class="space-y-2">
                            <label for="rename_on_upload" class="block text-sm font-medium text-gray-900">
                                Rename on Upload
                            </label>
                            <select name="rename_on_upload" id="rename_on_upload"
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                @foreach(['original', 'uuid', 'timestamp'] as $option)
                                    <option value="{{ $option }}"
                                        {{ old('rename_on_upload', $fieldFile->rename_on_upload ?? 'original') === $option ? 'selected' : '' }}>
                                        {{ ucfirst($option) }}
                                    </option>
                                @endforeach
                            </select>
                            <p class="text-xs text-gray-500">How to rename files during upload</p>
                        </div>
                    </div>
                </div>

                <!-- Display & Behavior Options Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-medium text-gray-900">Display & Behavior Options</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure how the field behaves and displays content</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <!-- Required -->
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input type="checkbox" name="is_required" value="1"
                                           {{ old('is_required', $fieldFile->is_required ?? false) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                                </div>
                                <div class="ml-3">
                                    <label class="text-sm font-medium text-gray-900">Required Field</label>
                                    <p class="text-xs text-gray-500">Make this field mandatory for form submission</p>
                                </div>
                            </div>

                            <!-- Enable Image Preview -->
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input type="checkbox" name="enable_image_preview" value="1"
                                           {{ old('enable_image_preview', $fieldFile->enable_image_preview ?? false) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                                </div>
                                <div class="ml-3">
                                    <label class="text-sm font-medium text-gray-900">Enable Image Preview</label>
                                    <p class="text-xs text-gray-500">Show thumbnails for uploaded images</p>
                                </div>
                            </div>

                            <!-- Auto Compress Images -->
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input type="checkbox" name="auto_compress_images" value="1"
                                           {{ old('auto_compress_images', $fieldFile->auto_compress_images ?? false) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                                </div>
                                <div class="ml-3">
                                    <label class="text-sm font-medium text-gray-900">Auto Compress Images</label>
                                    <p class="text-xs text-gray-500">Automatically compress uploaded images</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Display as Gallery -->
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input type="checkbox" name="display_as_gallery" value="1"
                                           {{ old('display_as_gallery', $fieldFile->display_as_gallery ?? false) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-colors">
                                </div>
                                <div class="ml-3">
                                    <label class="text-sm font-medium text-gray-900">Display as Gallery</label>
                                    <p class="text-xs text-gray-500">Show uploaded files in a gallery layout</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


@endsection