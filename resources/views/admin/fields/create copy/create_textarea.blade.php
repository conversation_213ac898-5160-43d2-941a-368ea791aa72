@extends('admin.fields.create.create')

@section('title', isset($field) ? __('Edit Textarea Field') : __('Add Textarea Field'))

@section('breadcrumb')
<li><a href="{{ localized_route('admin.fields.save') }}">{{ __('Textarea Fields') }}</a></li>
<li>{{ isset($field) ? __('Edit') : __('Add') }}</li>
@endsection

@section('create_form')
  

 
    <!-- Basic Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Basic Information
      </h2>
      
      <!-- Multilingual Label -->
      <div>
        <label class="block text-sm font-semibold text-gray-900 mb-3">{{ __('Label (multi-language)') }}</label>
        <div class="space-y-3">
          @foreach($enabledLanguages as $lang)
            @php
              $val = old("label_json.{$lang->code}", $field?->label_json[$lang->code] ?? '');
            @endphp
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0 w-20 pt-2">
                <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                  {{ json_decode($lang->name, true)[app()->getLocale()] }}
                </span>
              </div>
              <div class="flex-1">
                <textarea name="label_json[{{ $lang->code }}]" rows="3"
                          placeholder="{{ json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code }} label"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none">{{ $val }}</textarea>
              </div>
            </div>
          @endforeach
        </div>
        @error('label_json')
          <p class="text-sm text-red-600 mt-2 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            {{ $message }}
          </p>
        @enderror
      </div>
    </div>

    <!-- Field Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
        </svg>
        Field Content
      </h2>

      <!-- Help Text -->
      <div>
        <label class="block text-sm font-semibold text-gray-900 mb-3">{{ __('Help Text (multi-language)') }}</label>
        <div class="space-y-3">
          @foreach($enabledLanguages as $lang)
            @php
              $val = old("help_text_json.{$lang->code}", $field?->help_text_json[$lang->code] ?? '');
            @endphp
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0 w-20 pt-2">
                <span class="inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 uppercase">
                  {{ json_decode($lang->name, true)[app()->getLocale()]}}
                </span>
              </div>
              <div class="flex-1">
                <textarea name="help_text_json[{{ $lang->code }}]" rows="2"
                          placeholder="Enter helpful instructions for users in {{ json_decode($lang->name, true)[app()->getLocale()] ?? $lang->code }}"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none">{{ $val }}</textarea>
              </div>
            </div>
          @endforeach
        </div>
        @error('help_text_json')
          <p class="text-sm text-red-600 mt-2 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            {{ $message }}
          </p>
        @enderror
      </div>
    </div>

    <!-- Field Settings -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Field Settings
      </h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Required Field -->
        <div class="lg:col-span-2">
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" name="is_required" value="1"
                   class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                   {{ old('is_required', $field->is_required ?? false) ? 'checked' : '' }}>
            <span class="ml-3 text-sm font-medium text-gray-900">{{ __('This field is required') }}</span>
          </label>
          <p class="text-xs text-gray-500 mt-1 ml-7">Users must fill this field before submitting the form</p>
        </div>

        <!-- Textarea Settings (可以根据需要添加更多设置) -->
        <div class="lg:col-span-2">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-blue-900">Textarea Field Info</h3>
                <p class="text-sm text-blue-700 mt-1">
                  This field type allows users to input multi-line text with automatic resizing capabilities. 
                  Perfect for descriptions, comments, or any longer text content.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  @endsection
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 自动调整textarea高度
  const textareas = document.querySelectorAll('textarea');
  textareas.forEach(textarea => {
    textarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = this.scrollHeight + 'px';
    });
  });

  // 表单验证提示
  const form = document.querySelector('form');
  form.addEventListener('submit', function(e) {
    const requiredFields = document.querySelectorAll('textarea[name*="label"]');
    let hasEmptyLabel = false;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        hasEmptyLabel = true;
      }
    });
    
    if (hasEmptyLabel) {
      e.preventDefault();
      alert('Please fill in at least one label field before submitting.');
      return false;
    }
  });
});
</script>
@endpush
