@extends('admin.layouts.admin')

@section('title', __('Submit Data'))

@section('content')
@php
//getActiveLanguages();
$enabledLanguages = getActiveLanguages();
@endphp 
<form action="{{ localized_route('admin.fields.data.save', ['content_type_id' => $content_type_id, 'data_id' => $data_id]) }}" method="POST" enctype="multipart/form-data" class="kaz-upload-form">
    @csrf

    @foreach($fields as $field)
        @php
          $viewName = 'admin.fields.input.' . $fieldTypes[$field->field_type_id]['related_table']; // e.g., text, textarea, file, etc.
        @endphp
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex">
                @if(view()->exists($viewName))
                    @include($viewName, [
                        'field' => $field,
                    'enabledLanguages'=>$enabledLanguages,
                    'data_id'=>$data_id,
                    'data'=>$data,

                    ])
                @else
                    <p>{{ __('Unknown field type:') }} {{ $field->field_type_id }}</p>
                @endif
            </div>
        </div>
    @endforeach

    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
</form>
<script src="{{ asset('static/js/kaz-image-uploader.js') }}?v=1.0.2"></script>
@endsection

@push('scripts')
<script>
 document.addEventListener('DOMContentLoaded', () => {
  // 给所有语言tab按钮绑定事件
  const allTabs = document.querySelectorAll('button[role="tab"][data-lang][id^="tab-"]');

  allTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const lang = tab.dataset.lang;

      // 从 tab id 里解析 field_ids，格式： tab-{langCode}-{field_ids}
      // 例如： tab-en-1_10 的 field_ids 是 1_10
      const field_ids = tab.dataset.field; 

      if (!field_ids) return;

      // 1. 找出该字段的所有tabpanel，class 形如 tabpanel-{field_ids}
      const panels = document.querySelectorAll(`.tabpanel-${field_ids}`);

      // 2. 隐藏所有面板
      panels.forEach(panel => {
        panel.classList.add('hidden');
        panel.classList.remove('block');
      });

      // 3. 显示当前选中面板 id = tab-panel-{lang}-{field_ids}
      const activePanel = document.getElementById(`tab-panel-${lang}-${field_ids}`);
      if (activePanel) {
        activePanel.classList.remove('hidden');
        activePanel.classList.add('block');
      }

      // 4. 找出当前字段的所有 tab 按钮，id 以 tab-开头，且包含相同 field_ids
      // 以避免影响其他字段
      const fieldTabs = document.querySelectorAll(`button[id$="-${field_ids}"][role="tab"]`);

      // 5. 重置所有 tab 按钮样式和 aria-selected
      fieldTabs.forEach(t => {
        t.setAttribute('aria-selected', 'false');
        t.classList.remove('border-b-2', 'border-blue-600', 'text-blue-600');
        t.classList.add('text-gray-500', 'hover:text-gray-700');
      });

      // 6. 当前 tab 按钮样式设置为选中
      tab.setAttribute('aria-selected', 'true');
      tab.classList.add('border-b-2', 'border-blue-600', 'text-blue-600');
      tab.classList.remove('text-gray-500', 'hover:text-gray-700');
    });
  });
});




</script>




@endpush
@push('scripts')
<script>
    var currentCategoryButtonId = null;
    document.addEventListener('DOMContentLoaded', function () {
    // 给所有 .category-select-button 添加事件
    document.querySelectorAll('.category-select-button').forEach(button => {
        button.addEventListener('click', function () {
            openCategoryModal(this);
        });
    });
    document.getElementById('confirmCategoryBtn').addEventListener('click', confirmCategoryModal);
});

function openCategoryModal(button) {
    const categoryId = button.dataset.categoryid;
    const displayStyle = button.dataset.displaystyle;
    currentCategoryButtonId = button.id;

    // 打开 modal
    document.getElementById('categoryModal').classList.remove('hidden');
    const container = document.getElementById('categoryTree');
    container.innerHTML = ''; // 清空
    console.log('button clicked');

    loadCategoryTree(categoryId, container, displayStyle);
}
function closeCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

// 加载分类树
function loadCategoryTree(categoryId, container, displayStyle) {
    fetch('/{{app()->getLocale()}}/admin/categories/children/' + categoryId)
        .then(res => res.json())
        .then(categories => {
            categories.forEach(cat => {
                const wrapper = document.createElement('div');
                wrapper.classList.add('ml-4', 'py-2', 'border-l', 'border-gray-200', 'pl-4', 'hover:bg-gray-50', 'transition-colors', 'duration-200', 'rounded-r-md');

                // 创建flex容器来排列input/label和展开按钮
                const inputContainer = document.createElement('div');
                inputContainer.classList.add('flex', 'items-center', 'justify-between', 'w-full');

                const leftContent = document.createElement('div');
                leftContent.classList.add('flex', 'items-center', 'flex-1');

                // 只有final_category == 0时，显示选择框
                if (cat.final_category == 0) {
                    const input = document.createElement('input');
                    input.type = displayStyle === 'checkbox' ? 'checkbox' : 'radio';
                    input.name = 'category_selection';
                    input.value = cat.id;
                    input.id = `category_${cat.id}`;
                    input.classList.add('w-4', 'h-4', 'text-blue-600', 'bg-gray-100', 'border-gray-300', 'rounded', 'focus:ring-blue-500', 'focus:ring-2');

                    const label = document.createElement('label');
                    label.setAttribute('for', `category_${cat.id}`);
                    label.classList.add('ml-3', 'text-gray-700', 'font-medium', 'cursor-pointer', 'hover:text-blue-700', 'transition-colors', 'duration-200', 'flex-1', 'select-none');
                    label.textContent = cat.name['{{app()->getLocale()}}'];

                    leftContent.appendChild(input);
                    leftContent.appendChild(label);
                } else {
                    // final_category != 0， 不显示选择框，只显示分类名（无input）
                    const span = document.createElement('span');
                    span.classList.add('ml-7', 'text-gray-700', 'font-medium', 'select-none'); // ml-7 留空位对齐checkbox的位置
                    span.textContent = cat.name['{{app()->getLocale()}}'];
                    leftContent.appendChild(span);
                }

                inputContainer.appendChild(leftContent);

                // 判断是否还能继续展开
                if (cat.final_category == 1) {
                    const moreBtn = document.createElement('button');
                    moreBtn.type = 'button';
                    moreBtn.textContent = '▶';
                    moreBtn.classList.add(
                        'ml-2', 'px-2', 'py-1', 'text-blue-600', 'hover:text-blue-800', 'hover:bg-blue-100',
                        'rounded', 'transition-all', 'duration-200', 'font-bold', 'text-sm', 'focus:outline-none',
                        'focus:ring-2', 'focus:ring-blue-500', 'transform', 'hover:scale-110'
                    );
                    moreBtn.onclick = (e) => {
                        e.preventDefault();
                        if (!wrapper.querySelector('.children')) {
                            const childContainer = document.createElement('div');
                            childContainer.classList.add('children', 'ml-4', 'mt-2', 'border-l-2', 'border-blue-200', 'pl-3');
                            wrapper.appendChild(childContainer);
                            loadCategoryTree(cat.id, childContainer, displayStyle);

                            moreBtn.style.transform = 'rotate(90deg)';
                            moreBtn.classList.add('text-blue-800');
                        } else {
                            const existingChildren = wrapper.querySelector('.children');
                            if (existingChildren) {
                                existingChildren.remove();
                                moreBtn.style.transform = 'rotate(0deg)';
                                moreBtn.classList.remove('text-blue-800');
                            }
                        }
                    };
                    inputContainer.appendChild(moreBtn);
                }

                wrapper.appendChild(inputContainer);
                container.appendChild(wrapper);
            });
        });
}

function confirmCategoryModal() {
    console.log(currentCategoryButtonId);
    const currentCategoryButton = document.getElementById(currentCategoryButtonId);
    if (!currentCategoryButton) return;
    
    const displayStyle = currentCategoryButton.dataset.displaystyle;
    console.log(displayStyle);
    
    const inputs = document.querySelectorAll('#categoryTree input:checked');
    const fieldTypeId = currentCategoryButtonId.split('_')[1];
    const fieldId = currentCategoryButtonId.split('_')[2];
    const wrapper = document.getElementById(`selectedCategories_${fieldTypeId}_${fieldId}`);
    
    wrapper.innerHTML = '';
     // 收集id和名字
     const selectedIds = [];
    if (inputs.length > 0) {
        inputs.forEach((input, index) => {
          selectedIds.push(input.value); // 收集数字ID
            const label = document.querySelector(`label[for="${input.id}"]`);
            
            const span = document.createElement('span');
            span.classList.add(
                'inline-flex', 'items-center', 'px-3', 'py-1', 'mr-2', 'mb-1',
                'text-sm', 'font-medium', 'text-blue-800', 'bg-blue-100', 
                'rounded-full', 'border', 'border-blue-200',
                'hover:bg-blue-200', 'transition-colors', 'duration-200'
            );
            
            console.log(label.textContent);
            span.textContent = label.textContent;
            
            // 添加删除按钮
            const removeBtn = document.createElement('button');
            removeBtn.classList.add(
                'ml-2', 'inline-flex', 'items-center', 'justify-center',
                'w-4', 'h-4', 'text-blue-600', 'hover:text-red-600',
                'hover:bg-red-100', 'rounded-full', 'transition-all', 'duration-200',
                'focus:outline-none', 'focus:ring-1', 'focus:ring-red-500'
            );
            removeBtn.innerHTML = '×';
            removeBtn.setAttribute('data-inputid', `field_${fieldTypeId}_${fieldId}`);
            removeBtn.setAttribute('data-categoryid', input.value);
            removeBtn.onclick = (e) => {
                e.preventDefault();
                // 取消对应的checkbox/radio选中状态
                input.checked = false;

                // ===== 获取 inputId 和 categoryId，更新隐藏 input 的值 =====
                const inputId = removeBtn.getAttribute('data-inputid'); // input 是隐藏字段
                const categoryIdToRemove = removeBtn.getAttribute('data-categoryid');
                const hiddenInput = document.getElementById(inputId);
                if (hiddenInput) {
                    const values = hiddenInput.value.split(',').map(v => v.trim());
                    const newValues = values.filter(v => v !== categoryIdToRemove);
                    hiddenInput.value = newValues.join(',');
                }

                // 移除当前标签
                span.remove();
                // 如果没有选中项了，隐藏wrapper
                if (wrapper.children.length === 0) {
                    wrapper.classList.add('hidden');
                }
            };
            
            span.appendChild(removeBtn);
            wrapper.appendChild(span);
            
            // 如果不是最后一个且是多选，添加逗号分隔
            if (displayStyle === 'checkbox' && index < inputs.length - 1) {
                const comma = document.createElement('span');
                comma.classList.add('text-gray-400', 'text-sm', 'mr-1');
                comma.textContent = ',';
                wrapper.appendChild(comma);
            }
        });
        const hiddenInput = document.getElementById(`field_${fieldTypeId}_${fieldId}`);
      if(hiddenInput) {
      console.log(selectedIds.join(','));
          hiddenInput.value = selectedIds.join(',');
      }

        wrapper.classList.remove('hidden');
    } else {
        wrapper.classList.add('hidden');
    }
    
    closeCategoryModal();
}
</script>
<script>
const fileInputs = document.querySelectorAll('.kaz-file-input');
const fileStateMap = {}; // 存储每个 input 的文件信息（key: input.id）

fileInputs.forEach(input => {
    const previewContainerId = input.dataset.preview;
    const previewContainer = document.getElementById(previewContainerId);
    if (!previewContainer) return;

    const inputId = input.id;
    fileStateMap[inputId] = [];

    // 新增：读取隐藏 input 里的已有图片URL
    const hiddenInput = document.getElementById('existingImages_' + inputId);
    if (hiddenInput && hiddenInput.value.trim() !== '') {
        const existingUrls = hiddenInput.value.split(',');
        existingUrls.forEach((url, idx) => {
            fileStateMap[inputId].push({
                id: 'existing-' + idx, // 伪id，保证唯一
                file: null,            // 旧图片没有 file 对象
                previewUrl: url,
                isExisting: true       // 标记是已有图片
            });
        });
    }

    // 绑定文件选择事件
    input.addEventListener('change', (e) => {
        const files = Array.from(e.target.files);

        files.forEach(file => {
            const id = crypto.randomUUID();
            const previewUrl = file.type.startsWith('image/') ? URL.createObjectURL(file) : '';
            fileStateMap[inputId].push({ file, previewUrl, id, isExisting: false });
        });

        renderFilePreview(inputId, previewContainer);

        // 清空 input，方便多次上传同一个文件
        input.value = '';
    });

    // 初始化时先渲染已有图片
    renderFilePreview(inputId, previewContainer);
});


function renderFilePreview(inputId, container) {
   const fileList = fileStateMap[inputId];
   container.innerHTML = '';
   
   fileList.forEach((item, index) => {
       const div = document.createElement('div');
       div.className = 'relative w-28 h-28 border-2 border-gray-200 rounded-lg bg-white flex items-center justify-center overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-200 cursor-move group';
       div.setAttribute('draggable', true);
       div.dataset.id = item.id;
       div.dataset.inputid = inputId;
       
       if (item.previewUrl) {
           const img = document.createElement('img');
           img.src = item.previewUrl;
           img.className = 'object-contain max-w-full max-h-full';

           div.appendChild(img);
       } else {
           const span = document.createElement('span');
           span.textContent = item.file.name;
           span.className = 'text-xs text-gray-600 p-2 text-center font-medium truncate';
           div.appendChild(span);
       }
       
       // 删除按钮
       const deleteBtn = document.createElement('button');
       deleteBtn.innerHTML = '×';
       deleteBtn.className = 'absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full text-sm font-bold hover:bg-red-600 transition-colors duration-200 opacity-0 group-hover:opacity-100 flex items-center justify-center shadow-md';
       deleteBtn.onclick = (e) => {
            e.stopPropagation();
            fileStateMap[inputId] = fileStateMap[inputId].filter(file => file.id !== item.id);
            renderFilePreview(inputId, container);

            // 同步更新隐藏 input
            const hiddenInput = document.getElementById('existingImages_' + inputId);
            if (hiddenInput) {
                hiddenInput.value = fileStateMap[inputId]
                    .filter(f => f.isExisting)
                    .map(f => f.previewUrl)
                    .join(',');
            }
        };

       div.appendChild(deleteBtn);
       
       // 拖拽事件绑定
       div.addEventListener('dragstart', handleDragStart);
       div.addEventListener('dragover', handleDragOver);
       div.addEventListener('dragleave', handleDragLeave);
       div.addEventListener('drop', handleDrop);
       div.addEventListener('dragend', handleDragEnd);
       
       container.appendChild(div);
   });
}

let dragSrcEl = null;

function handleDragStart(e) {
    dragSrcEl = this;
    this.classList.add('opacity-50');
}

function handleDragOver(e) {
    e.preventDefault();
    this.classList.add('border-blue-500');
    this.classList.remove('border-gray-200'); // ✅ 正确匹配原来的颜色类
}
function handleDragLeave(e) {
    this.classList.remove('border-blue-500');
    this.classList.add('border-gray-200'); // ✅ 恢复原样
}

function handleDrop(e) {
    e.stopPropagation();
    this.classList.remove('border-blue-500');
    this.classList.add('border-gray-200'); // ✅ 恢复原样

    if (dragSrcEl === this) return;

    const inputId = this.dataset.inputid;
    const draggedId = dragSrcEl.dataset.id;
    const targetId = this.dataset.id;

    const fileList = fileStateMap[inputId];
    const draggedIndex = fileList.findIndex(f => f.id === draggedId);
    const targetIndex = fileList.findIndex(f => f.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    // 从原位置移除 dragged 项
    const [draggedItem] = fileList.splice(draggedIndex, 1);

    // 插入到目标位置前（注意目标位置可能会变化）
    fileList.splice(targetIndex, 0, draggedItem);

    const container = this.parentElement;
    renderFilePreview(inputId, container);
    // 同步更新隐藏 input
    const hiddenInput = document.getElementById('existingImages_' + inputId);
    if (hiddenInput) {
        hiddenInput.value = fileStateMap[inputId]
            .filter(f => f.isExisting)
            .map(f => f.previewUrl)
            .join(',');
    }
}


function handleDragEnd() {
    this.classList.remove('opacity-50');
}
</script>

<!--options -->
<script>
    document.addEventListener('DOMContentLoaded', () => {
  // 找到所有带有 data-max-select 的选项组
  const optionGroups = document.querySelectorAll('.option-group');

  optionGroups.forEach(group => {
    const maxSelect = parseInt(group.dataset.maxSelect, 10);
    const isMultiple = group.dataset.multiple === 'true';

    if (!isMultiple || maxSelect <= 0) {
      // 单选 或 不限数量，跳过限制处理
      return;
    }

    const inputs = group.querySelectorAll('input.option-input[type="checkbox"]');

    function updateInputs() {
      const checkedCount = [...inputs].filter(i => i.checked).length;

      inputs.forEach(input => {
        // 如果已选数达到最大，且当前input没被选中，就禁用它
        if (checkedCount >= maxSelect && !input.checked) {
          input.disabled = true;
        } else {
          input.disabled = false;
        }
      });
    }

    // 初始化状态
    updateInputs();

    // 监听每个checkbox变化
    inputs.forEach(input => {
      input.addEventListener('change', updateInputs);
    });
  });
});


// 初始化调用

</script>
@endpush
@push('scripts')
    @vite(['resources/js/kaz-image-uploader.js'])
@endpush

