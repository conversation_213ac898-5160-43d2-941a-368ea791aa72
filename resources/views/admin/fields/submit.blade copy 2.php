@extends('admin.layouts.admin')

@section('title', __('Submit Data'))

@section('content')
@php
//getActiveLanguages();
$enabledLanguages = getActiveLanguages();
@endphp 
<form action="{{ localized_route('admin.fields.data.save', ['content_type_id' => $content_type_id, 'data_id' => $data_id]) }}" method="POST" enctype="multipart/form-data" class="kaz-upload-form">
    @csrf
@php
 $show_category_modal = false;
@endphp 
    @foreach($fields as $field)
        @php
          $viewName = 'admin.fields.input.' . $fieldTypes[$field->field_type_id]['related_table']; // e.g., text, textarea, file, etc.
          if($field->field_type_id == 4) {
            $show_category_modal = true;
          }
          @endphp
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex">
                @if(view()->exists($viewName))
                    @include($viewName, [
                        'field' => $field,
                    'enabledLanguages'=>$enabledLanguages,
                    'data_id'=>$data_id,
                    'data'=>$data,

                    ])
                @else
                    <p>{{ __('Unknown field type:') }} {{ $field->field_type_id }}</p>
                @endif
            </div>
        </div>
    @endforeach

    <div class="flex gap-4 mt-6">
  <!-- 保存草稿 -->
  <button
    id="save-draft-btn"
    type="button"
    class="inline-flex items-center justify-center px-6 py-2
           rounded-lg font-medium text-sm transition
           bg-gray-200 text-gray-700 hover:bg-gray-300
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400
           shadow-sm hover:shadow-md active:shadow-inner"
  >
    💾 保存草稿
  </button>

  <!-- 发布 -->
  <button
    id="publish-btn"
    type="submit"
    class="inline-flex items-center justify-center px-6 py-2
           rounded-lg font-medium text-sm transition
           bg-blue-600 text-white hover:bg-blue-700
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
           shadow-sm hover:shadow-md active:shadow-inner"
  >
    🚀 发布
  </button>
</div>

</form>

@if($show_category_modal)
    <!-- Modal -->
    <div id="categoryModal" class="hidden fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white w-full max-w-lg p-4 rounded shadow relative">
            <h2 class="text-xl font-bold mb-4">选择分类</h2>
            <div id="categoryTree" class="space-y-2 max-h-[400px] overflow-auto"></div>
            <div class="mt-4 text-right">
                <button type="button"  id="closeCategoryModal" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">关闭</button>
                <button type="button"  id="confirmCategoryBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">确定</button>
            </div>
        </div>
    </div>
@endif
@endsection

@push('css')
<link rel="stylesheet" href="{{ asset('static/css/kaz-image-craft.css') }}?v=1.0.2">
@endpush

@push('scripts')
<script src="{{ asset('static/js/kaz-image-craft.js') }}?v=1.0.2"></script>
<script src="{{ asset('static/js/kaz-image-craft-lang-' . app()->getLocale() . '.js') }}?v=1.0.2"></script>

@vite('resources/admin/js/content-form.js')
<script>
/* ---------- initialise KazImageCraft ---------- */
KazImageCraft._init('kaz-file-input', 'kaz-upload-form');

// 绑定“发布”按钮
document.getElementById('publish-btn').addEventListener('click', e => {
  let hasError = false;

  document.querySelectorAll('.need_require').forEach(div => {
    let fieldName = div.id.replace(/^req_/, ''); // 如 fields_1_19

    // 取校验规则
    const required = div.dataset.required === '1';
    const minLen = div.dataset.minlength ? parseInt(div.dataset.minlength, 10) : null;
    const maxLen = div.dataset.maxlength ? parseInt(div.dataset.maxlength, 10) : null;

    const fieldInputs = document.querySelectorAll(
      `input[name^="${fieldName}["], textarea[name^="${fieldName}["], select[name^="${fieldName}["]`
    );

    // 清除之前的错误样式
    fieldInputs.forEach(el => el.classList.remove('border-red-500'));
    div.innerHTML = '';

    // 先判必填
    const isAnyFilled = Array.from(fieldInputs).some(input => input.value.trim() !== '');

    if (required && !isAnyFilled) {
      hasError = true;
      div.innerHTML = `
        <div class="bg-yellow-50 border border-yellow-300 rounded-md p-2 mt-2">
          <span class="inline-flex items-center text-sm font-medium text-yellow-800">
            ⚠️ 此字段至少填写一种语言
          </span>
        </div>
      `;
      fieldInputs[0]?.classList.add('border-red-500');
      return; // 本字段出错跳过后续长度校验
    }

    // 如果至少填写了一个，检查长度（针对所有语言都要检查）
    for (const input of fieldInputs) {
      const val = input.value.trim();
      if (val !== '') {
        if (minLen !== null && val.length < minLen) {
          hasError = true;
          div.innerHTML = `
            <div class="bg-yellow-50 border border-yellow-300 rounded-md p-2 mt-2">
              <span class="inline-flex items-center text-sm font-medium text-yellow-800">
                ⚠️ 输入长度不能小于 ${minLen} 个字符
              </span>
            </div>
          `;
          input.classList.add('border-red-500');
          break; // 长度不符，跳过后续输入校验
        }
        if (maxLen !== null && val.length > maxLen) {
          hasError = true;
          div.innerHTML = `
            <div class="bg-yellow-50 border border-yellow-300 rounded-md p-2 mt-2">
              <span class="inline-flex items-center text-sm font-medium text-yellow-800">
                ⚠️ 输入长度不能超过 ${maxLen} 个字符
              </span>
            </div>
          `;
          input.classList.add('border-red-500');
          break; // 长度超限，跳过后续输入校验
        }
      }
    }

    // 无错误时清空提示
    if (!hasError) {
      div.innerHTML = '';
    }
  });

  if (hasError) {
    e.preventDefault();
    document.querySelector('.border-red-500')?.scrollIntoView({behavior: 'smooth'});
  }
});


</script>
@endpush


