@extends('layouts.app')

@section('content')
<div class="max-w-5xl mx-auto p-6 mt-10 bg-white rounded-xl shadow-md">
    <h2 class="text-3xl font-semibold mb-8">Edit Site Settings</h2>

    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 text-green-800 rounded">
            {{ session('success') }}
        </div>
    @endif

    <form method="POST" action="{{ route('site-settings.save') }}">
        @csrf

        {{-- Site Name --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Site Name (English)</label>
            <input type="text" name="site_name[en]" value="{{ old('site_name.en', $siteSetting->site_name['en'] ?? '') }}" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('site_name.en') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">站点名称 (中文)</label>
            <input type="text" name="site_name[zh]" value="{{ old('site_name.zh', $siteSetting->site_name['zh'] ?? '') }}" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('site_name.zh') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Site URL --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Site URL</label>
            <input type="url" name="site_url" value="{{ old('site_url', $siteSetting->site_url ?? '') }}"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('site_url') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Meta Title --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Meta Title (English)</label>
            <input type="text" name="meta_title[en]" value="{{ old('meta_title.en', $siteSetting->meta_title['en'] ?? '') }}" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('meta_title.en') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Meta 标题 (中文)</label>
            <input type="text" name="meta_title[zh]" value="{{ old('meta_title.zh', $siteSetting->meta_title['zh'] ?? '') }}" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('meta_title.zh') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Meta Keywords --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Meta Keywords (JSON Array)</label>
            <input type="text" name="meta_keywords" value="{{ old('meta_keywords', json_encode($siteSetting->meta_keywords ?? [])) }}"
                placeholder='["keyword1", "keyword2", "keyword3"]'
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('meta_keywords') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Meta Description --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Meta Description (English)</label>
            <textarea name="meta_description[en]" rows="3" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200">{{ old('meta_description.en', $siteSetting->meta_description['en'] ?? '') }}</textarea>
            @error('meta_description.en') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Meta 描述 (中文)</label>
            <textarea name="meta_description[zh]" rows="3" 
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200">{{ old('meta_description.zh', $siteSetting->meta_description['zh'] ?? '') }}</textarea>
            @error('meta_description.zh') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Default Language --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Default Language</label>
            <select name="default_language_id" class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200">
                @foreach ($languages as $lang)
                    <option value="{{ $lang->id }}" {{ old('default_language_id', $siteSetting->default_language_id ?? '') == $lang->id ? 'selected' : '' }}>
                        {{ $lang->name }}
                    </option>
                @endforeach
            </select>
            @error('default_language_id') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Maintenance Mode --}}
        <div class="mb-6 flex items-center">
            <input type="checkbox" name="maintenance_mode" value="1" 
                class="rounded border-gray-300 text-indigo-600 shadow-sm"
                {{ old('maintenance_mode', $siteSetting->maintenance_mode ?? false) ? 'checked' : '' }}>
            <label class="ml-2 text-gray-700">Enable Maintenance Mode</label>
        </div>

        {{-- Maintenance Message --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Maintenance Message (English)</label>
            <input type="text" name="maintenance_message[en]" value="{{ old('maintenance_message.en', $siteSetting->maintenance_message['en'] ?? '') }}"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('maintenance_message.en') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">维护信息 (中文)</label>
            <input type="text" name="maintenance_message[zh]" value="{{ old('maintenance_message.zh', $siteSetting->maintenance_message['zh'] ?? '') }}"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('maintenance_message.zh') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Contact Email --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Contact Email</label>
            <input type="email" name="contact_email" value="{{ old('contact_email', $siteSetting->contact_email ?? '') }}"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('contact_email') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Contact Phone --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Contact Phone</label>
            <input type="text" name="contact_phone" value="{{ old('contact_phone', $siteSetting->contact_phone ?? '') }}"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('contact_phone') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Timezone --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Timezone</label>
            <input type="text" name="timezone" value="{{ old('timezone', $siteSetting->timezone ?? '') }}"
                placeholder="e.g. UTC, America/Montreal"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('timezone') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Default Currency --}}
        <div class="mb-6">
            <label class="block font-medium text-gray-700 mb-1">Default Currency</label>
            <input type="text" name="default_currency" value="{{ old('default_currency', $siteSetting->default_currency ?? '') }}"
                placeholder="e.g. USD, CAD"
                class="w-full rounded border-gray-300 shadow-sm focus:ring focus:ring-indigo-200" />
            @error('default_currency') <p class="text-red-600 text-sm mt-1">{{ $message }}</p> @enderror
        </div>

        {{-- Submit --}}
        <div class="text-right">
            <button type="submit"
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold px-6 py-2 rounded-lg">
                Save Settings
            </button>
        </div>
    </form>
</div>
@endsection
