@extends('admin.layouts.admin')

@section('content')
@php
    $locale = app()->getLocale();
@endphp

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        {{ $view ? 'Edit View' : 'Create New View' }}
                    </h1>
                    <p class="text-gray-600 mt-1">
                        {{ $view ? 'Modify the existing view configuration' : 'Set up a new content view with custom fields and output formats' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Form Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">

                @csrf

                <!-- Form Header -->
                <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">View Configuration</h2>
                </div>

                <div class="p-6 space-y-6">
                    <!-- View Name -->
                    <div class="space-y-2">
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            View Name
                            <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               name="name" 
                               id="name" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400" 
                               value="{{ old('name', $view->name ?? '') }}" 
                               placeholder="Enter a descriptive name for this view"
                               required>
                    </div>
<!-- Template Type -->
<div class="space-y-2">
    <label class="block text-sm font-medium text-gray-700">
        Template Type
        <span class="text-red-500">*</span>
    </label>

    @php
        $templateType = old('template_type', $view->template_type ?? 'data-list');
    @endphp

    <div class="flex flex-wrap gap-6 mt-1">
        <label class="inline-flex items-center">
            <input type="radio" name="template_type" value="data-list"
                   class="text-blue-600 focus:ring-blue-500 border-gray-300"
                   {{ $templateType == 'data-list' ? 'checked' : '' }}>
            <span class="ml-2">Data List</span>
        </label>

        <label class="inline-flex items-center">
            <input type="radio" name="template_type" value="data-detail"
                   class="text-blue-600 focus:ring-blue-500 border-gray-300"
                   {{ $templateType == 'data-detail' ? 'checked' : '' }}>
            <span class="ml-2">Data Detail</span>
        </label>

        <label class="inline-flex items-center">
            <input type="radio" name="template_type" value="form"
                   class="text-blue-600 focus:ring-blue-500 border-gray-300"
                   {{ $templateType == 'form' ? 'checked' : '' }}>
            <span class="ml-2">Form</span>
        </label>

        <label class="inline-flex items-center">
            <input type="radio" name="template_type" value="document"
                   class="text-blue-600 focus:ring-blue-500 border-gray-300"
                   {{ $templateType == 'document' ? 'checked' : '' }}>
            <span class="ml-2">Document</span>
        </label>
    </div>
</div>


                    <!-- Content Type -->
                    <div class="space-y-2">
                        <label for="content_type_id" class="block text-sm font-medium text-gray-700">
                            Content Type
                            <span class="text-red-500">*</span>
                        </label>
                        <select name="content_type_id" 
                                id="content_type_id" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white" 
                                required>
                            <option value="">Select a content type...</option>
                            @foreach($contentTypes as $ct)
                                @php
                                    $displayName = $ct->name[$locale] ?? $ct->name['en-us'] ?? 'Unknown';
                                @endphp
                                <option value="{{ $ct->id }}"
                                    {{ old('content_type_id', $view->content_type_id ?? '') == $ct->id ? 'selected' : '' }}>
                                    {{ $displayName }}
                                </option>
                            @endforeach
                        </select>
                    </div>
<!-- Filters -->
<div class="space-y-4 mt-4">

<!-- 1. Date Filter -->
<div class="space-y-2">
    <label class="block text-sm font-medium text-gray-700">
        Date Filter
    </label>

    <!-- 模式选择 -->
    <div class="flex space-x-4 items-center text-sm text-gray-600">
        <label>
            <input type="radio" name="date_mode" value="range" checked class="mr-1"> Date Range
        </label>
        <label>
            <input type="radio" name="date_mode" value="last_days" class="mr-1"> Last X Days
        </label>
    </div>

    <!-- 日期区间 -->
    <div class="flex space-x-2 items-center mt-1" id="date_range_inputs">
        <input type="date" name="date_from" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" placeholder="From">
        <input type="date" name="date_to" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" placeholder="To">
    </div>

    <!-- 最近 X 天 -->
    <div class="mt-1" id="last_days_input" style="display:none;">
        <input type="number" name="last_days" class="w-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" placeholder="Days">
    </div>

    <p class="text-xs text-gray-500">
        Choose a date range or enter the number of most recent days.
    </p>
</div>




    <!-- 2. Data ID List -->
    <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">
            Data ID List
        </label>
        <input type="text" name="data_id_list" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" placeholder="e.g. 1,2,3,4">
        <p class="text-xs text-gray-500">Comma-separated IDs to filter specific data.</p>
    </div>

    <!-- 3. Pagination Switch -->
    <div class="flex items-center space-x-2">
        <input type="checkbox" name="enable_pagination" id="enable_pagination" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" checked>
        <label for="enable_pagination" class="text-sm text-gray-700">Enable Pagination</label>

        <!-- 每页显示条数 -->
        <input type="number" name="per_page" id="per_page" class="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="20" min="1" step="1">
        <span class="text-sm text-gray-500">items per page</span>
    </div>


</div>

                    <!-- Fields Configuration -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="block text-sm font-medium text-gray-700">
                                Fields Configuration
                            </label>
                        </div>
                        
                        <div id="fields-container" class="space-y-4 min-h-[100px] p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                            <div class="text-center text-gray-500 py-8">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p class="text-sm">Select a content type to load available fields</p>
                            </div>
                        </div>
                    </div>

                    <!-- Output Types -->
                    <div class="space-y-2">
                        <label for="output_types" class="block text-sm font-medium text-gray-700">
                            Output Types
                        </label>
                        <div class="grid grid-cols-2 gap-3 sm:grid-cols-4">
                            <label class="relative flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                <input type="checkbox" 
                                       name="config[output_types][]" 
                                       value="html" 
                                       class="sr-only peer"
                                       {{ in_array('html', old('config.output_types', $view->config['output_types'] ?? [])) ? 'checked' : '' }}>
                                <div class="w-4 h-4 border-2 border-gray-300 rounded peer-checked:bg-blue-600 peer-checked:border-blue-600 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white opacity-0 peer-checked:opacity-100" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="ml-3 text-sm font-medium text-gray-700">HTML</span>
                            </label>

                            <label class="relative flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                <input type="checkbox" 
                                       name="config[output_types][]" 
                                       value="xls" 
                                       class="sr-only peer"
                                       {{ in_array('xls', old('config.output_types', $view->config['output_types'] ?? [])) ? 'checked' : '' }}>
                                <div class="w-4 h-4 border-2 border-gray-300 rounded peer-checked:bg-blue-600 peer-checked:border-blue-600 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white opacity-0 peer-checked:opacity-100" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="ml-3 text-sm font-medium text-gray-700">Excel</span>
                            </label>

                            <label class="relative flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                <input type="checkbox" 
                                       name="config[output_types][]" 
                                       value="csv" 
                                       class="sr-only peer"
                                       {{ in_array('csv', old('config.output_types', $view->config['output_types'] ?? [])) ? 'checked' : '' }}>
                                <div class="w-4 h-4 border-2 border-gray-300 rounded peer-checked:bg-blue-600 peer-checked:border-blue-600 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white opacity-0 peer-checked:opacity-100" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="ml-3 text-sm font-medium text-gray-700">CSV</span>
                            </label>

                            <label class="relative flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                <input type="checkbox" 
                                       name="config[output_types][]" 
                                       value="api" 
                                       class="sr-only peer"
                                       {{ in_array('api', old('config.output_types', $view->config['output_types'] ?? [])) ? 'checked' : '' }}>
                                <div class="w-4 h-4 border-2 border-gray-300 rounded peer-checked:bg-blue-600 peer-checked:border-blue-600 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white opacity-0 peer-checked:opacity-100" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="ml-3 text-sm font-medium text-gray-700">API</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Hidden Config Input -->
                <input type="hidden" name="config" id="config-json" value="{{ old('config', json_encode($view->config ?? [])) }}">

                <!-- Form Footer -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <span class="inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            All changes will be saved automatically
                        </span>
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" 
                                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                                onclick="window.history.back()">
                            Cancel
                        </button>
                        <button type="button" id="saveFilterBtn"
                                class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-sm">
                            <span class="inline-flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Save View
                            </span>
                        </button>
                    </div>
                </div>
        </div>
    </div>
</div>

<div id="filterModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white p-6 rounded shadow-lg w-96">
        <form class="space-y-4" id="filterForm"></form>
        <div class="flex justify-end space-x-2 mt-4">
            <button type="button" class="bg-gray-300 px-3 py-1 rounded" onclick="document.getElementById('filterModal').classList.add('hidden')">Cancel</button>
            <button type="submit" form="filterForm"  class="bg-blue-600 text-white px-3 py-1 rounded">Save</button>
        </div>
        
    </div>
</div>


@endsection

@push('scripts')
@vite(['resources/admin/js/filters/filter.js'])
<script>
    function collectViewConfig(contentTypeId) {
       // 1. 取本地 filterConditions
       let filters = {};
    const stored = localStorage.getItem('filterConditions');
    if (stored) {
        try {
            const allFilters = JSON.parse(stored);
            if (allFilters[contentTypeId]) {
                filters = allFilters[contentTypeId];
            }
        } catch (e) {
            console.warn("Invalid JSON in localStorage filterConditions");
        }
    }
    // 2. 日期过滤器
    const dateMode = document.querySelector('input[name="date_mode"]:checked')?.value || null;
    let dateFilter = null;
    if (dateMode === "range") {
        dateFilter = {
            mode: "range",
            from: document.querySelector('input[name="date_from"]')?.value || null,
            to: document.querySelector('input[name="date_to"]')?.value || null
        };
    } else if (dateMode === "last_days") {
        dateFilter = {
            mode: "last_days",
            days: parseInt(document.querySelector('input[name="last_days"]')?.value) || null
        };
    }

    // 3. Data ID List
    const dataIdListRaw = document.querySelector('input[name="data_id_list"]')?.value || "";
    const dataIdList = dataIdListRaw
        .split(",")
        .map(id => id.trim())
        .filter(id => id.length > 0);

    // 4. 分页配置
    const pagination = {
        enabled: document.querySelector('#enable_pagination')?.checked ?? true,
        per_page: parseInt(document.querySelector('#per_page')?.value) || 20
    };

    // 5. 排序配置
    const sort = {
        field: document.querySelector('#sort_field')?.value || 'created_at',
        direction: document.querySelector('#sort_direction')?.value || 'desc'
    };
    const selectedFields = Array.from(document.querySelectorAll('[id^="field-checkbox-"]'))
    .filter(cb => cb.checked) 
    .map(cb => cb.id.replace('field-checkbox-', '')); 
    // 6. 组装统一 config
    return {
        filters,
        date_filter: dateFilter,
        data_id_list: dataIdList,
        pagination,
        sort,
        selected_fields: selectedFields
    };
}
document.getElementById('saveFilterBtn').addEventListener('click', function() {
    const contentTypeId = document.getElementById('content_type_id').value;
    const viewName = document.getElementById('name').value;
    const templateType = document.querySelector('input[name="template_type"]:checked')?.value;
    submitView(contentTypeId, viewName, templateType);
});
async function submitView(contentTypeId, viewName, templateType) {
    const config = collectViewConfig(contentTypeId);

    const payload = {
        name: viewName,
        content_type_id: contentTypeId,
        template_type: templateType,
        config: config,
        _token: document.querySelector('meta[name="csrf-token"]').content
    };

    console.log("Submitting payload:", payload);

    const response = await fetch(`/${window.kazcms.locale}/admin/kazview/save/{{ $view->id ?? 0 }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(payload)
    });

    const result = await response.json();
    if (result.success) {
        alert("View saved successfully!");
        const stored = localStorage.getItem('filterConditions');
        if (stored) {
            try {
                const allFilters = JSON.parse(stored);
                if (allFilters[contentTypeId]) {
                    delete allFilters[contentTypeId]; // 删除对应 key
                    localStorage.setItem('filterConditions', JSON.stringify(allFilters));
                }
            } catch (e) {
                console.warn("Invalid JSON in localStorage filterConditions");
            }
        }
    } else {
        alert("Save failed: " + (result.message || "Unknown error"));
    }
}

    // 简单 JS 切换显示
    const dateModeRadios = document.querySelectorAll('input[name="date_mode"]');
    const rangeDiv = document.getElementById('date_range_inputs');
    const lastDaysDiv = document.getElementById('last_days_input');

    dateModeRadios.forEach(radio => {
        radio.addEventListener('change', () => {
            if (radio.value === 'range') {
                rangeDiv.style.display = 'flex';
                lastDaysDiv.style.display = 'none';
            } else {
                rangeDiv.style.display = 'none';
                lastDaysDiv.style.display = 'block';
            }
        });
    });

    const paginationCheckbox = document.getElementById('enable_pagination');
const perPageInput = document.getElementById('per_page');

paginationCheckbox.addEventListener('change', () => {
    perPageInput.disabled = !paginationCheckbox.checked;
});

</script>

<script>
let fields = @json($view->config['fields'] ?? []);

function renderFields() {
    const container = document.getElementById('fields-container');
    
    if (fields.length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p class="text-sm">No fields configured yet</p>
                <p class="text-xs text-gray-400 mt-1">Add fields to customize your view</p>
            </div>
        `;
        return;
    }

    // 按 field_type 分组
    const grouped = {};
    fields.forEach(f => {
        if (!grouped[f.field_type]) grouped[f.field_type] = [];
        console.log(f);
        grouped[f.field_type].push(f);
    });

    container.innerHTML = '';
    container.className = 'space-y-6 p-4 border border-gray-200 rounded-lg bg-white';

    Object.keys(grouped).forEach(type => {
        const typeDiv = document.createElement('div');
        typeDiv.className = 'space-y-3';

        // Type header
        const header = document.createElement('div');
        header.className = 'flex items-center space-x-2 pb-2 border-b border-gray-100';
        header.innerHTML = `
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <h3 class="text-sm font-semibold text-gray-800 uppercase tracking-wide">${type.charAt(0).toUpperCase() + type.slice(1)} Fields</h3>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">${grouped[type].length}</span>
        `;
        typeDiv.appendChild(header);

        // Fields grid
        const fieldsGrid = document.createElement('div');
        fieldsGrid.className = 'grid gap-2 sm:grid-cols-2 lg:grid-cols-3';

        grouped[type].forEach((f, index) => {
            const fieldIndex = fields.indexOf(f);
            const fieldDiv = document.createElement('div');
            fieldDiv.className = `relative p-3 border rounded-lg transition-all duration-200 cursor-pointer ${f.enabled ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50 opacity-60'}`;
            
            fieldDiv.innerHTML = `
    <div class="flex flex-col" id="field-${f.k}">
        <!-- 第一行：checkbox + label + 按钮 -->
        <div class="flex items-center justify-between">
            <label class="flex items-center space-x-2 cursor-pointer flex-1">
                <input type="checkbox" id="field-checkbox-${f.k}"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" 
                    ${f.enabled ? 'checked' : ''} 
                    onchange="toggleField(${fieldIndex})">
                <span class="text-sm font-medium text-gray-700 truncate" id="field-label-${f.k}">${f.label}</span>
            </label>
            <button type="button" 
                    class="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200 edit-filter-btn" 
                    title="Edit field"
                    data-type="${f.field_type}"
                    data-subtype="${f.sub_type || 'text'}"
                    data-name="${f.label}">
                <!-- 直观的铅笔图标 SVG -->
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15.232 5.232l3.536 3.536M9 11l6 6L6 18l3-6z"></path>
                </svg>
            </button>
        </div>
        
        <!-- 第二行：条件 -->
        <div class="ml-6 mt-2 space-y-1 text-sm text-gray-600" id="conditions-${f.k}">
            <!-- Conditions will be appended here -->
        </div>
    </div>
`;


// ======= 新增：只给按钮绑定点击事件 =======
const editBtn = fieldDiv.querySelector('.edit-filter-btn');
editBtn.addEventListener('click', (e) => {
    e.stopPropagation(); // 阻止冒泡到 fieldDiv
    const type = editBtn.dataset.type;
    const subType = editBtn.dataset.subtype;
    const name = editBtn.dataset.name;
    filterModal.open(f.k,type, subType, name);
});

            fieldsGrid.appendChild(fieldDiv);
        });

        typeDiv.appendChild(fieldsGrid);
        container.appendChild(typeDiv);
    });

    updateConfigInput();
}

function toggleField(index) {
    fields[index].enabled = !fields[index].enabled;
    renderFields();
}

function editField(index) {
    // Enhanced edit modal
    const field = fields[index];
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Edit Field: ${field.label}</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Label</label>
                        <input type="text" id="edit-label" value="${field.label}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="edit-enabled" ${field.enabled ? 'checked' : ''} class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="edit-enabled" class="ml-2 text-sm text-gray-700">Enabled</label>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">Cancel</button>
                    <button onclick="saveFieldEdit(${index})" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Save</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function saveFieldEdit(index) {
    const modal = document.querySelector('.fixed');
    const label = modal.querySelector('#edit-label').value;
    const enabled = modal.querySelector('#edit-enabled').checked;
    
    fields[index].label = label;
    fields[index].enabled = enabled;
    
    modal.remove();
    renderFields();
}

function addField() {
    fields.push({id:'', label:'New Field', field_type: 'text', settings:{}, enabled: true});
    renderFields();
}

function removeField(index) {
    fields.splice(index, 1);
    renderFields();
}

function updateField(index, key, value) {
    fields[index][key] = value;
    updateConfigInput();
}

function updateConfigInput() {
    const configInput = document.getElementById('config-json');
    const outputTypesCheckboxes = document.querySelectorAll('input[name="config[output_types][]"]:checked');
    const outputTypes = Array.from(outputTypesCheckboxes).map(cb => cb.value);

    const config = {
        fields: fields,
        output_types: outputTypes
    };
    configInput.value = JSON.stringify(config);
}

document.getElementById('content_type_id').addEventListener('change', function() {
    let contentTypeId = this.value;
    if (contentTypeId) {
        fetchFields(contentTypeId);
    } else {
        fields = [];
        renderFields();
    }
});

function fetchFields(contentTypeId) {
    // Show loading state
    const container = document.getElementById('fields-container');
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="inline-flex items-center space-x-2">
                <svg class="animate-spin w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">Loading fields...</span>
            </div>
        </div>
    `;

    fetch(`/${window.kazcms.locale}/admin/kazview/content-type/${contentTypeId}/fields`)
        .then(response => response.json())
        .then(data => {
            // data 是对象，用 Object.values 转成数组
                    // 给每个 field 加上 k 字段
                    fields = Object.entries(data).map(([key, field]) => {
    field.k = key;  // 给 field 加 key
    return field;   // 返回数组元素
});
            renderFields();
        })
        .catch(err => {
            console.error('Error fetching fields:', err);
            container.innerHTML = `
                <div class="text-center py-8">
                    <svg class="w-12 h-12 mx-auto mb-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-sm text-red-600">Error loading fields</p>
                    <p class="text-xs text-gray-500 mt-1">Please try selecting the content type again</p>
                </div>
            `;
        });
}

// Add event listeners for output types
document.addEventListener('DOMContentLoaded', function() {
    const outputTypeCheckboxes = document.querySelectorAll('input[name="config[output_types][]"]');
    outputTypeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateConfigInput);
    });
    
    renderFields();
});
</script>

@endpush