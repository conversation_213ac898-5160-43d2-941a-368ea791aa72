@extends('admin.layouts.admin')

@section('content')
<div class="container mx-auto px-4">
    <h1 class="text-2xl font-bold mb-4">Views List</h1>

    {{-- Search bar --}}
    <form method="GET" action="{{ localized_route('admin.kazview.index') }}" class="mb-4">
        <input type="text" name="search" value="{{ request('search') }}"
               placeholder="Search by name or template type"
               class="border rounded p-2 w-1/3">
        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Search</button>
    </form>

    <table class="table-auto w-full border-collapse border border-gray-300">
        <thead class="bg-gray-100">
            <tr>
                <th class="border px-4 py-2">ID</th>
                <th class="border px-4 py-2">Name</th>
                <th class="border px-4 py-2">Template Type</th>
                <th class="border px-4 py-2">Content Type ID</th>
                <th class="border px-4 py-2">Status</th>
                <th class="border px-4 py-2">Created At</th>
                <th class="border px-4 py-2">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($views as $view)
                <tr>
                    <td class="border px-4 py-2">{{ $view->id }}</td>
                    <td class="border px-4 py-2">{{ $view->name }}</td>
                    <td class="border px-4 py-2">{{ $view->template_type }}</td>
                    <td class="border px-4 py-2">{{ $view->content_type_id }}</td>
                    <td class="border px-4 py-2">
                        @if($view->status)
                            <span class="text-green-600 font-semibold">Enabled</span>
                        @else
                            <span class="text-red-600 font-semibold">Disabled</span>
                        @endif
                    </td>
                    <td class="border px-4 py-2">{{ $view->created_at->format('Y-m-d H:i') }}</td>
                    <td class="border px-4 py-2">
                        <a href="{{ localized_route('admin.kazview.edit', ['id' => $view->id]) }}" class="text-blue-500">Edit</a> |
                        <a href="javascript:void(0);" 
                        class="delete-view text-red-500" 
                        data-id="{{ $view->id }}">
                        Delete
                        </a>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center py-4">No views found.</td>
                </tr>
            @endforelse
        </tbody>
    </table>


</div>
@endsection
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.delete-view').forEach(function (el) {
        el.addEventListener('click', function (e) {
            e.preventDefault();

            const id = this.dataset.id;
            if (!confirm('Are you sure you want to delete this view?')) {
                return;
            }

            fetch(`/${window.kazcms.locale}/admin/kazview/delete`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": "{{ csrf_token() }}"
                },
                body: JSON.stringify({ id: id })
            })
            .then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert("Failed to delete view");
                }
            })
            .catch(() => {
                alert("Error occurred while deleting view");
            });
        });
    });
});
</script>
@endpush
