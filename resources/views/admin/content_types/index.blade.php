@extends('admin.layouts.admin')

@section('content')
<div class="mb-4">
<nav class="text-sm text-gray-500 mb-4" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1">
        <li>
            <a href="{{ localized_route('admin.content_types.index') }}" class="text-blue-600 hover:underline">
                Content Types
            </a>
        </li>
        <li>/</li>
        <li>
            <a href="{{ localized_route('admin.content_types.edit', ['type' => 'data']) }}" class="text-blue-600 hover:underline">
                Add Data Content
            </a>
        </li>
        <li>/</li>
        <li>
            <a href="{{ localized_route('admin.content_types.edit', ['type' => 'form']) }}" class="text-blue-600 hover:underline">
                Add Form Content
            </a>
        </li>
        <li>/</li>
        <li>
            <a href="{{ localized_route('admin.content_types.edit', ['type' => 'document']) }}" class="text-blue-600 hover:underline">
                Add Document Content
            </a>
        </li>
    </ol>
</nav>


    <div class="overflow-x-auto bg-white shadow rounded-lg">
        <table class="min-w-full text-sm text-left text-gray-600">
            <thead class="bg-gray-100 text-xs uppercase text-gray-500">
                <tr>
                    <th class="px-4 py-3">{{ __('messages.Name') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Frontend') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Order') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Data') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Fields') }}</th>
                    <th class="px-4 py-3">{{ __('messages.SEO') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Templates') }}</th>
                    <th class="px-4 py-3">{{ __('messages.Edit') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($contentTypes as $type)
                <tr class="border-b hover:bg-gray-50" id="tr_{{ $type->id }}">
                    <td class="px-4 py-3 font-medium">
                        {{ $type->name[app()->getLocale()] ?? reset($type->name) }}
                    </td>
                    <td class="px-4 py-3">
                        @if($type->show_on_frontend)
                        <span class="inline-block px-2 py-1 text-green-700 bg-green-100 rounded text-xs font-semibold">Visible</span>
                        @else
                        <span class="inline-block px-2 py-1 text-red-700 bg-red-100 rounded text-xs font-semibold">Hidden</span>
                        @endif
                    </td>
                    <td class="px-4 py-3">
                        <div class="flex items-center space-x-2">
                            <input type="number" min="0" value="{{ $type->display_order ?? 0 }}"
                                class="w-20 border-gray-300 rounded-md shadow-sm y_element_order text-sm"
                                id="y_order_{{ $type->id }}" data-id="{{ $type->id }}">
                            <button type="button" class="text-blue-500 hover:text-blue-700 y_order_update" data-id="{{ $type->id }}">
                                <i class="fas fa-arrow-up"></i>
                            </button>
                        </div>
                    </td>
                    <td class="px-4 py-3 space-x-1">
                        <a href="{{localized_route('admin.data.submit', ['content_type_id' =>$type->id, 'data_id' => 0]) }}" class="text-blue-600 hover:underline">Add</a>
                        <a href="{{localized_route('admin.data.list', ['id' => $type->id]) }}" class="text-blue-600 hover:underline">List</a>
                        <a href="/admin/data/export/{{ $type->id }}" class="text-blue-600 hover:underline">Export</a>
                    </td>
                    <td class="px-4 py-3">
                        <a href="{{localized_route('admin.fields.list', [$type->id]) }}" class="text-blue-600 hover:underline">Add</a>
                        <a href="{{localized_route('admin.fields.list', [$type->id]) }}" class="text-blue-600 hover:underline">List</a>
                        <a href="{{localized_route('admin.fields.list', [$type->id]) }}" class="text-blue-600 hover:underline">Export</a>
                    </td>
                    <td class="px-4 py-3">
                        <a href="/admin/seo_meta/{{ $type->id }}" class="text-blue-600 hover:underline">Edit</a>
                    </td>
                    <td class="px-4 py-3">
                        <a href="/admin/content-templates/list/{{ $type->id }}" class="text-blue-600 hover:underline">Templates</a>
                    </td>
                    <td class="px-4 py-3">
                        <a href="{{localized_route('admin.content_types.edit', ['id' => $type->id]) }}" class="text-blue-600 hover:underline">Edit</a>
                    </td>
                    
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow" id="y_allorder_update_btn">
            Update All Orders
        </button>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const updateElementOrder = (elementId, singleUpdate) => {
            let data = [];

            if (singleUpdate === 1) {
                data.push({
                    id: elementId,
                    order: document.getElementById(`y_order_${elementId}`).value
                });
            } else {
                document.querySelectorAll('.y_element_order').forEach(input => {
                    data.push({
                        id: input.dataset.id,
                        order: input.value
                    });
                });
            }

            fetch('/admin/content-types/order/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ elements: data })
            })
            .then(res => res.json())
            .then(res => {
                if (res.success) {
                    alert('Order updated successfully.');
                } else {
                    alert('Failed to update order.');
                }
            })
            .catch(() => alert('Server error.'));
        };

        document.querySelectorAll('.y_order_update').forEach(btn => {
            btn.addEventListener('click', () => {
                updateElementOrder(btn.dataset.id, 1);
            });
        });

        document.getElementById('y_allorder_update_btn').addEventListener('click', () => {
            updateElementOrder(0, 0);
        });
    });
</script>
@endpush
@endsection
