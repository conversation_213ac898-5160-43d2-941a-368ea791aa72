@extends('admin.layouts.admin')

@section('content')
@php
    $isNew = empty($contentType);
    $ct = $contentType ?? (object)[
        'id' => 0,
        'name' => [],
        'slug' => '',
        'keywords' => '',
        'description' => '',
        'show_on_frontend' => false,
        'render_type' => 'normal',
    ];
@endphp

@if ($errors->any())
    <div class="mb-4 text-red-600">
        <ul class="list-disc list-inside text-sm">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<div class="max-w-4xl mx-auto px-4 py-6">
    <!-- Breadcrumb -->
    <nav class="text-sm text-gray-500 mb-4" aria-label="Breadcrumb">
        <ol class="flex space-x-2">
            <li><a href="/admin/content-types" class="text-blue-600 hover:underline">Content Types</a></li>
            <li>/</li>
            <li class="text-gray-700">Edit</li>
        </ol>
    </nav>

    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">{{__('messages.Edit Content Type')}}</h2>

        <form method="POST" action="{{ localized_route('admin.content_types.save', [$ct->id]) }}" method="POST"  id="contentTypeForm" class="space-y-6" autocomplete="off">
            @csrf
            <input type="hidden" name="id" value="{{ $ct->id }}" />

            <!-- Name -->
            @php
                $languages = getActiveLanguages(); 
            @endphp

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-1">Content Type Name</label>
                <div class="space-y-2">
                    @foreach ($languages as $lang)
                        @php
                            $code = $lang['code'];
                            $label = $lang['name'][$code] ?? strtoupper($code);
                            $value = $ct->name[$code] ?? '';
                        @endphp
                        <div>
                            <label for="name_{{ $code }}" class="block text-xs text-gray-500">{{ $label }}</label>
                            <input
                                type="text"
                                name="name[{{ $code }}]"
                                id="name_{{ $code }}"
                                value="{{ $value }}"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                placeholder="Enter name in {{ $label }}">
                        </div>
                    @endforeach
                </div>
            </div>


            <!-- Slug -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                <div class="space-y-2">
                    @foreach ($languages as $lang)
                        @php
                            $code = $lang['code'];
                            $label = $lang['name'][$code] ?? strtoupper($code);
                            $value = $ct->slug[$code] ?? '';
                        @endphp
                        <div>
                            <label for="slug_{{ $code }}" class="block text-xs text-gray-500">{{ $label }}</label>
                            <input
                                type="text"
                                name="slug[{{ $code }}]"
                                id="slug_{{ $code }}"
                                value="{{ $value }}"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                placeholder="Slug in {{ $label }}">
                        </div>
                    @endforeach
                </div>
            </div>


            <!-- Show on Frontend -->
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="show_on_frontend" name="show_on_frontend" type="checkbox" value="1"
                           class="h-4 w-4 text-blue-600 border-gray-300 rounded"
                           {{ $ct->show_on_frontend ? 'checked' : '' }}>
                </div>
                <div class="ml-3 text-sm">
                    <label for="show_on_frontend" class="font-medium text-gray-700">Display in frontend</label>
                    <p class="text-gray-500">Enable this if the content type should appear on the frontend.</p>
                </div>
            </div>

            <!-- Show Form or Data -->
            <fieldset>
                <legend class="text-sm font-medium text-gray-700 mb-2">Frontend Mode</legend>
                <div class="flex gap-6">
                    <label class="inline-flex items-center">
                        <input type="radio" name="render_type" value="normal"
                               class="text-blue-600 focus:ring-blue-500 border-gray-300"
                               {{ $ct->render_type == 'normal' ? 'checked' : '' }}>
                        <span class="ml-2 text-sm text-gray-700">Data</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="render_type" value="form"
                               class="text-blue-600 focus:ring-blue-500 border-gray-300"
                               {{ $ct->render_type == 'form' ? 'checked' : '' }}>
                        <span class="ml-2 text-sm text-gray-700">Form</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="render_type" value="document"
                               class="text-blue-600 focus:ring-blue-500 border-gray-300"
                               {{ $ct->render_type == 'document' ? 'checked' : '' }}>


                        <span class="ml-2 text-sm text-gray-700">Document</span>
                    </label>
                </div>
                <p class="text-sm text-gray-500 mt-1">Choose how this content type appears to end users.</p>
            </fieldset>
            <!-- Form Settings -->
            <div id="kazcmsFormSettings" class="hidden p-4 border border-gray-300 rounded-lg bg-gray-50 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Start Date</label>
                    <input type="datetime-local" name="form_start_time"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">End Date</label>
                    <input type="datetime-local" name="form_end_time"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

            </div>
            <!-- Keywords & Description（可选字段） -->
            @foreach (['keywords' => 'SEO Keywords', 'description' => 'Description'] as $field => $label)
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $label }}</label>
                <div class="space-y-2">
                    @foreach ($languages as $lang)
                        @php
                            $code = $lang['code'];
                            $langLabel = $lang['name'][$code] ?? strtoupper($code);
                            $value = $ct->$field[$code] ?? '';
                        @endphp
                        <div>
                            <label for="{{ $field }}_{{ $code }}" class="block text-xs text-gray-500">{{ $langLabel }}</label>
                            @if ($field === 'description')
                                <textarea
                                    name="{{ $field }}[{{ $code }}]"
                                    id="{{ $field }}_{{ $code }}"
                                    rows="3"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                    placeholder="{{ $label }} in {{ $langLabel }}">{{ $value }}</textarea>
                            @else
                                <input
                                    type="text"
                                    name="{{ $field }}[{{ $code }}]"
                                    id="{{ $field }}_{{ $code }}"
                                    value="{{ $value }}"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                    placeholder="{{ $label }} in {{ $langLabel }}">
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
            @endforeach


            <!-- Actions -->
            <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 shadow-sm">
                        <i class="fas fa-save mr-2"></i> Save
                    </button>
                    <a href="/admin/content-types" class="inline-flex items-center px-4 py-2 border rounded text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                </div>
                <button type="button" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 shadow-sm" data-modal-toggle="deleteModal">
                    <i class="fas fa-trash mr-2"></i> Delete
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden bg-gray-800 bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-sm text-gray-600">Are you sure you want to delete this content type? This action cannot be undone.</p>
        <div class="mt-6 flex justify-end space-x-2">
            <button class="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50" onclick="document.getElementById('deleteModal').classList.add('hidden')">Cancel</button>
            <a href="/admin/content-types/delete/{{ $ct->id }}" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Delete Permanently</a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    // auto-generate slug
    if(false) {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    nameInput.addEventListener('input', () => {
        if (!slugInput.dataset.manual) {
            slugInput.value = nameInput.value.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
        }
    });

    slugInput.addEventListener('input', () => {
        slugInput.dataset.manual = true;
    });

    // toggle modal
    document.querySelectorAll('[data-modal-toggle="deleteModal"]').forEach(btn => {
        btn.addEventListener('click', () => {
            document.getElementById('deleteModal').classList.remove('hidden');
        });
    });
}
    document.querySelectorAll('input[name="render_type"]').forEach((radio) => {
        radio.addEventListener('click', () => {
            const selectedValue = document.querySelector('input[name="render_type"]:checked')?.value;
            const settingsDiv = document.getElementById('kazcmsFormSettings');

            if (selectedValue === 'form') {
                settingsDiv.classList.remove('hidden');
            } else {
                settingsDiv.classList.add('hidden');
            }
        });
    });



});
</script>
@endpush
