@extends('admin.layouts.admin')

@section('title', __('messages.Role & Permission'))
@section('breadcrumb', __('messages.Role & Permission'))

@section('content')
<div class="p-6 bg-white rounded shadow">

    <a href="{{ localized_route('admin.user_groups.form') }}" class="mb-4 inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
        {{ __('messages.Create New Group') }}
    </a>

    @if(session('success'))
        <div class="mb-4 text-green-600">{{ session('success') }}</div>
    @endif

    <table class="w-full border border-gray-200 rounded" >
        <thead class="bg-gray-100">
            <tr>
                <th class="p-2 border border-gray-300">#</th> {{-- ✅ 选择框列 --}}
                <th class="p-2 border border-gray-300">ID</th>
                <th class="p-2 border border-gray-300">{{ __('messages.Group Name') }}</th>
                <th class="p-2 border border-gray-300">{{ __('messages.Actions') }}</th>
            </tr>
        </thead>
        <tbody>
        @forelse ($groups as $group)
            <tr class="odd:bg-gray-50" data-group-id="{{ $group->id }}">
                <td class="p-2 border border-gray-300 text-center">
                    <input type="checkbox" name="group_select[]" value="{{ $group->id }}" autocomplete="off">
                </td>
                <td class="p-2 border border-gray-300">{{ $group->id }}</td>
                <td class="p-2 border border-gray-300">
                    {!! str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $group->level) !!}
                    {{ $group->getName(app()->getLocale()) }}
                </td>
                <td class="p-2 border border-gray-300">
                    <a href="{{ localized_route('admin.user_groups.form', [$group->id]) }}" class="text-blue-600 hover:underline mr-2">{{ __('messages.Edit') }}</a>

                    <a href="#" class="text-blue-600 hover:underline mr-2 openPermissionModalBtn" data-group-id="{{ $group->id }}">{{ __('messages.Assign Permissions') }}</a>

                    <form action="{{ localized_route('admin.user_groups.destroy', [$group->id]) }}" method="POST" class="inline" onsubmit="return confirm('{{ __('messages.Are you sure to delete this group?') }}');">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="text-red-600 hover:underline">{{ __('messages.Delete') }}</button>
                    </form>
                </td>
            </tr>
        @empty
            <tr><td colspan="6" class="text-center p-4">{{ __('messages.No user groups found.') }}</td></tr>
        @endforelse

        @if(!empty($groups))


        <tr>
            <td colspan="6" class="text-center p-4">
            <button type="button" id="addPermissionsBtn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 hidden">
                {{ __('messages.Assign Permissions') }}
            </button>

            </td>
        </tr>
        @endif
        </tbody>
    </table>
</div>

<div id="permissionModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg w-11/12 md:w-2/3 lg:w-1/2 max-h-[90vh] overflow-auto p-4 shadow-lg">
        <h2 class="text-xl font-semibold mb-4">{{ __('messages.Assign Permissions') }}</h2>
        <form id="permissionForm">
            <input type="hidden" name="assignee_type" value="group">
            <input type="hidden" name="modalAssigneeIds" id="modalAssigneeIds">

            <div id="permissionTree" class="mb-4">
                <p>{{ __('messages.Loading...') }}</p>
            </div>

            <div class="text-right">
                <button type="button" id="closeModal" class="mr-2 px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">Cancel</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Save</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('scripts')
@vite(['resources/admin/js/permissions.js'])

</script>
@endpush
