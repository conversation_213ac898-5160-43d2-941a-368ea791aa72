@extends('admin.layouts.admin')
@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8 px-4">
    <div class="max-w-2xl mx-auto">

        <!-- Main Form Card -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden p-4">
            <!-- Error Messages -->
            @if ($errors->any())
            <div class="bg-red-50 border-l-4 border-red-400 p-6 m-6 rounded-lg">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 mb-2">Please fix the following errors:</h3>
                        <ul class="text-sm text-red-700 space-y-1">
                            @foreach ($errors->all() as $error)
                            <li class="flex items-center">
                                <span class="w-1.5 h-1.5 bg-red-400 rounded-full mr-2"></span>
                                {{ $error }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endif

            <!-- Form Content -->
            <div class="p-8">
                <form action="{{ localized_route('admin.user_groups.save') }}" method="POST" class="space-y-8">
                    @csrf
                    <input type="hidden" name="id" value="{{ old('id', $group->id) }}">

                    <!-- Parent Group Selection -->
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-900 mb-3">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Parent Group
                            </span>
                        </label>
                        <div class="relative">
                            <select name="parent_id" class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 pr-8 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:bg-gray-100">
                                <option value="0" @if(old('parent_id', $group->parent_id) == 0) selected @endif>-- None (Root) --</option>
                                @foreach ($allGroups as $parent)
                                <option value="{{ $parent->id }}" @if(old('parent_id', $group->parent_id) == $parent->id) selected @endif>
                                    {!! str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $parent->level) !!}{{ $parent->getName('en') }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Language Names Section -->
                    <div class="space-y-6">
                        <div class="flex items-center border-b border-gray-200 pb-2">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900">Group Names</h3>
                        </div>
                        
                        @foreach ($languages as $langCode => $langName)
                        <div class="space-y-2">
                            <label for="name_{{ $langCode }}" class="block text-sm font-medium text-gray-700">
                                <span class="flex items-center justify-between">
                                    <span>{{__('messages.Group Name')}} ({{ $langName->name }})</span>
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                        {{ strtoupper($langName->code) }}
                                    </span>
                                </span>
                            </label>
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="name_{{ $langName->code }}" 
                                    name="name[{{ $langName->code }}]" 
                                    maxlength="60"
                                    value="{{ old("name.$langName->code", $group->name[$langName->code] ?? '') }}"
                                    class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:bg-gray-100"
                                    placeholder="Enter group name in {{ $langName->name }}"
                                    required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-xs text-gray-400">60 max</span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Sort Order -->
                    <div class="space-y-2">
                        <label for="sort_order" class="block text-sm font-medium text-gray-700">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                </svg>
                                Sort Order
                            </span>
                        </label>
                        <input 
                            type="number" 
                            id="sort_order" 
                            name="sort_order"
                            value="{{ old('sort_order', $group->sort_order ?? 0) }}"
                            class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:bg-gray-100"
                            placeholder="Enter display order (e.g., 1, 2, 3...)">
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-8 border-t border-gray-200">
                        <a href="{{ localized_route('admin.user_groups.index') }}"
                           class="inline-flex items-center px-6 py-3 text-sm font-medium text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 hover:text-gray-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Cancel
                        </a>
                        
                        <button type="submit"
                                class="inline-flex items-center px-8 py-3 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                            @if($group->id)
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                {{ __('messages.Update') }}
                            @else
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                {{ __('messages.Create') }}
                            @endif
                        </button>
                    </div>
                </form>
            </div>
        </div>

       
    </div>
</div>
@endsection