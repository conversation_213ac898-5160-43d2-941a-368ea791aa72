@extends('admin.layouts.admin')

@section('title', __('Pages List'))

@section('content')
@if (session('success'))
    <div 
        x-data="{ show: true }" 
        x-init="setTimeout(() => show = false, 1000)" 
        x-show="show"
        x-transition
        class="fixed top-4 right-4 z-50 px-3 py-2 rounded-md bg-green-500 text-white text-sm shadow-md"
    >
        {{ session('success') }}
    </div>
@endif

<div class="w-full bg-white shadow-xl rounded-2xl overflow-hidden">
    <!-- Header -->
    <div class="px-8 py-6 bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-between">
        <h1 class="text-2xl font-bold">{{ __('Pages List') }}</h1>
        <a href="{{ localized_route('admin.pages.edit',[0]) }}"
           class="bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold shadow hover:bg-gray-100 transition">
            + {{ __('Add Page') }}
        </a>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full border-collapse">
            <thead>
                <tr class="bg-gray-100 text-gray-700 text-left">
                    <th class="px-6 py-3 border-b">{{ __('ID') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Enable') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Main') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Content Type') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Slug') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Title') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Created At') }}</th>
                    <th class="px-6 py-3 border-b">{{ __('Actions') }}</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($pages as $page)
                    @php
                        $locale = app()->getLocale();
                        $slug  = $page->slug[$locale]  ?? $page->slug['all']  ?? '';
                        $title = $page->title[$locale] ?? $page->title['all'] ?? '';
                    @endphp
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-3 border-b">{{ $page->id }}</td>
                        <td class="px-6 py-3 border-b">
                            @if ($page->enable)
                                <span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">{{ __('Yes') }}</span>
                            @else
                                <span class="px-2 py-1 text-xs bg-red-100 text-red-700 rounded">{{ __('No') }}</span>
                            @endif
                        </td>
                        <td class="px-6 py-3 border-b text-center">
                            @if($page->content_type_id > 0)
                                <button class="main-toggle-btn" 
                                    data-page-id="{{ $page->id }}" 
                                    data-content-type-id="{{ $page->content_type_id }}" 
                                    title="{{ $page->is_main ? __('Main Page') : __('Set as Main') }}">
                                    @if($page->is_main)
                                        <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.49 6.91l6.564-.955L10 0l2.946 5.955 6.564.955-4.755 4.635 1.123 6.545z"/>
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5 text-gray-300 hover:text-yellow-500 transition" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.49 6.91l6.564-.955L10 0l2.946 5.955 6.564.955-4.755 4.635 1.123 6.545z"/>
                                        </svg>
                                    @endif
                                </button>
                            @endif
                        </td>
                        <td class="px-6 py-3 border-b">
                            

                            @if($page->content_type_id > 0)
                            <span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
                                {{ $contentTypes[$page->content_type_id]->name[$locale] ?? $contentTypes[$page->content_type_id]->name['en-us'] ?? '' }} - {{ ucfirst($page->content_type) }}
                            </span>
                            @else
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                                {{ ucfirst($page->content_type) }}
                            </span>
                            @endif
                        </td>
                        <td class="px-6 py-3 border-b">{{ $slug ?: '-' }}</td>
                        <td class="px-6 py-3 border-b">{{ $title ?: '-' }}</td>
                        <td class="px-6 py-3 border-b">{{ $page->created_at->format('Y-m-d') }}</td>
                        <td class="px-6 py-3 border-b">
                            <a href="{{ localized_route('admin.pages.edit', [$page->id]) }}" class="text-indigo-600 hover:underline">{{ __('Edit') }}</a>
                            |
                            <form action="{{ localized_route('admin.pages.delete', [$page->id]) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('{{ __('Are you sure?') }}')" class="text-red-600 hover:underline">
                                    {{ __('Delete') }}
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                            {{ __('No pages found.') }}
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.main-toggle-btn');

    buttons.forEach(btn => {
        btn.addEventListener('click', async function() {
            const pageId = this.dataset.pageId;
            const contentTypeId = this.dataset.contentTypeId;

            try {
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                const response = await fetch(`/${kazcms.locale}/admin/pages/set-main/${pageId}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                });

                const data = await response.json();

                if (data.success) {
                    // 清空同 content_type_id 的按钮
                    buttons.forEach(b => {
                        if (b.dataset.contentTypeId === contentTypeId) {
                            b.querySelector('svg').classList.remove('text-yellow-500');
                            b.querySelector('svg').classList.add('text-gray-300');
                        }
                    });

                    // 当前按钮高亮
                    this.querySelector('svg').classList.remove('text-gray-300');
                    this.querySelector('svg').classList.add('text-yellow-500');

                    alert(data.message);
                } else {
                    alert(data.message || 'Error');
                }
            } catch (err) {
                console.error(err);
                alert('Request failed.');
            }
        });
    });
});
</script>
@endpush
