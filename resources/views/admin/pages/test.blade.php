{{-- kazview:10 --}}

{{-- This is a data submission form.
     Use it when the user needs to submit or update data.
--}}
@php
    $locale = app()->getLocale();
@endphp

<div class="max-w-6xl mx-auto p-6 bg-white">

@php
  // Unique variables for this field
  $field = $fields->{'1-9'};

  $field_ids_1_9 = "1_9";
  $field_name_1_9 = "fields_1_9";

  $label_1_9 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Text Field';
  $placeholders_1_9 = json_decode($field->placeholder_json, true) ?? [];
  $help_texts_1_9 = json_decode($field->help_text_json, true) ?? [];
  $prefixes_1_9 = json_decode($field->prefix_text_json, true) ?? [];
  $suffixes_1_9 = json_decode($field->suffix_text_json, true) ?? [];

  $required_1_9 = $field->is_required ?? false;
  $min_1_9 = $field->min_length ?? null;
  $max_1_9 = $field->max_length ?? null;

  $only_show_locale_1_9 = $field->only_show_locale ?? true;
  $currentLang_1_9 = app()->getLocale();

  // Filter languages to show
  $enabledLanguagesFiltered_1_9 = $only_show_locale_1_9
    ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_1_9)->values()
    : collect($enabledLanguages);

  $showTabs_1_9 = $enabledLanguagesFiltered_1_9->count() > 1;
@endphp

<!-- Text Field Section -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
  <!-- Left Label -->
  <div class="lg:col-span-1 flex items-start pt-2">
    <label class="block text-lg font-semibold text-gray-900 leading-tight">
      {{ $label_1_9 }} @if($required_1_9)<span class="text-red-500 ml-1">*</span>@endif
    </label>
  </div>

  <!-- Right Input Area -->
  <div class="lg:col-span-3">

    @if($showTabs_1_9)
      <!-- Tabs for multiple languages -->
      <div class="border-b border-gray-200 mb-4">
        <nav class="flex -mb-px space-x-1" aria-label="Tabs" id="language-tabs-{{$field_ids_1_9}}" role="tablist">
          @foreach($enabledLanguagesFiltered_1_9 as $index => $lang)
            <button
              type="button"
              role="tab"
              aria-selected="{{ $loop->first ? 'true' : 'false' }}"
              aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_1_9}}"
              id="tab-{{ $lang->code }}-{{$field_ids_1_9}}"
              data-lang="{{ $lang->code }}"
              data-field="{{ $field_ids }}"
              class="px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                {{ $loop->first ? 'border-blue-600 text-blue-600 bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
              {{ strtoupper($lang->code) }}
            </button>
          @endforeach
        </nav>
      </div>
    @endif

    <!-- Input(s) -->
    @foreach($enabledLanguagesFiltered_1_9 as $index => $lang)
      @php
        $langCode = $lang->code;
        $placeholder = $placeholders_1_9[$langCode] ?? '';
        $help = $help_texts_1_9[$langCode] ?? '';
        $prefix = $prefixes_1_9[$langCode] ?? '';
        $suffix = $suffixes_1_9[$langCode] ?? '';
      @endphp

      <div
        role="tabpanel"
        id="tab-panel-{{ $langCode }}-{{$field_ids_1_9}}"
        aria-labelledby="tab-{{ $langCode }}-{{$field_ids_1_9}}"
        class="{{ (!$showTabs_1_9 || $loop->first) ? 'block' : 'hidden' }} tabpanel-{{$field_ids_1_9}}"
      >
        <div class="flex rounded-lg shadow-sm overflow-hidden">
          @if($prefix)
            <span class="inline-flex items-center px-4 py-3 bg-gray-50 border border-r-0 border-gray-300 text-gray-600 text-sm font-medium">
              {{ $prefix }}
            </span>
          @endif

          <input
            type="text"
            name="{{ $field_name_1_9 }}[{{ $langCode }}]"
            value="{{ old($field_name_1_9 . '.' . $langCode, $data[$field_name_1_9][$langCode] ?? '') }}"
            placeholder="{{ $placeholder }}"
            @if($required_1_9) required @endif
            @if($min_1_9) minlength="{{$min_1_9}}" @endif
            @if($max_1_9) maxlength="{{$max_1_9}}" @endif
            class="flex-1 px-4 py-3 border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200
                   {{ $prefix && $suffix ? 'border-x-0' : ($prefix ? 'border-l-0 rounded-r-lg' : ($suffix ? 'border-r-0 rounded-l-lg' : 'rounded-lg')) }}"
          />

          @if($suffix)
            <span class="inline-flex items-center px-4 py-3 bg-gray-50 border border-l-0 border-gray-300 text-gray-600 text-sm font-medium">
              {{ $suffix }}
            </span>
          @endif
        </div>

        @if($help)
          <p class="text-sm text-gray-600 mt-2 leading-relaxed">{{ $help }}</p>
        @endif
      </div>
    @endforeach

    @if($required_1_9 || $min_1_9 || $max_1_9)
      <div
        id="req_{{ $field_name_1_9 }}"
        class="need_require text-sm mt-2 text-red-600 font-medium"
        data-required="{{ $required_1_9 ? '1' : '0' }}"
        data-minlength="{{ $min_1_9 ?? '' }}"
        data-maxlength="{{ $max_1_9 ?? '' }}"
      ></div>
    @endif

  </div>
</div>

@php
    // Unique variables for this field
    $field = $fields->{'2-3'};

    $field_ids_2_3 = "2_3";
    $field_name_2_3 = "fields_2_3";

    $label_2_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';
    $help_texts_2_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_2_3 = $field->is_required ?? false;

    $only_show_locale_2_3 = $field->only_show_locale ?? true;
    $currentLang_2_3 = app()->getLocale();

    // Filter languages to show
    $enabledLanguagesFiltered_2_3 = $only_show_locale_2_3
        ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_2_3)->values()
        : collect($enabledLanguages);

    $showTabs_2_3 = $enabledLanguagesFiltered_2_3->count() > 1;
@endphp

<!-- Textarea Field Section -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
  <!-- Left Label -->
  <div class="lg:col-span-1 flex items-start pt-2">
    <label class="block text-lg font-semibold text-gray-900 leading-tight">
      {{ $label_2_3 }} @if($required_2_3)<span class="text-red-500 ml-1">*</span>@endif
    </label>
  </div>

  <!-- Right side textarea(s) -->
  <div class="lg:col-span-3">

    @if($showTabs_2_3)
      <div class="border-b border-gray-200 mb-4">
        <nav class="flex -mb-px space-x-1" aria-label="Tabs" id="language-tabs-{{$field_ids_2_3}}" role="tablist">
          @foreach($enabledLanguagesFiltered_2_3 as $index => $lang)
            <button
              type="button"
              role="tab"
              aria-selected="{{ $loop->first ? 'true' : 'false' }}"
              aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_2_3}}"
              id="tab-{{ $lang->code }}-{{$field_ids_2_3}}"
              data-lang="{{ $lang->code }}"
              data-field="{{ $field_ids }}"
              class="px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                {{ $loop->first ? 'border-blue-600 text-blue-600 bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
              {{ strtoupper($lang->code) }}
            </button>
          @endforeach
        </nav>
      </div>
    @endif

    @foreach($enabledLanguagesFiltered_2_3 as $index => $lang)
      @php
        $langCode = $lang->code;
        $help = $help_texts_2_3[$langCode] ?? '';
        $value = old($field_name_2_3 . '.' . $langCode, $data[$field_name_2_3][$langCode] ?? '');
      @endphp
      <div
        class="{{ (!$showTabs_2_3 || $loop->first) ? 'block' : 'hidden' }} tabpanel-{{$field_ids_2_3}}"
        aria-labelledby="tab-{{ $langCode }}-{{$field_ids_2_3}}"
        id="tab-panel-{{ $langCode }}-{{$field_ids_2_3}}"
      >
        <textarea
          id="textarea_\{$field_name_2_3\}_\{$langCode\}"
          name="{{ $field_name_2_3 }}[{{ $langCode }}]"
          rows="8"
          placeholder="{{ $help }}"
          class="kazcms-rich-textarea w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono resize-y transition-colors duration-200 bg-white"
          @if($required_2_3) required @endif
        >{{ $value }}</textarea>
        @error($field_name_2_3 . '.' . $langCode)
          <p class="mt-2 text-sm text-red-600 font-medium">{{ $message }}</p>
        @enderror
      </div>
    @endforeach

    @if($required_2_3)
      <div
        id="req_\{$field_name_2_3\}"
        class="need_require text-sm mt-2 text-red-600 font-medium"
        data-required="1"
      ></div>
    @endif

  </div>
</div>

<link rel="stylesheet" href="{{ asset('static/css/kazeditor.css') }}">
<script src="{{ asset('static/js/kazeditor.'.app()->getLocale().'.js') }}"></script>
<script src="{{ asset('static/js/kaz-editor.umd.js') }}?v=1.0.2"></script>
<script>
    // Optional KazEditor init
    // document.addEventListener('DOMContentLoaded', function () {
    //     const allButtons = Object.keys(window.KazEditorLang || {});
    //     window.KazEditor.KazEditor.init({
    //         lang: '{{ app()->getLocale() }}',
    //         target: 'textarea.kazcms-rich-textarea',
    //         autosync: true,
    //         toolbar: allButtons
    //     });
    // });
</script>

@php
    // Unique variables for this field
    $field = $fields->{'3-2'};

    $field_ids_3_2 = "3_2";
    $field_name_3_2 = "fields_3_2";

    $label_3_2 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'File Field';
    $help_texts_3_2 = json_decode($field->help_text_json ?? '{}', true);
    $required_3_2 = $field->is_required ?? false;

    $only_show_locale_3_2 = $field->only_show_locale ?? true;
    $currentLang_3_2 = app()->getLocale();

    // Filter languages to show
    $enabledLanguagesFiltered_3_2 = $only_show_locale_3_2
        ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_3_2)->values()
        : collect($enabledLanguages);

    $showTabs_3_2 = $enabledLanguagesFiltered_3_2->count() > 1;
    $acceptMime_3_2 = '.';
    $maxUploadCount_3_2 = 1;
@endphp

<!-- File Upload Field Section -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
  <!-- Left Label -->
  <div class="lg:col-span-1 flex items-start pt-2">
    <label class="block text-lg font-semibold text-gray-900 leading-tight">
      {{ $label_3_2 }} @if($required_3_2)<span class="text-red-500 ml-1">*</span>@endif
    </label>
  </div>

  <div class="lg:col-span-3">

    @if($showTabs_3_2)
      <div class="border-b border-gray-200 mb-4">
        <nav class="flex -mb-px space-x-1" aria-label="Tabs" id="language-tabs-{{$field_ids_3_2}}" role="tablist">
          @foreach($enabledLanguagesFiltered_3_2 as $index => $lang)
            <button
              type="button"
              role="tab"
              aria-selected="{{ $loop->first ? 'true' : 'false' }}"
              aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_3_2}}"
              id="tab-{{ $lang->code }}-{{$field_ids_3_2}}"
              data-lang="{{ $lang->code }}"
              data-field="{{ $field_ids }}"
              class="px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                {{ $loop->first ? 'border-blue-600 text-blue-600 bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
              {{ strtoupper($lang->code) }}
            </button>
          @endforeach
        </nav>
      </div>

      {{-- Checkbox for shared images --}}
      <div class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center space-x-3">
          <input
            type="checkbox"
            id="shared_{{ $field_name_3_2 }}"
            name="shared_{{ $field_name_3_2 }}"
            value="1"
            class="shared-sameimages h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            data-field-ids="{{ $field_ids_3_2 }}"
            data-primary="{{ $currentLang_3_2 }}"
            {{ old("shared_{$field_name_3_2}") ? 'checked' : '' }}
          >
          <label for="shared_{{ $field_name_3_2 }}" class="text-sm font-medium text-blue-800 cursor-pointer">
            Use the same files for all languages
          </label>
        </div>
      </div>
    @endif

    @foreach($enabledLanguagesFiltered_3_2 as $index => $lang)
      @php
        $langCode = $lang->code;
        $help = $help_texts_3_2[$langCode] ?? '';
        $existingImages = collect($data[$field_name_3_2][$langCode] ?? [])
          ->map(fn($id) => $data['attachments'][$id]->url ?? null)
          ->filter()
          ->values()
          ->all();
        $existingImagesValue = old("existing_images_\{$field_name_3_2\}.\{$langCode\}", json_encode($existingImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $panelClass = $showTabs_3_2 ? ($loop->first ? 'block' : 'hidden') : 'block';
      @endphp

      <div class="code-tab-content {{ $panelClass }} tabpanel-{{$field_ids_3_2}}" id="tab-panel-{{ $langCode }}-{{$field_ids_3_2}}" aria-labelledby="tab-{{ $langCode }}-{{$field_ids_3_2}}">
        <div class="space-y-4">
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200">
            <input
              type="file"
              class="kaz-file-input hidden"
              data-preview="PreviewContainer_{{ $field_name_3_2 }}_{{ $langCode }}"
              name="{{ $field_name_3_2 }}[{{ $langCode }}]"
              id="{{ $field_name_3_2 }}_{{ $langCode }}"
              {{ $acceptMime_3_2 ? "accept=\{$acceptMime_3_2\}" : '' }}
              {{ $maxUploadCount_3_2 > 1 ? 'multiple' : '' }}
              data-target-existing="#existingImages_{{ $field_name_3_2 }}_{{ $langCode }}"
            >
            <label for="{{ $field_name_3_2 }}_{{ $langCode }}" class="cursor-pointer">
              <div class="space-y-2">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="text-sm text-gray-600">
                  <span class="font-medium text-blue-600 hover:text-blue-500">Click to upload</span> or drag and drop
                </div>
                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
              </div>
            </label>
          </div>

          <input
            type="hidden"
            id="existingImages_{{ $field_name_3_2 }}_{{ $langCode }}"
            name="existing_images_{{ $field_name_3_2 }}[{{ $langCode }}]"
            value='{{ $existingImagesValue }}'
          >

          <input
            type="hidden"
            name="image_order_{{ $field_name_3_2 }}[{{ $langCode }}]"
            value=""
          >

          @if($help)
            <p class="text-sm text-gray-600 leading-relaxed">{{ $help }}</p>
          @endif

          @if($required_3_2)
            <div
              id="req_{{ $field_name_3_2 }}"
              class="need_require text-sm text-red-600 font-medium"
              data-required="1"
              data-maxupload="{{ $maxUploadCount_3_2 }}"
            ></div>
          @endif

          @error($field_name_3_2 . '.' . $langCode)
            <p class="text-sm text-red-600 font-medium">{{ $message }}</p>
          @enderror
        </div>
      </div>
    @endforeach

  </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    const toggleGroupInheritance = (checkbox) => {
        const fieldIds = checkbox.dataset.fieldIds;
        const primaryLang = checkbox.dataset.primary;
        const isChecked = checkbox.checked;
        document.querySelectorAll(`.tabpanel-${fieldIds}`).forEach(panel => {
            const isPrimary = panel.id.endsWith(`${primaryLang}-${fieldIds}`);
            if (isPrimary) return;
            panel.classList.toggle('kaz-inherited', isChecked);
        });
        document.querySelectorAll(`#language-tabs button[data-field="${fieldIds}"]`).forEach(tab => {
            const isPrimary = tab.dataset.lang === primaryLang;
            if (isPrimary) return;
            tab.classList.toggle('tab-inherited', isChecked);
        });
    };
    document.querySelectorAll('input.shared-sameimages').forEach(checkbox => {
        toggleGroupInheritance(checkbox);
        checkbox.addEventListener('change', () => toggleGroupInheritance(checkbox));
    });
});
</script>
@endpush

@php
    // Unique variables for this field
    $field = $fields->{'4-3'};

    $field_ids_4_3 = "4_3";
    $field_name_4_3 = "fields_4_3";

    $label_4_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Category Field';
    $help_texts_4_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_4_3 = $field->is_required ?? false;
    $maxSelectCount_4_3 = $field->max_select_count ?? null;

    // Selected categories
    $selectedCategories_4_3 = $data[$field_name_4_3]['all'] ?? [];
@endphp

<!-- Category Selection Field Section -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
  <!-- Left Label -->
  <div class="lg:col-span-1 flex items-start pt-2">
    <label class="block text-lg font-semibold text-gray-900 leading-tight">
      {{ $label_4_3 }} @if($required_4_3)<span class="text-red-500 ml-1">*</span>@endif
    </label>
  </div>

  <div class="lg:col-span-3">
    <div class="bg-white rounded-lg border border-gray-300 shadow-sm overflow-hidden">
      <div class="flex items-center justify-between p-4">
        <button
          type="button"
          id="categorySelectButton_{{ $field_ids_4_3 }}"
          data-categoryid="{{ $field->category_id }}"
          data-displaystyle="{{ $field->display_style }}"
          class="category-select-button inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          选择分类
        </button>
      </div>

      <div id="selectedCategories_{{ $field_ids_4_3 }}" class="{{ empty($selectedCategories_4_3) ? 'hidden' : 'px-4 pb-4' }}">
        <div class="flex flex-wrap gap-2">
          @if(isset($selectedCategories_4_3) && is_array($selectedCategories_4_3))
            @foreach($selectedCategories_4_3 as $cat_id)
              <span class="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full border border-blue-200">
                {{ $data['categories'][$cat_id]['name'][app()->getLocale()] ?? 'Untitled Category' }}
                <button type="button" class="ml-2 w-5 h-5 flex items-center justify-center text-blue-600 hover:text-red-600 hover:bg-red-100 rounded-full transition-colors duration-200 kazcms-remove-category" data-inputid="field_{{ $field_ids_4_3 }}" data-categoryid="{{ $cat_id }}">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </span>
            @endforeach
          @endif
        </div>
      </div>
    </div>

    <input type="hidden" id="field_{{ $field_ids_4_3 }}" name="{{ $field_name_4_3 }}" value="{{ implode(',', $selectedCategories_4_3) }}">

    @if($required_4_3 || $maxSelectCount_4_3)
      <div
        id="req_{{ $field_name_4_3 }}"
        class="need_require text-sm mt-2 text-red-600 font-medium"
        data-required="{{ $required_4_3 ? '1' : '0' }}"
        data-maxselect="{{ $maxSelectCount_4_3 ?? '' }}"
      ></div>
    @endif

    @if($help_texts_4_3[app()->getLocale()] ?? false)
      <p class="mt-2 text-sm text-gray-600 leading-relaxed">{{ $help_texts_4_3[app()->getLocale()] }}</p>
    @endif

  </div>
</div>

@php
    // Unique variables for this field
    $field = $fields->{'5-3'};

    $field_ids_5_3 = "5_3";
    $field_name_5_3 = "fields_5_3";

    $label_5_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Options Field';
    $help_texts_5_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_5_3 = $field->is_required ?? false;

    $style_5_3 = $field->display_style;
    $isMultiple_5_3 = in_array($style_5_3, ['checkbox']);
    $maxSelectCount_5_3 = $field->max_select_count ?? 0;
    $prefixText_5_3 = json_decode($field->prefix_text_json, true)[app()->getLocale()] ?? '';
    $suffixText_5_3 = json_decode($field->suffix_text_json, true)[app()->getLocale()] ?? '';

    // Get stored value
    $dataValue_5_3 = $data[$field_name_5_3]['all'] ?? [];
    $selected_5_3 = old($field_name_5_3, $dataValue_5_3);
    $selected_5_3 = is_array($selected_5_3) ? $selected_5_3 : [$selected_5_3];

    // Filter and sort options
    $options_5_3 = collect(json_decode($field->options, true) ?? [])
        ->filter(fn($opt) => $opt['enabled'] ?? false)
        ->sortBy('sort_order');
@endphp

<!-- Options Field Section -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200 option-group"
     data-field-id="{{ $field_ids_5_3 }}"
     data-max-select="{{ $maxSelectCount_5_3 }}"
     data-multiple="{{ $isMultiple_5_3 ? 'true' : 'false' }}"
>
  <!-- Left Label -->
  <div class="lg:col-span-1 flex items-start pt-2">
    @if($label_5_3)
      <label class="block text-lg font-semibold text-gray-900 leading-tight">
        {{ $label_5_3 }}
        @if($required_5_3)
          <span class="text-red-500 ml-1">*</span>
        @endif
      </label>
    @endif
  </div>

  <div class="lg:col-span-3">
    @if($isMultiple_5_3 && $maxSelectCount_5_3 > 0)
      <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <p class="text-sm text-blue-800 font-medium">
          最多可选 {{ $maxSelectCount_5_3 }} 项
        </p>
      </div>
    @endif

    @if($prefixText_5_3)
      <p class="text-sm text-gray-600 mb-4 leading-relaxed">{{ $prefixText_5_3 }}</p>
    @endif

    @if(in_array($style_5_3, ['select', 'multi-select']))
      <div class="bg-white rounded-lg border border-gray-300 shadow-sm">
        <select name="{{ $field_name_5_3 }}{{ $isMultiple_5_3 ? '[]' : '' }}"
                id="field_select_{{ $field_ids_5_3 }}"
                class="block w-full px-4 py-3 border-0 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                {{ $isMultiple_5_3 ? 'multiple' : '' }}>
          @foreach($options_5_3 as $option)
            @php
              $optVal = $option['value'] ?? '';
              $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
              $selectedAttr = in_array($optVal, $selected_5_3) ? 'selected' : '';
            @endphp
            <option value="{{ $optVal }}" {{ $selectedAttr }}>{{ $optLabel }}</option>
          @endforeach
        </select>
      </div>
    @else
      <div class="bg-white rounded-lg border border-gray-300 shadow-sm p-4">
        <div class="space-y-4">
          @foreach($options_5_3 as $index => $option)
            @php
              $optVal = $option['value'] ?? '';
              $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
              $checked = in_array($optVal, $selected_5_3) ? 'checked' : '';
              $type = $isMultiple_5_3 ? 'checkbox' : 'radio';
            @endphp
            <div class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              <input type="{{ $type }}"
                     name="{{ $field_name_5_3 }}{{ $isMultiple_5_3 ? '[]' : '' }}"
                     value="{{ $optVal }}"
                     id="field_{{ $field_ids_5_3 }}_{{ $loop->index }}"
                     class="option-input h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                     {{ $checked }}
                     >
              <label for="field_{{ $field_ids_5_3 }}_{{ $loop->index }}"
                     class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer select-none">
                {{ $optLabel }}
              </label>
            </div>
          @endforeach
        </div>
      </div>
    @endif

    @if($suffixText_5_3)
      <p class="text-sm text-gray-600 mt-4 leading-relaxed">{{ $suffixText_5_3 }}</p>
    @endif

    @if($required_5_3 || ($isMultiple_5_3 && $maxSelectCount_5_3 > 0))
      <div
        id="req_{{ $field_name_5_3 }}"
        class="need_require text-sm mt-2 text-red-600 font-medium"
        data-required="{{ $required_5_3 ? '1' : '0' }}"
        data-maxselect="{{ $isMultiple_5_3 ? $maxSelectCount_5_3 : '' }}"
      ></div>
    @endif

    @error($field_name_5_3)
      <p class="text-sm text-red-600 mt-2 font-medium">{{ $message }}</p>
    @enderror

  </div>
</div>

</div> <!-- Close main container -->