{{-- kazview:10 --}} 

{{-- This is a data submission form. 
     Use it when the user needs to submit or update data. 
--}}
@php
    $locale = app()->getLocale();
@endphp

@php
  // Unique variables for this field
  $field = $fields->{'1-9'};

  $field_ids_1_9 = "1_9";
  $field_name_1_9 = "fields_1_9";

  $label_1_9 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Text Field';
  $placeholders_1_9 = json_decode($field->placeholder_json, true) ?? [];
  $help_texts_1_9 = json_decode($field->help_text_json, true) ?? [];
  $prefixes_1_9 = json_decode($field->prefix_text_json, true) ?? [];
  $suffixes_1_9 = json_decode($field->suffix_text_json, true) ?? [];

  $required_1_9 = $field->is_required ?? false;
  $min_1_9 = $field->min_length ?? null;
  $max_1_9 = $field->max_length ?? null;

  $only_show_locale_1_9 = $field->only_show_locale ?? true;
  $currentLang_1_9 = app()->getLocale();

  // Filter languages to show
  $enabledLanguagesFiltered_1_9 = $only_show_locale_1_9
    ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_1_9)->values()
    : collect($enabledLanguages);

  $showTabs_1_9 = $enabledLanguagesFiltered_1_9->count() > 1;
@endphp

<!-- Left Label -->
<div class="w-48 flex items-start pt-3">
  <label class="block text-lg font-semibold text-gray-900">
    {{ $label_1_9 }} @if($required_1_9)<span class="text-red-500 ml-1">*</span>@endif
  </label>
</div>

<!-- Right Input Area -->
<div class="flex-1">

  @if($showTabs_1_9)
    <!-- Tabs for multiple languages -->
    <div class="border-b border-gray-200">
      <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{$field_ids_1_9}}" role="tablist">
        @foreach($enabledLanguagesFiltered_1_9 as $index => $lang)
          <button
            type="button"
            role="tab"
            aria-selected="{{ $loop->first ? 'true' : 'false' }}"
            aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_1_9}}"
            id="tab-{{ $lang->code }}-{{$field_ids_1_9}}"
            data-lang="{{ $lang->code }}"
            data-field="{{ $field_ids }}"
            class="py-2 px-4 text-sm font-medium rounded-t-lg
              focus:outline-none
              {{ $loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
            {{ strtoupper($lang->code) }}
          </button>
        @endforeach
      </nav>
    </div>
  @endif

  <!-- Input(s) -->
  @foreach($enabledLanguagesFiltered_1_9 as $index => $lang)
    @php
      $langCode = $lang->code;
      $placeholder = $placeholders_1_9[$langCode] ?? '';
      $help = $help_texts_1_9[$langCode] ?? '';
      $prefix = $prefixes_1_9[$langCode] ?? '';
      $suffix = $suffixes_1_9[$langCode] ?? '';
    @endphp

    <div
      role="tabpanel"
      id="tab-panel-{{ $langCode }}-{{$field_ids_1_9}}"
      aria-labelledby="tab-{{ $langCode }}-{{$field_ids_1_9}}"
      class="{{ (!$showTabs_1_9 || $loop->first) ? 'block' : 'hidden' }} pt-4 tabpanel-{{$field_ids_1_9}}"
    >
      <div class="flex rounded-md shadow-sm">
        @if($prefix)
          <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
            {{ $prefix }}
          </span>
        @endif

        <input
          type="text"
          name="{{ $field_name_1_9 }}[{{ $langCode }}]"
          value="{{ old($field_name_1_9 . '.' . $langCode, $data[$field_name_1_9][$langCode] ?? '') }}"
          placeholder="{{ $placeholder }}"
          @if($required_1_9) required @endif
          @if($min_1_9) minlength="{{$min_1_9}}" @endif
          @if($max_1_9) maxlength="{{$max_1_9}}" @endif
          class="block w-full px-3 py-2 border border-gray-300
                 {{ $prefix ? 'rounded-none rounded-r-md' : 'rounded-md' }}
                 {{ $suffix ? 'border-r-0' : '' }}
                 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
        />

        @if($suffix)
          <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
            {{ $suffix }}
          </span>
        @endif
      </div>

      @if($help)
        <p class="text-xs text-gray-500 mt-1">{{ $help }}</p>
      @endif
    </div>
  @endforeach

  @if($required_1_9 || $min_1_9 || $max_1_9)
    <div
      id="req_{{ $field_name_1_9 }}"
      class="need_require text-sm mt-1 text-red-600"
      data-required="{{ $required_1_9 ? '1' : '0' }}"
      data-minlength="{{ $min_1_9 ?? '' }}"
      data-maxlength="{{ $max_1_9 ?? '' }}"
    ></div>
  @endif

</div>

@php
    // Unique variables for this field
    $field = $fields->{'2-3'};

    $field_ids_2_3 = "2_3";
    $field_name_2_3 = "fields_2_3";

    $label_2_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';
    $help_texts_2_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_2_3 = $field->is_required ?? false;

    $only_show_locale_2_3 = $field->only_show_locale ?? true;
    $currentLang_2_3 = app()->getLocale();

    // Filter languages to show
    $enabledLanguagesFiltered_2_3 = $only_show_locale_2_3
        ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_2_3)->values()
        : collect($enabledLanguages);

    $showTabs_2_3 = $enabledLanguagesFiltered_2_3->count() > 1;
@endphp

<!-- Left Label -->
<div class="w-48 flex items-start pt-3">
    <label class="block text-lg font-semibold text-gray-900">
        {{ $label_2_3 }} @if($required_2_3)<span class="text-red-500 ml-1">*</span>@endif
    </label>
</div>

<!-- Right side textarea(s) -->
<div class="flex-1">

    @if($showTabs_2_3)
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{$field_ids_2_3}}" role="tablist">
                @foreach($enabledLanguagesFiltered_2_3 as $index => $lang)
                    <button
                        type="button"
                        role="tab"
                        aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                        aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_2_3}}"
                        id="tab-{{ $lang->code }}-{{$field_ids_2_3}}"
                        data-lang="{{ $lang->code }}"
                        data-field="{{ $field_ids }}"
                        class="py-2 px-4 text-sm font-medium rounded-t-lg
                            focus:outline-none
                            {{ $loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </nav>
        </div>
    @endif

    @foreach($enabledLanguagesFiltered_2_3 as $index => $lang)
        @php
            $langCode = $lang->code;
            $help = $help_texts_2_3[$langCode] ?? '';
            $value = old($field_name_2_3 . '.' . $langCode, $data[$field_name_2_3][$langCode] ?? '');
        @endphp
        <div 
            class="{{ (!$showTabs_2_3 || $loop->first) ? 'block' : 'hidden' }} tabpanel-{{$field_ids_2_3}}"
            aria-labelledby="tab-{{ $langCode }}-{{$field_ids_2_3}}"
            id="tab-panel-{{ $langCode }}-{{$field_ids_2_3}}"
        >
            <textarea
                id="textarea_\{$field_name_2_3\}_\{$langCode\}"
                name="{{ $field_name_2_3 }}[{{ $langCode }}]"
                rows="6"
                placeholder="{{ $help }}"
                class="kazcms-rich-textarea w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono resize-y"
                @if($required_2_3) required @endif
            >{{ $value }}</textarea>
            @error($field_name_2_3 . '.' . $langCode)
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>
    @endforeach

    @if($required_2_3)
        <div
            id="req_\{$field_name_2_3\}"
            class="need_require text-sm mt-1 text-red-600"
            data-required="1"
        ></div>
    @endif
</div>

<link rel="stylesheet" href="{{ asset('static/css/kazeditor.css') }}">
<script src="{{ asset('static/js/kazeditor.'.app()->getLocale().'.js') }}"></script>
<script src="{{ asset('static/js/kaz-editor.umd.js') }}?v=1.0.2"></script>
<script>
    // Optional KazEditor init
    // document.addEventListener('DOMContentLoaded', function () {
    //     const allButtons = Object.keys(window.KazEditorLang || {});
    //     window.KazEditor.KazEditor.init({
    //         lang: '{{ app()->getLocale() }}',
    //         target: 'textarea.kazcms-rich-textarea',
    //         autosync: true,
    //         toolbar: allButtons
    //     });
    // });
</script>

@php
    // Unique variables for this field
    $field = $fields->{'3-2'};

    $field_ids_3_2 = "3_2";
    $field_name_3_2 = "fields_3_2";

    $label_3_2 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'File Field';
    $help_texts_3_2 = json_decode($field->help_text_json ?? '{}', true);
    $required_3_2 = $field->is_required ?? false;

    $only_show_locale_3_2 = $field->only_show_locale ?? true;
    $currentLang_3_2 = app()->getLocale();

    // Filter languages to show
    $enabledLanguagesFiltered_3_2 = $only_show_locale_3_2
        ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang_3_2)->values()
        : collect($enabledLanguages);

    $showTabs_3_2 = $enabledLanguagesFiltered_3_2->count() > 1;
    $acceptMime_3_2 = '.';
    $maxUploadCount_3_2 = 1;
@endphp

<div class="w-48 flex items-start pt-3">
    <label class="block text-lg font-semibold text-gray-900">
        {{ $label_3_2 }} @if($required_3_2)<span class="text-red-500 ml-1">*</span>@endif
    </label>
</div>

<div class="flex-1">

    @if($showTabs_3_2)
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs-{{$field_ids_3_2}}" role="tablist">
                @foreach($enabledLanguagesFiltered_3_2 as $index => $lang)
                    <button
                        type="button"
                        role="tab"
                        aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                        aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids_3_2}}"
                        id="tab-{{ $lang->code }}-{{$field_ids_3_2}}"
                        data-lang="{{ $lang->code }}"
                        data-field="{{ $field_ids }}"
                        class="py-2 px-4 text-sm font-medium rounded-t-lg
                            focus:outline-none
                            {{ $loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </nav>
        </div>

        {{-- Checkbox for shared images --}}
        <div class="mt-4 flex items-center space-x-2">
            <input 
                type="checkbox"
                id="shared_{{ $field_name_3_2 }}"
                name="shared_{{ $field_name_3_2 }}"
                value="1"
                class="shared-sameimages"
                data-field-ids="{{ $field_ids_3_2 }}"
                data-primary="{{ $currentLang_3_2 }}"
                {{ old("shared_{$field_name_3_2}") ? 'checked' : '' }}
            >
            <label for="shared_{{ $field_name_3_2 }}" class="text-sm text-gray-700">
                Use the same files for all languages
            </label>
        </div>
    @endif

    @foreach($enabledLanguagesFiltered_3_2 as $index => $lang)
        @php
            $langCode = $lang->code;
            $help = $help_texts_3_2[$langCode] ?? '';
            $existingImages = collect($data[$field_name_3_2][$langCode] ?? [])
                ->map(fn($id) => $data['attachments'][$id]->url ?? null)
                ->filter()
                ->values()
                ->all();
            $existingImagesValue = old("existing_images_\{$field_name_3_2\}.\{$langCode\}", json_encode($existingImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            $panelClass = $showTabs_3_2 ? ($loop->first ? 'block' : 'hidden') : 'block';
        @endphp

        <div class="code-tab-content {{ $panelClass }} tabpanel-{{$field_ids_3_2}}" id="tab-panel-{{ $langCode }}-{{$field_ids_3_2}}" aria-labelledby="tab-{{ $langCode }}-{{$field_ids_3_2}}">
            <input
                type="file"
                class="kaz-file-input"
                data-preview="PreviewContainer_{{ $field_name_3_2 }}_{{ $langCode }}"
                name="{{ $field_name_3_2 }}[{{ $langCode }}]"
                id="{{ $field_name_3_2 }}_{{ $langCode }}"
                {{ $acceptMime_3_2 ? "accept=\{$acceptMime_3_2\}" : '' }}
                {{ $maxUploadCount_3_2 > 1 ? 'multiple' : '' }}
                data-target-existing="#existingImages_{{ $field_name_3_2 }}_{{ $langCode }}"
            >

            <input
                type="text"
                id="existingImages_{{ $field_name_3_2 }}_{{ $langCode }}"
                name="existing_images_{{ $field_name_3_2 }}[{{ $langCode }}]"
                value='{{ $existingImagesValue }}'
            >

            <input 
                type="text" 
                name="image_order_{{ $field_name_3_2 }}[{{ $langCode }}]" 
                value="" 
            >

            @if($help)
                <p class="mt-1 text-sm text-gray-500">{{ $help }}</p>
            @endif

            @if($required_3_2)
                <div
                    id="req_{{ $field_name_3_2 }}"
                    class="need_require text-sm mt-1 text-red-600"
                    data-required="1"
                    data-maxupload="{{ $maxUploadCount_3_2 }}"
                ></div>
            @endif

            @error($field_name_3_2 . '.' . $langCode)
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>
    @endforeach
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    const toggleGroupInheritance = (checkbox) => {
        const fieldIds = checkbox.dataset.fieldIds;
        const primaryLang = checkbox.dataset.primary;
        const isChecked = checkbox.checked;
        document.querySelectorAll(`.tabpanel-${fieldIds}`).forEach(panel => {
            const isPrimary = panel.id.endsWith(\`${primaryLang}-${fieldIds}\`);
            if (isPrimary) return;
            panel.classList.toggle('kaz-inherited', isChecked);
        });
        document.querySelectorAll(`#language-tabs button[data-field="${fieldIds}"]`).forEach(tab => {
            const isPrimary = tab.dataset.lang === primaryLang;
            if (isPrimary) return;
            tab.classList.toggle('tab-inherited', isChecked);
        });
    };
    document.querySelectorAll('input.shared-sameimages').forEach(checkbox => {
        toggleGroupInheritance(checkbox);
        checkbox.addEventListener('change', () => toggleGroupInheritance(checkbox));
    });
});
</script>
@endpush

@php
    // Unique variables for this field
    $field = $fields->{'4-3'};

    $field_ids_4_3 = "4_3";
    $field_name_4_3 = "fields_4_3";

    $label_4_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Category Field';
    $help_texts_4_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_4_3 = $field->is_required ?? false;
    $maxSelectCount_4_3 = $field->max_select_count ?? null;

    // Selected categories
    $selectedCategories_4_3 = $data[$field_name_4_3]['all'] ?? [];
@endphp

<div class="w-48 flex items-start pt-3">
    <label class="block text-lg font-semibold text-gray-900">
        {{ $label_4_3 }} @if($required_4_3)<span class="text-red-500 ml-1">*</span>@endif
    </label>
</div>

<div class="flex-1">
    <div class="relative flex items-center justify-between w-full px-4 py-3 bg-white transition-all duration-200">
        <span
            id="categorySelectButton_{{ $field_ids_4_3 }}"
            data-categoryid="{{ $field->category_id }}"
            data-displaystyle="{{ $field->display_style }}"
            class="category-select-button cursor-pointer text-gray-700 font-medium hover:text-blue-700 transition-colors duration-200 select-none"
        >
            选择分类
        </span>

        <div id="selectedCategories_{{ $field_ids_4_3 }}" class="{{ empty($selectedCategories_4_3) ? 'hidden' : 'flex-1 ml-4 text-lg text-gray-600 truncate' }}">
            @if(isset($selectedCategories_4_3) && is_array($selectedCategories_4_3))
                @foreach($selectedCategories_4_3 as $cat_id)
                    <span class="inline-flex items-center px-3 py-1 mr-2 mb-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                        {{ $data['categories'][$cat_id]['name'][app()->getLocale()] ?? 'Untitled Category' }}
                        <button class="ml-2 w-4 h-4 text-blue-600 hover:text-red-600 kazcms-remove-category" data-inputid="field_{{ $field_ids_4_3 }}" data-categoryid="{{ $cat_id }}">×</button>
                    </span>
                @endforeach
            @endif
        </div>

        <input type="text" id="field_{{ $field_ids_4_3 }}" name="{{ $field_name_4_3 }}" value="{{ implode(',', $selectedCategories_4_3) }}">
    </div>

    @if($required_4_3 || $maxSelectCount_4_3)
        <div
            id="req_{{ $field_name_4_3 }}"
            class="need_require text-sm mt-1 text-red-600"
            data-required="{{ $required_4_3 ? '1' : '0' }}"
            data-maxselect="{{ $maxSelectCount_4_3 ?? '' }}"
        ></div>
    @endif

    @if($help_texts_4_3[app()->getLocale()] ?? false)
        <p class="mt-1 text-sm text-gray-500">{{ $help_texts_4_3[app()->getLocale()] }}</p>
    @endif
</div>

@php
    // Unique variables for this field
    $field = $fields->{'5-3'};

    $field_ids_5_3 = "5_3";
    $field_name_5_3 = "fields_5_3";

    $label_5_3 = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Options Field';
    $help_texts_5_3 = json_decode($field->help_text_json ?? '{}', true);
    $required_5_3 = $field->is_required ?? false;

    $style_5_3 = $field->display_style;
    $isMultiple_5_3 = in_array($style_5_3, ['checkbox']);
    $maxSelectCount_5_3 = $field->max_select_count ?? 0;
    $prefixText_5_3 = json_decode($field->prefix_text_json, true)[app()->getLocale()] ?? '';
    $suffixText_5_3 = json_decode($field->suffix_text_json, true)[app()->getLocale()] ?? '';

    // Get stored value
    $dataValue_5_3 = $data[$field_name_5_3]['all'] ?? [];
    $selected_5_3 = old($field_name_5_3, $dataValue_5_3);
    $selected_5_3 = is_array($selected_5_3) ? $selected_5_3 : [$selected_5_3];

    // Filter and sort options
    $options_5_3 = collect(json_decode($field->options, true) ?? [])
        ->filter(fn($opt) => $opt['enabled'] ?? false)
        ->sortBy('sort_order');
@endphp

<div class="mb-6 option-group"
     data-field-id="{{ $field_ids_5_3 }}"
     data-max-select="{{ $maxSelectCount_5_3 }}"
     data-multiple="{{ $isMultiple_5_3 ? 'true' : 'false' }}"
>
    @if($label_5_3)
        <label class="block text-lg font-semibold text-gray-900 mb-2">
            {{ $label_5_3 }}
            @if($required_5_3)
                <span class="text-red-600">*</span>
            @endif
        </label>
    @endif

    @if($isMultiple_5_3 && $maxSelectCount_5_3 > 0)
        <p class="text-xs text-gray-500 mb-3">
            最多可选 {{ $maxSelectCount_5_3 }} 项
        </p>
    @endif

    @if($prefixText_5_3)
        <p class="text-sm text-gray-600 mb-2">{{ $prefixText_5_3 }}</p>
    @endif

    @if(in_array($style_5_3, ['select', 'multi-select']))
        <select name="{{ $field_name_5_3 }}{{ $isMultiple_5_3 ? '[]' : '' }}"
                id="field_select_{{ $field_ids_5_3 }}"
                class="block w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500"
                {{ $isMultiple_5_3 ? 'multiple' : '' }}>
            @foreach($options_5_3 as $option)
                @php
                    $optVal = $option['value'] ?? '';
                    $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
                    $selectedAttr = in_array($optVal, $selected_5_3) ? 'selected' : '';
                @endphp
                <option value="{{ $optVal }}" {{ $selectedAttr }}>{{ $optLabel }}</option>
            @endforeach
        </select>
    @else
        <div class="space-y-3">
            @foreach($options_5_3 as $index => $option)
                @php
                    $optVal = $option['value'] ?? '';
                    $optLabel = $option['label'][app()->getLocale()] ?? $optVal;
                    $checked = in_array($optVal, $selected_5_3) ? 'checked' : '';
                    $type = $isMultiple_5_3 ? 'checkbox' : 'radio';
                @endphp
                <div class="flex items-center">
                    <input type="{{ $type }}"
                           name="{{ $field_name_5_3 }}{{ $isMultiple_5_3 ? '[]' : '' }}"
                           value="{{ $optVal }}"
                           id="field_{{ $field_ids_5_3 }}_{{ $loop->index }}"
                           class="option-input h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                           {{ $checked }}
                           >
                    <label for="field_{{ $field_ids_5_3 }}_{{ $loop->index }}"
                           class="ml-2 block text-sm text-gray-700 select-none">
                        {{ $optLabel }}
                    </label>
                </div>
            @endforeach
        </div>
    @endif

    @if($suffixText_5_3)
        <p class="text-sm text-gray-600 mt-3">{{ $suffixText_5_3 }}</p>
    @endif

    @if($required_5_3 || ($isMultiple_5_3 && $maxSelectCount_5_3 > 0))
        <div
          id="req_{{ $field_name_5_3 }}"
          class="need_require text-sm mt-1 text-red-600"
          data-required="{{ $required_5_3 ? '1' : '0' }}"
          data-maxselect="{{ $isMultiple_5_3 ? $maxSelectCount_5_3 : '' }}"
        ></div>
    @endif

    @error($field_name_5_3)
        <p class="text-sm text-red-600 mt-2">{{ $message }}</p>
    @enderror
</div>