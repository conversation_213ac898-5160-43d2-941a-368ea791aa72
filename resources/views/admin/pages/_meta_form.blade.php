<div x-data="{ showMeta: false, useSameMeta: {{ old('meta_all_languages', $page->meta_all_languages ?? false) ? 'true' : 'false' }}, activeLang: '{{ app()->getLocale() }}' }" class="space-y-6">

    <!-- Toggle Button -->
    <div class="flex justify-center">
        <button type="button" @click="showMeta = !showMeta"
                class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
            <svg x-show="!showMeta" class="w-5 h-5 mr-2 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <svg x-show="showMeta" class="w-5 h-5 mr-2 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
            </svg>
            <span x-show="!showMeta" class="transition-all duration-300">Add Meta Information</span>
            <span x-show="showMeta" class="transition-all duration-300">Hide Meta Information</span>
        </button>
    </div>

    <!-- Meta Form -->
    <div x-show="showMeta" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white border border-gray-200 rounded-2xl p-8 shadow-lg">
        
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                SEO & Meta Configuration
            </h3>
            <div class="flex items-center space-x-3">
                <input type="checkbox" name="meta_all_languages" value="1" x-model="useSameMeta"
                       class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                <label class="text-sm font-medium text-gray-600 cursor-pointer">
                    Use same Meta for all languages
                </label>
            </div>
        </div>

        <!-- If useSameMeta = true, only show default language -->
        <template x-if="useSameMeta">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Title</label>
                    <input type="text" name="meta[title]" value="{{ old('meta.title', $page->meta['title'] ?? '') }}"
                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           placeholder="Enter meta title for SEO">
                </div>
                
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Keywords</label>
                    <input type="text" name="meta[keywords]" value="{{ old('meta.keywords', $page->meta['keywords'] ?? '') }}"
                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           placeholder="keyword1, keyword2, keyword3">
                </div>
                
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Robots</label>
                    <input type="text" name="meta[robots]" value="{{ old('meta.robots', $page->meta['robots'] ?? '') }}"
                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                           placeholder="index,follow">
                </div>
                
                <div class="lg:col-span-2">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Description</label>
                    <textarea name="meta[description]" rows="3" 
                              class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                              placeholder="Brief description for search engines (150-160 characters)">{{ old('meta.description', $page->meta['description'] ?? '') }}</textarea>
                </div>

                <!-- Open Graph Section -->
                <div class="lg:col-span-2 pt-6 border-t border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        Open Graph (Social Sharing)
                    </h4>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">OG Title</label>
                            <input type="text" name="meta[og_title]" value="{{ old('meta.og_title', $page->meta['og_title'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="Title for social media sharing">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">OG Image URL</label>
                            <input type="text" name="meta[og_image]" value="{{ old('meta.og_image', $page->meta['og_image'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="https://example.com/image.jpg">
                        </div>
                        
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">OG Description</label>
                            <textarea name="meta[og_description]" rows="2" 
                                      class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                                      placeholder="Description for social media sharing">{{ old('meta.og_description', $page->meta['og_description'] ?? '') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Custom CSS Class</label>
                    <input type="text" name="meta[custom_css_class]" value="{{ old('meta.custom_css_class', $page->meta['custom_css_class'] ?? '') }}"
                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           placeholder="custom-page-class additional-styles">
                </div>
            </div>
        </template>

        <!-- If useSameMeta = false, show per-language Tabs -->
        <template x-if="!useSameMeta">
            <div>
                <!-- Tabs -->
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl mb-6">
                    @foreach ($languages as $lang)
                        <button type="button"
                                @click="activeLang = '{{ $lang }}'"
                                :class="activeLang === '{{ $lang }}' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'"
                                class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200">
                            {{ strtoupper($lang) }}
                        </button>
                    @endforeach
                </div>

                <!-- Language-specific fields -->
                @foreach($languages as $lang)
                    <div x-show="activeLang === '{{ $lang }}'" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform translate-x-4"
                         x-transition:enter-end="opacity-100 transform translate-x-0"
                         class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Title ({{ strtoupper($lang) }})</label>
                            <input type="text" name="meta[{{ $lang }}][title]"
                                   value="{{ old('meta.'.$lang.'.title', $page->meta[$lang]['title'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="Enter meta title for {{ strtoupper($lang) }}">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Keywords ({{ strtoupper($lang) }})</label>
                            <input type="text" name="meta[{{ $lang }}][keywords]"
                                   value="{{ old('meta.'.$lang.'.keywords', $page->meta[$lang]['keywords'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="keyword1, keyword2, keyword3">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Robots ({{ strtoupper($lang) }})</label>
                            <input type="text" name="meta[{{ $lang }}][robots]"
                                   value="{{ old('meta.'.$lang.'.robots', $page->meta[$lang]['robots'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="index,follow">
                        </div>
                        
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Meta Description ({{ strtoupper($lang) }})</label>
                            <textarea name="meta[{{ $lang }}][description]" rows="3"
                                      class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                                      placeholder="Brief description for search engines">{{ old('meta.'.$lang.'.description', $page->meta[$lang]['description'] ?? '') }}</textarea>
                        </div>

                        <!-- Open Graph Section for each language -->
                        <div class="lg:col-span-2 pt-6 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Open Graph ({{ strtoupper($lang) }})
                            </h4>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">OG Title</label>
                                    <input type="text" name="meta[{{ $lang }}][og_title]"
                                           value="{{ old('meta.'.$lang.'.og_title', $page->meta[$lang]['og_title'] ?? '') }}"
                                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                           placeholder="Title for social media sharing">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">OG Image URL</label>
                                    <input type="text" name="meta[{{ $lang }}][og_image]"
                                           value="{{ old('meta.'.$lang.'.og_image', $page->meta[$lang]['og_image'] ?? '') }}"
                                           class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                           placeholder="https://example.com/image.jpg">
                                </div>
                                
                                <div class="lg:col-span-2">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">OG Description</label>
                                    <textarea name="meta[{{ $lang }}][og_description]" rows="2"
                                              class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                                              placeholder="Description for social media sharing">{{ old('meta.'.$lang.'.og_description', $page->meta[$lang]['og_description'] ?? '') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Custom CSS Class ({{ strtoupper($lang) }})</label>
                            <input type="text" name="meta[{{ $lang }}][custom_css_class]"
                                   value="{{ old('meta.'.$lang.'.custom_css_class', $page->meta[$lang]['custom_css_class'] ?? '') }}"
                                   class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="custom-page-class additional-styles">
                        </div>
                    </div>
                @endforeach
            </div>
        </template>
    </div>
</div>