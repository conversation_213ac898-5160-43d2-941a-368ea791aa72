@extends('admin.layouts.admin')

@section('content')
@php
    $languages = getActiveLanguages()->pluck('code')->all();
    $page = $page ?? null;
    $locale = app()->getLocale();

@endphp

<div class="w-full  mx-auto bg-white shadow-xl rounded-2xl overflow-hidden">
    <div class="px-8 py-6 bg-gradient-to-r from-blue-600 to-indigo-600">
        <h1 class="text-2xl font-bold text-white">
            {{ $page ? 'Edit Page' : 'Create New Page' }}
        </h1>
        <p class="text-blue-100 mt-1">Manage your single page content across multiple languages</p>
    </div>

    <form action="{{ localized_route('admin.pages.save', [$page ? $page->id : 0]) }}" method="POST" class="p-8 space-y-8">

                        <!-- Content Type -->
                        <div class="space-y-2">
                            <label for="content_type_id" class="block text-sm font-medium text-gray-700">
                                Content Type <span class="text-red-500">*</span>
                            </label>
                            <select name="content_type_id"
                                    id="content_type_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg 
                                        focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                                    x-model="contentTypeId">
                                <option value="0">Single Page (Static)</option>
                                @foreach($contentTypes as $ct)
                                    @php
                                        $displayName = $ct->name[$locale] ?? $ct->name['en-us'] ?? 'Unknown';
                                    @endphp
                                    <option value="{{ $ct->id }}"
                                        {{ old('content_type_id', $page->content_type_id ?? 0) == $ct->id ? 'selected' : '' }}>
                                        {{ $displayName }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- List / Detail selector -->
                        <div class="space-y-2" x-show="contentTypeId > 0">
                            <label for="content_type" class="block text-sm font-medium text-gray-700">
                                Page Mode <span class="text-red-500">*</span>
                            <select name="content_type"
                                    id="content_type"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg 
                                        focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white">
                                <option value="list"   {{ old('content_type', $page->content_type ?? '') == 'list' ? 'selected' : '' }}>List</option>
                                <option value="detail" {{ old('content_type', $page->content_type ?? '') == 'detail' ? 'selected' : '' }}>Detail</option>
                                <option value="form" {{ old('content_type', $page->content_type ?? '') == 'form' ? 'selected' : '' }}>Form</option>
                            </select>
                        </div>

        @csrf
        {{-- ================= Enable Page ================= --}}
        <div class="group mb-6">
            <div class="flex items-center justify-between">
                <label class="text-lg font-semibold text-gray-800">Enable Page</label>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" name="enable" id="enable"
                        value="1" {{ old('enable', $page->enable ?? 1) ? 'checked' : '' }}
                        class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 transition-all duration-200">
                    <label for="enable" class="text-sm font-medium text-gray-600 cursor-pointer">
                        Toggle to make this page active or inactive
                    </label>
                </div>
            </div>
        </div>

        {{-- ================= Slug ================= --}}
        <div class="group">
            <div class="flex items-center justify-between mb-4">
                <label class="text-lg font-semibold text-gray-800">URL Slug</label>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" name="slug_all_languages" id="slug_all_languages"
                        value="1" {{ old('slug_all_languages', $page->slug_all_languages ?? 0) ? 'checked' : '' }}
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                    <label for="slug_all_languages" class="text-sm font-medium text-gray-600 cursor-pointer">
                        Use same slug for all languages
                    </label>
                </div>
            </div>

            {{-- 单语言统一输入框 --}}
            <div class="transition-all duration-300" id="slug_single_input" style="display:none;">
                <input type="text" name="slug_single"
                    class="slug-input w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter slug for all languages"
                    value="{{ old('slug_single', $page->slug['all'] ?? '') }}">
            </div>

            {{-- Tabs 多语言输入 --}}
            <div class="transition-all duration-300" id="slug_tabs_container">
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl mb-4">
                    @foreach($languages as $idx => $lang)
                        <button type="button"
                                class="tab-btn flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 {{ $idx === 0 ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800' }}"
                                data-lang="{{ $lang }}" data-field="slug">
                            {{ strtoupper($lang) }}
                        </button>
                    @endforeach
                </div>
                @foreach($languages as $idx => $lang)
                    <div class="tab-content {{ $idx !== 0 ? 'hidden' : '' }}" data-lang="{{ $lang }}" data-field="slug">
                        <input type="text" name="slug[{{ $lang }}]"
                            class="slug-input w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            value="{{ old('slug.' . $lang, $page->slug[$lang] ?? '') }}"
                            placeholder="Enter slug for {{ strtoupper($lang) }}">
                    </div>
                @endforeach
            </div>

            {{-- 检查按钮 + 提示 --}}
            <div class="mt-3">
                <button type="button" id="check-slug-btn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition">
                    Check Slug
                </button>
                <p id="slug-check-result" class="mt-2 text-sm"></p>
            </div>
        </div>


        {{-- ================= Title ================= --}}
        <div class="group">
            <div class="flex items-center justify-between mb-4">
                <label class="text-lg font-semibold text-gray-800">Page Title</label>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" name="title_all_languages" id="title_all_languages"
                           value="1" {{ old('title_all_languages', $page->title_all_languages ?? 0) ? 'checked' : '' }}
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                    <label for="title_all_languages" class="text-sm font-medium text-gray-600 cursor-pointer">
                        Use same title for all languages
                    </label>
                </div>
            </div>

            <div class="transition-all duration-300" id="title_single_input" style="display:none;">
                <input type="text" name="title_single" 
                       class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                       placeholder="Enter title for all languages"
                       value="{{ old('title_single', $page->title['all'] ?? '') }}">
            </div>

            <div class="transition-all duration-300" id="title_tabs_container">
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl mb-4">
                    @foreach($languages as $idx => $lang)
                        <button type="button" 
                                class="tab-btn flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 {{ $idx === 0 ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800' }}" 
                                data-lang="{{ $lang }}" data-field="title">
                            {{ strtoupper($lang) }}
                        </button>
                    @endforeach
                </div>
                @foreach($languages as $idx => $lang)
                    <div class="tab-content {{ $idx !== 0 ? 'hidden' : '' }}" data-lang="{{ $lang }}" data-field="title">
                        <input type="text" name="title[{{ $lang }}]" 
                               class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                               value="{{ old('title.' . $lang, $page->title[$lang] ?? '') }}"
                               placeholder="Enter title for {{ strtoupper($lang) }}">
                    </div>
                @endforeach
            </div>
        </div>

        {{-- ================= Content ================= --}}
        <div class="group">
            <div class="flex items-center justify-between mb-4">
                <label class="text-lg font-semibold text-gray-800">Content Template</label>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" name="content_all_languages" id="content_all_languages"
                           value="1" {{ old('content_all_languages', $page->content_all_languages ?? 0) ? 'checked' : '' }}
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                    <label for="content_all_languages" class="text-sm font-medium text-gray-600 cursor-pointer">
                        Use same content for all languages
                    </label>
                </div>
            </div>

            <div class="transition-all duration-300" id="content_single_input" style="display:none;">
                <textarea name="content_single" rows="6" 
                          class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                          placeholder="Enter content for all languages">{{ old('content_single', $page->content['all'] ?? '') }}</textarea>
            </div>

            <div class="transition-all duration-300" id="content_tabs_container">
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl mb-4">
                    @foreach($languages as $idx => $lang)
                        <button type="button" 
                                class="tab-btn flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 {{ $idx === 0 ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800' }}" 
                                data-lang="{{ $lang }}" data-field="content">
                            {{ strtoupper($lang) }}
                        </button>
                    @endforeach
                </div>
                @foreach($languages as $idx => $lang)
                    <div class="tab-content {{ $idx !== 0 ? 'hidden' : '' }}" data-lang="{{ $lang }}" data-field="content">
                        <textarea name="content[{{ $lang }}]" rows="6" 
                                  class="w-full px-4 py-3 text-gray-700 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                                  placeholder="Enter content for {{ strtoupper($lang) }}">{{ old('content.' . $lang, $page->content[$lang] ?? '') }}</textarea>
                    </div>
                @endforeach
            </div>
        </div>

        {{-- ================= Meta ================= --}}
        <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Meta Information</h3>
            @include('admin.pages._meta_form', [
                'page' => $page ?? null,
                'languages' => $languages
            ])
        </div>

        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="#" class="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                {{ $page ? 'Update Page' : 'Create Page' }}
            </button>
        </div>
    </form>
</div>

@endsection


@push('scripts')
<script>
    const fields = ['slug', 'title', 'content'];

    fields.forEach(field => {
        const checkbox = document.getElementById(field + '_all_languages');
        const singleInput = document.getElementById(field + '_single_input');
        const tabsContainer = document.getElementById(field + '_tabs_container');

        function toggleField() {
            if (checkbox.checked) {
                singleInput.style.display = '';
                tabsContainer.style.display = 'none';
            } else {
                singleInput.style.display = 'none';
                tabsContainer.style.display = '';
            }
        }

        checkbox?.addEventListener('change', toggleField);

        // 初始化状态
        toggleField();
    });

    // Tabs 切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const field = btn.dataset.field;
            const lang = btn.dataset.lang;

            // 切换按钮样式
            btn.parentElement.querySelectorAll('button').forEach(b => {
                b.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
                b.classList.add('text-gray-600');
            });
            btn.classList.remove('text-gray-600');
            btn.classList.add('bg-white', 'text-blue-600', 'shadow-sm');

            // 切换内容
            const container = btn.closest('#' + field + '_tabs_container');
            container.querySelectorAll('.tab-content').forEach(tc => tc.classList.add('hidden'));
            container.querySelector('.tab-content[data-lang="'+lang+'"]').classList.remove('hidden');
        });
    });
</script>

<script>
document.getElementById('check-slug-btn').addEventListener('click', function () {
    let resultEl = document.getElementById('slug-check-result');
    resultEl.innerHTML = "Checking...";
    resultEl.className = "mt-2 text-sm text-gray-600";

    // 获取是否勾选“统一slug”
    let useSame = document.getElementById('slug_all_languages').checked;

    let data = {
        slug_all_languages: useSame ? 1 : 0,
        slugs: {}
    };

    if (useSame) {
        // 单一 slug
        let val = document.querySelector('#slug_single_input .slug-input').value.trim();
        data.slugs['all'] = val;
    } else {
        // 多语言 slug
        document.querySelectorAll('#slug_tabs_container .slug-input').forEach(input => {
            let lang = input.getAttribute('name').match(/\[(.*?)\]/)[1]; // 从 name="slug[en]" 提取 en
            data.slugs[lang] = input.value.trim();
        });
    }

    // AJAX 提交
    fetch("{{ localized_route('admin.pages.check-slug') }}", {
        method: "POST",
        headers: {
            "X-CSRF-TOKEN": "{{ csrf_token() }}",
            "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
    })
    .then(res => res.json())
    .then(res => {
        if (res.status === "ok") {
            resultEl.innerHTML = "✅ All slugs are available.";
            resultEl.className = "mt-2 text-sm text-green-600";
        } else if (res.status === "error") {
            let msg = res.errors.map(e => `❌ ${e}`).join("<br>");
            resultEl.innerHTML = msg;
            resultEl.className = "mt-2 text-sm text-red-600";
        }
    })
    .catch(err => {
        console.error(err);
        resultEl.innerHTML = "⚠️ Server error, please try again later.";
        resultEl.className = "mt-2 text-sm text-red-600";
    });
});
</script>

@endpush