@extends('admin.layouts.admin')

@section('title', __('messages.multilang_checker'))
@section('breadcrumb', __('messages.multilang_checker'))


@section('content')
<div class="max-w-6xl mx-auto p-6">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">多语言缺失检测</h1>
        <p class="text-gray-600">检测多语言翻译中的缺失项目</p>
    </div>

    <!-- 警告提示 -->
    <div class="bg-amber-50 border-l-4 border-amber-400 p-4 mb-6 rounded-r-lg">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-amber-700">
                    建议每次最多选择 <strong class="font-semibold">3-5 个模块</strong> 扫描，否则可能会稍慢。
                </p>
            </div>
        </div>
    </div>

    <!-- 扫描表单 -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 rounded-t-lg">
            <h2 class="text-lg font-semibold text-gray-900">扫描配置</h2>
        </div>
        
        <form id="scanForm" class="p-6 space-y-6">
            <!-- 基准语言选择 -->
            <div class="mb-4">
                <label for="sourceLang" class="block text-sm font-medium text-gray-700 mb-2">
                    基准语言
                </label>
                <select id="sourceLang" name="sourceLang" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    @foreach($langs as $language)
                        @php
                            $language->name = json_decode($language->name, true);
                        @endphp
                        <option value="{{ $language->code }}" {{ $language->code === $defaultLang ? 'selected' : '' }}>
                            {{ $language->name[app()->getLocale()] }} ({{ $language->code }})
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    对比语言（可多选）
                </label>
                <div class="grid grid-cols-2 gap-2">
                    @foreach($langs as $language)

                        <label class="inline-flex items-center">
                            <input type="checkbox" name="targetLangs[]" value="{{ $language->code }}" class="text-blue-600 border-gray-300 rounded shadow-sm focus:ring-blue-500">
                            <span class="ml-2">{{ $language->name[app()->getLocale()] }} ({{ $language->code }})</span>
                        </label>
                    @endforeach
                </div>
            </div>


            <!-- 模块选择 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    请选择要扫描的模块
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($groupedCheckers as $tableName => $checkers)
                        @php
                            // module 字段是 JSON，解析第一个 checker 的 module 作为当前表名多语言显示
                            $moduleJson = $checkers->first()->module ?? '{}';
                            $moduleNames = json_decode($moduleJson, true);
                            $displayTableName = $moduleNames[app()->getLocale()] ?? $moduleNames['en-us'] ?? $tableName;
                        @endphp

                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-2">{{ $displayTableName }}</h3>

                            @foreach($checkers as $checker)
                                @php
                                    $descJson = $checker->column_description ?? '{}';
                                    $desc = json_decode($descJson, true);
                                    $displayFieldName = $desc[$locale] ?? $desc['en'] ?? $checker->json_column_name;
                                @endphp

                                <div class="relative mb-2">
                                    <label class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group">
                                        <input 
                                            class="checker-box w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" 
                                            type="checkbox" 
                                            name="checker_ids[]" 
                                            value="{{ $checker->id }}" 
                                            id="checker{{ $checker->id }}"
                                        >
                                        <div class="ml-3 flex-1">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $displayFieldName }}
                                            </div>
                                        </div>
                                        <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="pt-4">
                <button 
                    type="submit" 
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    开始扫描
                </button>
            </div>
        </form>
    </div>

    <!-- 进度条 -->
    <div id="progressContainer" class="mt-6 hidden">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">扫描进度</span>
                <span id="progressText" class="text-sm text-gray-500">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
                <div 
                    id="progressBar" 
                    class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style="width: 0%;"
                ></div>
            </div>
        </div>
    </div>

    <div id="missingDataContainerSource" class="hidden">
    <div id="sourceMissingList" class="mb-4"></div> <!-- 新增用于显示源语言缺失 -->
</div>
        <!-- 无缺失数据展示 -->
         <div id="noMissingDataContainer" class="mt-8 max-w-6xl mx-auto p-6 bg-white shadow-lg rounded-lg border border-gray-200  hidden">
            <p class="text-green-600">恭喜！没有缺失的源语言内容。</p>
        </div>

        <!-- 缺失数据展示 -->
    <div id="missingDataContainerTarget" class="mt-8 max-w-6xl mx-auto p-6 bg-white shadow-lg rounded-lg border border-gray-200 hidden">
        <h3 class="mt-6 text-lg font-semibold">
            💡 请将以下红色框中所有内容复制并发送给 AI，让其翻译。翻译结果粘贴到下方输入框中：
        </h3>

        <!-- 红色边框包裹提示 + 数据 -->
        <div class="border-2 border-red-500 p-4 rounded-lg bg-white mt-2">
            <p class="mb-2 text-gray-700 whitespace-pre-wrap font-mono text-sm" id="aiPrompt"></p>
            <div id="missingItemsList" class="space-y-2 max-h-72 overflow-y-auto border border-gray-300 rounded p-4 bg-gray-50 font-mono text-sm text-gray-800"></div>
        </div>


        <label for="translatedResults" class="block mt-6 mb-2 font-medium text-gray-700">请将 AI 翻译结果 JSON 粘贴到这里：</label>
        <textarea id="translatedResults" rows="8" class="w-full p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm" placeholder='格式示例：[{"record_id":1,"lang_code":"zh-cn","text":"翻译结果"}]'></textarea>

        <button id="submitTranslation" class="mt-4 px-5 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
            提交翻译补全
        </button>
    </div>
</div>


<!-- 模态框 -->
<div id="confirmModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden">
  <div class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full">
    <h2 class="text-lg font-semibold mb-4">提示</h2>
    <p id="confirmMessage" class="text-gray-700 mb-6">
    </p>
    <div class="flex justify-end gap-3">
      <button id="cancelBtn" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">取消</button>
      <button id="okBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">继续</button>
    </div>
  </div>
</div>


@endsection

@push('scripts')
@vite(['resources/admin/js/language_checker.js'])
@endpush
