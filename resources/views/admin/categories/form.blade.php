@extends('admin.layouts.admin')

@section('title', $parentId > 0 ? __('Category Management') : __('Category Label Management'))
@section('breadcrumb', $parentId > 0 ? __('Categories') : __('Category Labels'))

@section('content')
@if ($errors->any())
    <div class="mb-4 p-4 bg-red-50 border border-red-300 rounded text-red-700">
        <ul class="list-disc list-inside">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {{-- Category Tree Sidebar --}}
        <div class="lg:col-span-5">
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ __('Category Tree') }}
                    </h3>
                    @if ($parentId > 0)
                    <a href="{{ localized_route('admin.categories.form', [$categoryId , 0, 0, 0]) }}"
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                        {{ __('Category') }}
                    </a>
                    @endif
                </div>

                <div class="p-6">
                    @if (!empty($categoryTree))
                        <div class="space-y-2">
                            @foreach ($categoryTree as $node)
                                @include('admin.categories.partials.category_node', [
                                    'node' => $node,
                                    'categoryId' => $categoryId,
                                    'level' => 0
                                ])
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">
                                {{ $parentId > 0 ? __('No categories') : __('No labels') }}
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ $parentId > 0 ? __('Get started by creating your first category.') : __('Get started by creating your first label.') }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        {{-- Right Side Content Area with Inline Form --}}
        <div class="lg:col-span-7">
            <div class="bg-white shadow-sm rounded-lg p-6">
                <form method="POST" 
                      action="{{ localized_route('admin.categories.store-update', [$parentId ?? 0, $category->id ?? null]) }}"
                      class="space-y-6">
                    @csrf
                    
                    <input type="hidden" name="category_id" value="{{ $categoryId }}">
                    <input type="hidden" name="parent_id" value="{{ $parentId }}">
                    <input type="hidden" name="id" value="{{ $id }}">

                    {{-- Category Name Fields --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-md font-semibold text-gray-900">
                            @if (isset($category) && $category->id)
                                {{ $parentId > 0 ? __('Edit Category') : __('Edit Label') }}: 
                                <span class="text-blue-600 font-medium">
                                    {{ $category->name[app()->getLocale()] ?? $category->name['en-us'] ?? 'Unnamed' }}
                                </span>
                            @elseif (isset($parent) && $parent)
                                {{ __('Create Sub:type for', ['type' => $parentId > 0 ? __('category') : __('label')]) }}: 
                                <span class="text-blue-600 font-medium">
                                    {{ $parent->name[app()->getLocale()] ?? $parent->name['en-us'] ?? 'Unnamed' }}
                                </span>
                            @else
                                {{ __('Create New Root :type', ['type' => $parentId > 0 ? __('Category') : __('Label')]) }}
                            @endif
                        </h4>

                        <div id="kazcmsQuickFillContainer"></div>
                    </div>


                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach (getActiveLanguages() as $lang)
                                @php $lang->name = json_decode($lang->name, true); @endphp
                                <div>
                                    <label for="name_{{ $lang->code }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ __('Name') }} ({{ $lang->name[app()->getLocale()] ?? $lang->code }})
                                    </label>
                                    <input type="text" 
                                           id="name_{{ $lang->code }}"
                                           name="name[{{ $lang->code }}]"
                                           value="{{ old("name.{$lang->code}", $category->name[$lang->code] ?? '') }}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="{{ $parentId > 0 ? __('Enter category name') : __('Enter label name') }}">
                                    @error("name.{$lang->code}")
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Slug Fields --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">{{ __('URL Slug') }}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach (getActiveLanguages() as $lang)
                                @php $lang->name = json_decode($lang->name, true); @endphp
                                <div>
                                    <label for="slug_{{ $lang->code }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ __('Slug') }} ({{ $lang->name[app()->getLocale()] ?? $lang->code }})
                                    </label>
                                    <input type="text" 
                                           id="slug_{{ $lang->code }}"
                                           name="slug[{{ $lang->code }}]"
                                           value="{{ old("slug.{$lang->code}", $category->slug[$lang->code] ?? '') }}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="{{ __('auto-generated-slug') }}">
                                    @error("slug.{$lang->code}")
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Description Fields --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">{{ __('Description') }}</h4>
                        <div class="space-y-4">
                            @foreach (getActiveLanguages() as $lang)
                                @php $lang->name = json_decode($lang->name, true); @endphp
                                <div>
                                    <label for="description_{{ $lang->code }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ __('Description') }} ({{ $lang->name[app()->getLocale()] ?? $lang->code }})
                                    </label>
                                    <textarea id="description_{{ $lang->code }}"
                                              name="description[{{ $lang->code }}]" 
                                              rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                              placeholder="{{ $parentId > 0 ? __('Enter category description') : __('Enter label description') }}">{{ old("description.{$lang->code}", $category->description[$lang->code] ?? '') }}</textarea>
                                    @error("description.{$lang->code}")
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Configuration Options --}}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">{{ __('Configuration') }}</h4>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_virtual" 
                                   name="is_virtual" 
                                   value="1"
                                   {{ old('is_virtual', $category->is_virtual ?? true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_virtual" class="ml-2 block text-sm text-gray-900">
                                {{ __('Visible on Frontend') }}
                            </label>
                        </div>
                    </div>

                    {{-- Form Actions --}}
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" 
                                onclick="closeModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            {{ __('Cancel') }}
                        </button>
                        <button type="submit"
                                class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            {{ $parentId > 0 ? __('Save Category') : __('Save Label') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{{-- JS Variables & Form Helper --}}
@php
    $formFields = [
        'name', 'slug', 'description', 'is_virtual'
    ];
@endphp


@endsection

@push('scripts')
@vite('resources/common/kazcms-form-helper.js')
<script>
    window.kazcmsFormFields = @json($formFields);
    window.kazcmsFormLanguages = @json(getActiveLanguages()->pluck('code')->toArray());

    document.addEventListener('DOMContentLoaded', () => {
        if(window.KazCMSFormHelper){
            KazCMSFormHelper.init({
                fields: window.kazcmsFormFields,
                languages: window.kazcmsFormLanguages,
                 buttonContainerId: 'kazcmsQuickFillContainer'
            });
        }
    });
</script>
@endpush

