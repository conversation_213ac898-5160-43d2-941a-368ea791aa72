
@php
    $hasChildren = !empty($node->children);
    $indentLevel = ($level ?? 0) * 1.5; // 1.5rem per level
@endphp

<div class="group relative" style="margin-left: {{ $indentLevel }}rem;" 
     x-data="{ expanded: true }"> 
    {{-- Tree connector lines --}}
    @if ($level > 0)
        <div class="absolute left-0 top-0 w-6 h-6 border-l-2 border-b-2 border-gray-200"></div>
    @endif
    
    {{-- Category item --}}
    <div class="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-blue-50 transition-all duration-200 border border-transparent hover:border-blue-200">
        <div class="flex items-center space-x-3 min-w-0 flex-1">
            {{-- Expand/Collapse indicator --}}
            @if ($hasChildren)
                <button class="flex-shrink-0 w-4 h-4 text-gray-400 hover:text-gray-600 focus:outline-none"
                        
                        @click="expanded = !expanded">
                    <svg x-show="expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                    <svg x-show="!expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            @else
                <div class="w-4 h-4 flex items-center justify-center">
                    <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                </div>
            @endif
            
            {{-- Category icon --}}
            <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
            </div>
            
            {{-- Category info --}}
            <div class="min-w-0 flex-1">
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-900 truncate">
                        {{ $node->name[app()->getLocale()] ?? $node->name['en-us'] ?? 'Untitled Category' }}
                    </span>
                    
                    {{-- Children count badge --}}
                    @if ($hasChildren)
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ count($node->children) }}
                        </span>
                    @endif
                    
                    {{-- Virtual indicator --}}
                    @if (!($node->is_virtual ?? true))
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{ __('Hidden') }}
                        </span>
                    @endif
                </div>
                
                {{-- Category description --}}
                @if (!empty($node->description[app()->getLocale()]))
                    <p class="text-xs text-gray-500 mt-1 truncate">
                        {{ $node->description[app()->getLocale()] }}
                    </p>
                @endif
            </div>
        </div>
        
        {{-- Action buttons --}}
        <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            {{-- Edit link --}}
            <a href="{{ localized_route('admin.categories.form', [$categoryId, $node->parent_id ?? $parentId ?? 0, $node->id ?? 0, 1]) }}"
            class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 hover:border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 transition-all duration-200"
            title="{{ __('Edit Category') }}">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                {{ __('Edit') }}
            </a>

            {{-- Add Subcategory link --}}
            <a href="{{ localized_route('admin.categories.form', [$categoryId, $node->id, 0, 1]) }}"
            class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200"
            title="{{ __('Add Subcategory') }}">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                {{ __('Add') }}
            </a>

            {{-- Delete link --}}
            <form method="POST" action="{{ localized_route('admin.categories.destroy', [$categoryId,$node->id]) }}"
      onsubmit="return confirm('{{ __('Are you sure you want to delete this category and its subcategories?') }}')"
      class="inline-block">
    @csrf
    @method('DELETE')
    
    <button type="submit"
            class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 hover:border-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-all duration-200"
            title="{{ __('Delete Category') }}">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
        </svg>
        {{ __('Delete') }}
    </button>
</form>
        </div>

    </div>
    
    {{-- Children categories --}}
    @if ($hasChildren)
        <div x-show="expanded" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             class="mt-1 space-y-1">
            @foreach ($node->children as $child)
                @include('admin.categories.partials.category_node', [
                    'node' => $child, 
                    'categoryId' => $categoryId,
                    'parentId' => $node->id,
                    'level' => ($level ?? 0) + 1
                ])
            @endforeach
        </div>
    @endif
</div>