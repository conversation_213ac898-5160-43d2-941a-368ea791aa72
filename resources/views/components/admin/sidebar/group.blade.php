@props([
    'id',
    'icon' => 'fas fa-folder',
    'label' => '',
    'open' => false,
    'color'=> 'gray',
    'items' => [], // 每项应包含 'href' 和 'label'
])

<li>
    <div class="menu-item">
        <button type="button" data-toggle-submenu="{{ $id }}"
                class="flex items-center justify-between w-full p-3 rounded-lg hover:bg-gray-700 submenu-toggle">
            <div class="flex items-center space-x-3">
                <i class="{{ $icon }} w-5 text-center text-{{ $color }}-400"></i>
                <span class="sidebar-text">{{ $label }}</span>
            </div>
            <i class="fas fa-chevron-down sidebar-text transition-transform duration-200 {{ $open ? 'rotate-180' : '' }}" id="{{ $id }}-icon"></i>
        </button>
        <ul id="{{ $id }}" class="ml-8 mt-2 space-y-1 {{ $open ? '' : 'hidden' }}">
            @foreach ($items as $item)
                <li>
                    <a href="{{ $item['href'] ?? '#' }}"
                       class="block p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white">
                        {{ $item['label'] ?? '' }}
                    </a>
                </li>
            @endforeach
        </ul>
    </div>
</li>
