@php
$kazUserMenus = kaz_menus('admin', app()->getLocale(), 'admin.left');
@endphp
<x-admin.sidebar.container>
    @foreach ($kazUserMenus as $menu)
        @if ($menu->children->isEmpty())
            <x-admin.sidebar.item
                href="/{{ app()->getLocale() }}/{{ ltrim($menu->url ?? '#', '/') }}"
                icon="{{ $menu->icon ?? 'fas fa-link' }}"
                label="{{ $menu->display_name }}"
                :active="request()->url() === url('/' . app()->getLocale() . '/' . ltrim($menu->url ?? '', '/'))"
                color="{{ $menu->color ?? 'blue' }}"
            />
        @else
            <x-admin.sidebar.group
                id="menu-{{ $menu->id }}"
                icon="{{ $menu->icon ?? 'fas fa-folder' }}"
                label="{{ $menu->display_name }}"
                :items="
                    $menu->children->map(function ($child) {
                        return [
                            'href' => $child->url ?? '#',
                            'label' => $child->display_name,
                        ];
                    })->toArray()
                "
                color="{{ $menu->color ?? 'gray' }}"
            />
        @endif
    @endforeach
</x-admin.sidebar.container>
