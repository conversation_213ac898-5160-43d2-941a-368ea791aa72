    <!-- Header -->
    @php
        $contentTypes = getFrontendContentTypes();
    @endphp
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <!-- Top Bar -->
        <div class="bg-blue-100 text-blue-900 py-2">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center text-sm">
                    <div class="flex items-center space-x-6">
                        <span><i class="fas fa-phone mr-1"></i> +****************</span>
                        <span><i class="fas fa-envelope mr-1"></i> <EMAIL></span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="#" class="hover:text-blue-400 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="hover:text-blue-400 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-blue-400 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="hover:text-blue-400 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
         <!-- Logo -->
<div class="flex items-center space-x-3">
    <div class="w-12 h-12 flex items-center justify-center float-animation">
        <!-- SVG LOGO 缩放到适合的大小 -->
         <a href="{{ localized_route('home') }}" class="w-12 h-12">
        <svg class="w-full h-full" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
            <!-- 左上角 - 黄色 -->
            <rect x="50" y="50" width="140" height="140" rx="15" ry="15" fill="#FFD700"/>
            <!-- 右上角 - 蓝色 -->
            <rect x="210" y="50" width="140" height="140" rx="15" ry="15" fill="#2E86AB"/>
            <!-- 左下角 - 深蓝色 -->
            <rect x="50" y="210" width="140" height="140" rx="15" ry="15" fill="#1B4F72"/>
            <!-- 右下角 - 青色 -->
            <rect x="210" y="210" width="140" height="140" rx="15" ry="15" fill="#17A2B8"/>
        </svg>
        </a>
    </div>
    <div>
        <h1 class="text-2xl font-bold gradient-text">KAZCMS</h1>
        <p class="text-gray-500 text-sm">{{ __('messages.welcome') }}</p>
    </div>
</div>


                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                @foreach ($contentTypes as $type)
                    <a href="/{{app()->getLocale()}}/{{$type->slug[app()->getLocale()]}}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors {{ request()->routeIs('home') ? 'text-blue-600 border-b-2 border-blue-600 pb-1' : '' }}">
                        {{$type->name[app()->getLocale()]}}
                    </a>
                @endforeach
                </nav>

                <!-- Right Section -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="hidden md:block relative">
                        <input type="text"
                            placeholder="{{ __('Search products...') }}"
                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>

                    <!-- Cart -->
                    <a href="{{ localized_route('cart.index') }}"
                        class="relative p-2 text-gray-700 hover:text-blue-600 transition-colors"
                        title="{{ __('Shopping Cart') }}">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                            {{ session('cart_count', 0) }}
                        </span>
                    </a>

                    <!-- User Account -->
                    <a href="{{ localized_route('account.index') }}"
                        class="hidden md:flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors">
                        <i class="fas fa-user"></i>
                        <span>{{ __('Account') }}</span>
                    </a>

                    <!-- Enhanced Language Switcher -->
                    <div class="relative group">
                        <button class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors rounded-md hover:bg-gray-50"
                            aria-label="{{ __('Change Language') }}"
                            aria-expanded="false"
                            aria-haspopup="true">
                            <span class="text-lg mr-2">{{ $languageFlags[app()->getLocale()] ?? '🌐' }}</span>
                            <span class="hidden sm:inline font-medium">{{ $languageNames[app()->getLocale()] ?? strtoupper(app()->getLocale()) }}</span>
                            <span class="sm:hidden font-medium">{{ strtoupper(app()->getLocale()) }}</span>
                            <svg class="ml-1 w-4 h-4 transition-transform group-hover:rotate-180"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-1">
                                @php
                                $availableLanguages = getActiveLanguages();
                                    $currentLanguage = $availableLanguages[app()->getLocale()];
                                    $currentLanguage['name'] = json_decode($currentLanguage['name'], true);
                                    $localizedPaths = $localizedPaths ?? [];
                                @endphp
                                @foreach ($availableLanguages as $language)
                                    @php
                                            if (is_string($language['name'])) {
                                                $language['name'] = json_decode($language['name'], true);
                                            } 
                                    @endphp
                                <a href="{{ localized_current_url($language->code,$localizedPaths) }}"
                                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors {{ app()->getLocale() === $language->locale ? 'bg-blue-50 text-blue-600 font-medium' : '' }}"
                                    @if(app()->getLocale() === $language->locale) aria-current="true" @endif>
                                    <div class="flex flex-col">
                                        <span class="font-medium">{{ $language['name'][$language['code']]}}</span>
                                    </div>
                                    @if(app()->getLocale() === $language->code)
                                    <i class="fas fa-check ml-auto text-blue-600"></i>
                                    @endif
                                </a>
                                @endforeach

                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button onclick="toggleMobileMenu()"
                        class="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors"
                        aria-label="{{ __('Toggle Mobile Menu') }}"
                        aria-expanded="false">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>



                <!-- 辅助样式 -->
                <style>
                    .group:hover .group-hover\:rotate-180 {
                        transform: rotate(180deg);
                    }

                    /* 确保下拉菜单在移动设备上也能正常显示 */
                    @media (max-width: 640px) {
                        .group:hover>div {
                            right: -50px;
                            width: 200px;
                        }
                    }

                    /* 平滑的hover效果 */
                    .transition-all {
                        transition: all 0.2s ease-in-out;
                    }
                </style>

                <!-- 增强的JavaScript功能 -->
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // 处理语言切换器的键盘导航
                        const languageSwitcher = document.querySelector('.group button');
                        const dropdown = document.querySelector('.group > div');

                        if (languageSwitcher && dropdown) {
                            languageSwitcher.addEventListener('click', function(e) {
                                e.preventDefault();
                                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                                this.setAttribute('aria-expanded', !isExpanded);
                                dropdown.classList.toggle('opacity-100');
                                dropdown.classList.toggle('invisible');
                            });

                            // 点击外部关闭下拉菜单
                            document.addEventListener('click', function(e) {
                                if (!languageSwitcher.contains(e.target) && !dropdown.contains(e.target)) {
                                    languageSwitcher.setAttribute('aria-expanded', 'false');
                                    dropdown.classList.add('opacity-0');
                                    dropdown.classList.add('invisible');
                                }
                            });

                            // ESC键关闭下拉菜单
                            document.addEventListener('keydown', function(e) {
                                if (e.key === 'Escape') {
                                    languageSwitcher.setAttribute('aria-expanded', 'false');
                                    dropdown.classList.add('opacity-0');
                                    dropdown.classList.add('invisible');
                                }
                            });
                        }
                    });

                    // 语言切换函数
                    function switchLanguage(locale) {
                        // 可以添加loading状态
                        const switcher = document.querySelector('.group button');
                        if (switcher) {
                            switcher.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                        }

                        // 执行语言切换
                        window.location.href = `/language/${locale}`;
                    }
                </script>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden mobile-menu bg-white border-t border-gray-200">
            <div class="container mx-auto px-4 py-4">
                <nav class="flex flex-col space-y-4">
                    <a href="{{ localized_route('home') ?? '#' }}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2">Home</a>
                    <a href="{{ localized_route('products') ?? '#' }}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2">Products</a>
                    <a href="{{ localized_route('blog') ?? '#' }}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2">Blog</a>
                    <a href="{{ localized_route('about') ?? '#' }}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2">About</a>
                    <a href="{{ localized_route('contact') ?? '#' }}" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2">Contact</a>
                    <div class="pt-4 border-t border-gray-200">
                        <input type="text" placeholder="Search products..." class="w-full pl-4 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <!-- Language Switcher for Mobile -->
                    <div class="pt-4 border-t border-gray-200">
                        @foreach(['en', 'fr', 'zh'] as $lang)
                        <a href="/{{ $lang }}{{ request()->getPathInfo() }}"
                            class="block text-gray-700 hover:text-blue-600 font-medium py-2">
                            {{ strtoupper($lang) }}
                        </a>
                        @endforeach
                    </div>
                </nav>
            </div>
        </div>
    </header>