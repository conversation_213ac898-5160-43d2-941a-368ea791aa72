// resources/views/layouts/app.blade.php
<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" @if(in_array(app()->getLocale(), ['ar', 'he', 'fa', 'ur'])) dir="rtl" @else dir="ltr" @endif>
<head>
    @include('partials.header')
</head>
<body class="bg-gray-50">
    @include('partials.nav')

    <main>
        @yield('content')
    </main>

    @include('partials.footer')
    @stack('scripts')
</body>
</html>

// resources/views/partials/header.blade.php
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="csrf-token" content="{{ csrf_token() }}" />
<title>@yield('title', 'Welcome to Our Store')</title>
<meta name="description" content="@yield('description', 'Discover amazing products and latest articles from our store')" />
<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
<style>
    body { font-family: 'Inter', sans-serif; }
    html { scroll-behavior: smooth; }
    html[dir="rtl"] {
        direction: rtl;
        text-align: right;
    }
</style>

// resources/views/partials/nav.blade.php
@include('partials.topbar')
@include('partials.main_nav')

// resources/views/partials/footer.blade.php
<footer class="bg-blue-100 text-blue-900 py-16">
    <!-- Footer Content Here -->
</footer>
<button id="back-to-top" class="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible">
    <i class="fas fa-chevron-up"></i>
</button>
<script>
    // JavaScript for footer functionality
</script>
