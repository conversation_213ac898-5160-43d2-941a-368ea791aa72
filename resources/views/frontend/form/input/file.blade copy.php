@php
    $field_ids = "{$field->field_type_id}_{$field->id}";

    $field_name = "fields_{$field->field_type_id}_{$field->id}";
    $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';
    $required = $field?->is_required ?? false;
    $help_texts = json_decode($field->help_text_json ?? '{}', true);
    $maxUploadCount = $field->max_upload_count ?? 1;
    $acceptTypes = $field->allowed_file_types ?? ''; // comma-separated
    $acceptMime = collect(explode(',', $acceptTypes))->map(fn($ext) => '.' . trim($ext))->join(',');
@endphp
    <div class="w-48 flex items-start pt-3">
      <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
        {{ $label }} @if($required)<span class="text-red-500 ml-1">*</span>@endif
      </label>
    </div>

    <div class="flex-1">
          <!-- Tabs 标题栏 -->
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs" role="tablist">
            @foreach($enabledLanguages as $index => $lang)
                <button
                type="button"
                role="tab"
                aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids}}"
                id="tab-{{ $lang->code }}-{{$field_ids}}"
                data-lang="{{ $lang->code }}"
                data-field="{{ $field_ids }}"
                class="py-2 px-4 text-sm font-medium rounded-t-lg
                    focus:outline-none
                    {{ $loop->first? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                {{ strtoupper($lang->code) }}
                </button>
            @endforeach
            </nav>
        </div>
        <div class="mt-4 flex items-center space-x-2">
        <input type="checkbox"
       id="shared_{{ $field_name }}"
       name="shared_{{ $field_name }}"
       value="1"
       class="shared-sameimages"
       data-field-ids="{{ $field_ids }}"
       data-primary="{{ app()->getLocale() }}"
       {{ old("shared_{$field_name}") ? 'checked' : '' }}>
    <label for="shared_{{ $field_name }}" class="text-sm text-gray-700">
        所有语言使用相同的图片
    </label>
</div>

@foreach($enabledLanguages as $index => $lang)
    @php
        $langCode = $lang->code;
        $help = $help_texts[$langCode] ?? '';
        $value = old("{$field_name}.{$langCode}", $data?->{$field_name}[$langCode] ?? '');
    @endphp
    <div 
        class="code-tab-content {{ $loop->first ? 'block' : 'hidden' }} tabpanel-{{$field_ids}}"
        aria-labelledby="tab-{{ $langCode }}-{{$field_ids}}"
        id="tab-panel-{{ $langCode }}-{{$field_ids}}"
    >
        <input
            type="file"
            class="kaz-file-input"
            data-preview="PreviewContainer_{{ $field_name }}_{{ $langCode }}"
            name="{{ $field_name }}[{{ $langCode }}]"
            id="{{ $field_name }}_{{ $langCode }}"
            {{ $acceptMime ? "accept=$acceptMime" : '' }}
            
            {{ $maxUploadCount > 1 ? 'multiple' : '' }}
            data-target-existing="#existingImages_{{ $field_name }}_{{ $langCode }}"
        >
        @php
    // 取当前语言的附件 ID 数组
    $imageIds = $data["fields_{$field->field_type_id}_{$field->id}"][$langCode] ?? [];

    // 根据附件 ID 拿到 URL
    $existingImages = collect($imageIds)
        ->map(fn($id) => $data['attachments'][$id]->url ?? null)
        ->filter()          // 去掉 null
        ->values()          // 重排索引
        ->all();            // 变回纯数组

    // 默认值：未提交过 → 用现有图片；提交失败回表单 → 用 old()
    $defaultJson = json_encode($existingImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    // 如果有 old()，优先生效
    $value = old("existing_images_{$field_name}.{$langCode}", $defaultJson);
@endphp

<input
    type="text"
    id="existingImages_{{ $field_name }}_{{ $langCode }}"
    name="existing_images_{{ $field_name }}[{{ $langCode }}]"
    value='{{ $value }}'>

<input type="text" name="image_order_{{ $field_name }}[{{ $langCode }}]" value="" >

        <p class="mt-2 text-xs text-gray-500">提示：你可以拖拽附件来改变顺序。</p>


        @if($help)
            <p class="mt-1 text-sm text-gray-500">{{ $help }}</p>
        @endif

        @error("{$field_name}.{$langCode}")
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>
    @if($required )
          <div
            id="req_{{ $field_name }}"
            class="need_require text-sm mt-1 text-red-600"
            data-required="{{ $required ? '1' : '0' }}"
          ></div>
        @endif
    </div>
@endforeach

        
    </div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    /** 切换整组面板继承状态 */
    const toggleGroupInheritance = cb => {
        const fieldIds   = cb.dataset.fieldIds;    // 如 "3_6"
        const primaryLang = cb.dataset.primary;    // 当前后台默认语言
        const checked    = cb.checked;

        // 1) 灰化 / 取消灰化 该组下所有非主语言面板
        document
          .querySelectorAll(`.tabpanel-${fieldIds}`)
          .forEach(panel => {
              const isPrimary = panel.id.endsWith(`${primaryLang}-${fieldIds}`);
              if (isPrimary) return;
              panel.classList.toggle('kaz-inherited', checked);
          });

        // 2) 给 Tab 标题加链条图标
        document
          .querySelectorAll(`#language-tabs button[data-field="${fieldIds}"]`)
          .forEach(tab => {
              const isPrimary = tab.dataset.lang === primaryLang;
              if (isPrimary) return;
              tab.classList.toggle('tab-inherited', checked);
          });
    };

    /** 初始化 & 监听所有同名 checkbox */
    document
      .querySelectorAll('input.shared-sameimages')
      .forEach(cb => {
          // 初始化
          toggleGroupInheritance(cb);

          // 监听变更
          cb.addEventListener('change', () => toggleGroupInheritance(cb));
      });
});
</script>

@endpush

