@php
    $field_ids = "{$field->field_type_id}_{$field->id}";
    $field_name = "fields_{$field->field_type_id}_{$field->id}";
    $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';
    $required = $field?->is_required ?? false;
    $help_texts = json_decode($field->help_text_json ?? '{}', true);

    $only_show_locale = $field->only_show_locale ?? true;
    $currentLang = app()->getLocale();

    // Filter enabledLanguages according to $only_show_locale
    if ($only_show_locale) {
        // Keep only current language
        $enabledLanguagesFiltered = collect($enabledLanguages)
            ->filter(fn($lang) => $lang->code === $currentLang)
            ->values();
    } else {
        $enabledLanguagesFiltered = collect($enabledLanguages);
    }

    // Whether to show tabs: only if more than 1 language
    $showTabs = $enabledLanguagesFiltered->count() > 1;
@endphp

<!-- Left Label -->
<div class="w-48 flex items-start pt-3">
    <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
        {{ $label }} @if($required)<span class="text-red-500 ml-1">*</span>@endif
    </label>
</div>

<!-- Right side textarea(s) -->
<div class="flex-1">

    @if($showTabs)
        <!-- Tabs navigation -->
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs" role="tablist">
                @foreach($enabledLanguagesFiltered as $index => $lang)
                    <button
                        type="button"
                        role="tab"
                        aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                        aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids}}"
                        id="tab-{{ $lang->code }}-{{$field_ids}}"
                        data-lang="{{ $lang->code }}"
                        data-field="{{ $field_ids }}"
                        class="py-2 px-4 text-sm font-medium rounded-t-lg
                            focus:outline-none
                            {{ $loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </nav>
        </div>
    @endif

    @foreach($enabledLanguagesFiltered as $index => $lang)
        @php
            $langCode = $lang->code;
            $help = $help_texts[$langCode] ?? '';
            $value = old("{$field_name}.{$langCode}", $data[$field_name][$langCode] ?? '');
        @endphp
        <div 
            class="code-tab-content {{ (!$showTabs || $loop->first) ? 'block' : 'hidden' }} tabpanel-{{$field_ids}}"
            aria-labelledby="tab-{{ $langCode }}-{{$field_ids}}"
            id="tab-panel-{{ $langCode }}-{{$field_ids}}"
        >
            <textarea
                id="textarea_{{ $field_name }}_{{ $langCode }}"
                name="{{ $field_name }}[{{ $langCode }}]"
                rows="6"
                placeholder="{{ $help }}"
                class="kazcms-rich-textarea w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono resize-y"
            >{{ $value }}</textarea>
            @error("{$field_name}.{$langCode}")
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>
    @endforeach

    @if($required)
        <div
            id="req_{{ $field_name }}"
            class="need_require text-sm mt-1 text-red-600"
            data-required="1"
        ></div>
    @endif
</div>

<link rel="stylesheet" href="{{ asset('static/css/kazeditor.css') }}">
<script src="{{ asset('static/js/kazeditor.'.app()->getLocale().'.js') }}"></script>
<script src="{{ asset('static/js/kaz-editor.umd.js') }}?v=1.0.2"></script>

<script>
    // Optional KazEditor init here
    // document.addEventListener('DOMContentLoaded', function () {
    //     const allButtons = Object.keys(window.KazEditorLang || {});
    //
    //     window.KazEditor.KazEditor.init({
    //         lang: '{{ app()->getLocale() }}',
    //         target: 'textarea.kazcms-rich-textarea',
    //         autosync: true,
    //         toolbar: allButtons
    //     });
    // });
</script>
