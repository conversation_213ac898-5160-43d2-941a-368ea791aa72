@php
    // Unique field identifier
    $field_ids = "{$field->field_type_id}_{$field->id}";

    // Input field name prefix
    $field_name = "fields_{$field->field_type_id}_{$field->id}";

    // Current locale label or fallback
    $label = json_decode($field->label_json, true)[app()->getLocale()] ?? 'Code Field';

    // Required flag
    $required = $field?->is_required ?? false;

    // Help texts per language
    $help_texts = json_decode($field->help_text_json ?? '{}', true);

    // Max uploads allowed
    $maxUploadCount = $field->max_upload_count ?? 1;

    // Allowed file extensions, comma-separated
    $acceptTypes = $field->allowed_file_types ?? '';

    // Convert extensions to accept attribute format
    $acceptMime = collect(explode(',', $acceptTypes))
        ->map(fn($ext) => '.' . trim($ext))
        ->join(',');

    // Whether to show input only for current locale
    $only_show_locale = $field->only_show_locale ?? true;

    // Current app locale code
    $currentLang = app()->getLocale();

    // Filter enabled languages depending on only_show_locale
    $languagesToShow = $only_show_locale
        ? collect($enabledLanguages)->filter(fn($lang) => $lang->code === $currentLang)->all()
        : $enabledLanguages;

    // Determine if tabs should be shown:
    // Tabs only if more than one language and not only_show_locale
    $showTabs = !$only_show_locale && count($enabledLanguages) > 1;
@endphp

<div class="w-48 flex items-start pt-3">
    <label class="block text-lg font-semibold text-gray-900" for="input_tabs">
        {{ $label }} @if($required)<span class="text-red-500 ml-1">*</span>@endif
    </label>
</div>

<div class="flex-1">
    {{-- Show language tabs only if needed --}}
    @if($showTabs)
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px space-x-4" aria-label="Tabs" id="language-tabs" role="tablist">
                @foreach($languagesToShow as $index => $lang)
                    <button
                        type="button"
                        role="tab"
                        aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                        aria-controls="tab-panel-{{ $lang->code }}-{{$field_ids}}"
                        id="tab-{{ $lang->code }}-{{$field_ids}}"
                        data-lang="{{ $lang->code }}"
                        data-field="{{ $field_ids }}"
                        class="py-2 px-4 text-sm font-medium rounded-t-lg
                            focus:outline-none
                            {{ $loop->first ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                        {{ strtoupper($lang->code) }}
                    </button>
                @endforeach
            </nav>
        </div>
    @endif

    {{-- Checkbox for shared images --}}
    @if($showTabs)
        <div class="mt-4 flex items-center space-x-2">
            <input 
                type="checkbox"
                id="shared_{{ $field_name }}"
                name="shared_{{ $field_name }}"
                value="1"
                class="shared-sameimages"
                data-field-ids="{{ $field_ids }}"
                data-primary="{{ $currentLang }}"
                {{ old("shared_{$field_name}") ? 'checked' : '' }}
            >
            <label for="shared_{{ $field_name }}" class="text-sm text-gray-700">
                Use the same images for all languages
            </label>

        </div>
    @endif

    {{-- Language inputs --}}
    @foreach($languagesToShow as $index => $lang)
        @php
            $langCode = $lang->code;
            $help = $help_texts[$langCode] ?? '';
            $value = old("{$field_name}.{$langCode}", $data?->{$field_name}[$langCode] ?? '');

            // Get attachment IDs for this language
            $imageIds = $data["fields_{$field->field_type_id}_{$field->id}"][$langCode] ?? [];

            // Map attachment IDs to URLs, remove nulls, reindex, convert to array
            $existingImages = collect($imageIds)
                ->map(fn($id) => $data['attachments'][$id]->url ?? null)
                ->filter()
                ->values()
                ->all();

            // Default JSON for existing images
            $defaultJson = json_encode($existingImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            // Final value for existing images input (prefer old submitted value)
            $existingImagesValue = old("existing_images_{$field_name}.{$langCode}", $defaultJson);

            // Determine tab panel visibility class
            // If tabs shown, show only first language block, hide others
            // If no tabs (only one language), always show
            $panelClass = $showTabs
                ? ($loop->first ? 'block' : 'hidden')
                : 'block';
        @endphp

        <div 
            class="code-tab-content {{ $panelClass }} tabpanel-{{$field_ids}}"
            aria-labelledby="tab-{{ $langCode }}-{{$field_ids}}"
            id="tab-panel-{{ $langCode }}-{{$field_ids}}"
        >
            <input
                type="file"
                class="kaz-file-input"
                data-preview="PreviewContainer_{{ $field_name }}_{{ $langCode }}"
                name="{{ $field_name }}[{{ $langCode }}]"
                id="{{ $field_name }}_{{ $langCode }}"
                {{ $acceptMime ? "accept=$acceptMime" : '' }}
                {{ $maxUploadCount > 1 ? 'multiple' : '' }}
                data-target-existing="#existingImages_{{ $field_name }}_{{ $langCode }}"
            >

            <input
                type="text"
                id="existingImages_{{ $field_name }}_{{ $langCode }}"
                name="existing_images_{{ $field_name }}[{{ $langCode }}]"
                value='{{ $existingImagesValue }}'
            >

            <input 
                type="text" 
                name="image_order_{{ $field_name }}[{{ $langCode }}]" 
                value="" 
            >

            <p class="mt-2 text-xs text-gray-500">Tip: You can drag and drop attachments to change the order.</p>

            @if($help)
                <p class="mt-1 text-sm text-gray-500">{{ $help }}</p>
            @endif

            @if($required)
                <div
                    id="req_{{ $field_name }}"
                    class="need_require text-sm mt-1 text-red-600"
                    data-required="{{ $required ? '1' : '0' }}"
                    data-maxupload="{{ $maxUploadCount }}"
                ></div>
            @endif

            @error("{$field_name}.{$langCode}")
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>
    @endforeach
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    /**
     * Toggle inheritance style for non-primary languages when shared images checkbox toggled
     */
    const toggleGroupInheritance = (checkbox) => {
        const fieldIds = checkbox.dataset.fieldIds;
        const primaryLang = checkbox.dataset.primary;
        const isChecked = checkbox.checked;

        // Toggle inheritance on panels for non-primary languages
        document.querySelectorAll(`.tabpanel-${fieldIds}`).forEach(panel => {
            const isPrimary = panel.id.endsWith(`${primaryLang}-${fieldIds}`);
            if (isPrimary) return;
            panel.classList.toggle('kaz-inherited', isChecked);
        });

        // Toggle inheritance on tabs for non-primary languages
        document.querySelectorAll(`#language-tabs button[data-field="${fieldIds}"]`).forEach(tab => {
            const isPrimary = tab.dataset.lang === primaryLang;
            if (isPrimary) return;
            tab.classList.toggle('tab-inherited', isChecked);
        });
    };

    // Initialize and bind checkboxes with class 'shared-sameimages'
    document.querySelectorAll('input.shared-sameimages').forEach(checkbox => {
        toggleGroupInheritance(checkbox);
        checkbox.addEventListener('change', () => toggleGroupInheritance(checkbox));
    });
});
</script>
@endpush
