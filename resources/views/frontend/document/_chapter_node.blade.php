@php
    // Build this node's path (starting from parentNoteUrl + this slug)
    //echo $currentNodePath = trim($contentType->slug[$locale] . '/' . $extraSegments[0] . $parentNoteUrl . '/' . $node->slug[$locale], '/');

    // Build the current requested path from extraSegments
    // This starts from index 1 to the end (excluding lang/contentType if needed)

    // Check if they match




$parentNoteUrl .= '/'.$node->slug[$locale];
$path = $contentType->slug[$locale] .'/'.$extraSegments[0].$parentNoteUrl;
$requestedPath = $contentType->slug[$locale] .'/'.trim(implode('/', $extraSegments), '/');
$isActive = ($path === $requestedPath);

$chapterUrl = localized_route(
    'dynamic.frontend',
    [
        'lang' => $locale,
        'path' => $path,
    ]
) . '?version=' . $versionId;
@endphp

<li data-id="{{ $node->id }}" data-parent-id="{{ $node->parent_id }}" 
    class="chapter-node">
    <div class="flex justify-between items-center rounded-lg px-3 py-2.5 transition-all duration-200 border border-transparent hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:border-blue-100 hover:shadow-sm {{ $isActive ? 'bg-blue-100 border-blue-300' : '' }}">
        <a href="{{ $chapterUrl }}" class="flex-1 cursor-pointer">
            <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full opacity-60 hover:opacity-100 hover:bg-blue-500 transition-all duration-200"></div>
                <span class="font-medium text-gray-800 hover:text-gray-900 transition-colors duration-200 text-sm leading-relaxed">
                    {{ $node->name[$locale] ?? 'Unnamed Chapter' }}
                </span>
            </div>
        </a>
    </div>
    
    @if ($node->children->isNotEmpty())
        <ul class="ml-6 mt-2 space-y-1 border-l border-gray-200 pl-4 relative">
            <div class="absolute -left-px top-0 bottom-0 w-px bg-gradient-to-b from-gray-200 via-gray-100 to-transparent"></div>
            @foreach ($node->children as $child)
                @include('frontend.document._chapter_node', [
                    'parentNoteUrl'=>$parentNoteUrl,
                    'node' => $child, 
                    'locale' => $locale, 
                    'versionId' => $versionId, 
                    'extraSegments' => $extraSegments,
                    'contentType' => $contentType
                ])
            @endforeach
        </ul>
    @endif
</li>
