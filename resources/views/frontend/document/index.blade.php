@extends('frontend.layouts.kazcms')

@section('content')
@php
    $locale = app()->getLocale();
    $parentNoteUrl = '';
@endphp

<div class="flex h-screen">
    <!-- Sidebar: Chapter Tree -->
    <div class="w-80 bg-white border-r border-gray-200 shadow-sm">
        <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Chapters</h3>

            <!-- Version Select (只读，不可操作) -->
            <select  class="w-full border border-gray-200 rounded-lg px-3 py-2.5 text-sm text-gray-700 bg-gray-100" >
                @foreach ($versions as $version)
                    <option value="{{ $version->id }}" {{ $version->id == $versionId ? 'selected' : '' }}>
                        Version {{ $version->version }}
                    </option>
                @endforeach
            </select>
        </div>

        <!-- Chapter Tree -->
        <div class="flex-1 overflow-y-auto p-4">
            <ul class="space-y-1">
                @foreach ($tree as $node)
                    @include('frontend.document._chapter_node', ['parentNoteUrl'=>$parentNoteUrl,'node' => $node, 'locale' => $locale, 'contentType' => $contentType,'extraSegments'=>$extraSegments])
                @endforeach
            </ul>
        </div>
    </div>

    <!-- Content Display -->
    <div class="flex-1 flex flex-col bg-white">
        <div class="px-8 py-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                {{ $chapter->name[$locale] ?? 'Unnamed Chapter' }}
            </h2>
        </div>

        <!-- Content Display Area -->
        <div class="flex-1 p-8 overflow-auto whitespace-pre-wrap text-gray-800 bg-gray-50/50 border border-gray-200 rounded-xl">
            {!! nl2br(e($chapter->content[$locale] ?? '')) !!}
        </div>
    </div>
</div>
@endsection
