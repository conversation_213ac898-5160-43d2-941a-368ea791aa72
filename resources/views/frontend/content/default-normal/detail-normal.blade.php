@extends('frontend.layouts.kazcms')

@section('title', $title ?? __('Content'))

@section('content')
@php
    $locale = app()->getLocale();
@endphp

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-5xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="p-8 sm:p-12">
                {{-- Render each field --}}
                @foreach($fields as $index => $field)
                    @php
                        $fieldType = $fieldTypes[$field->field_type_id]['related_table'];
                        $field_name = "fields_{$field->field_type_id}_{$field->id}";
                        $isLast = $loop->last;
                    @endphp

                    <div class="@if(!$isLast) mb-10 pb-8 border-b border-gray-100 @else mb-0 @endif">
                        {{-- Render content by field type --}}
                        @switch($fieldType)

                            @case('text')
                                @php
                                    $prefix = optional(json_decode($field->prefix_text_json, true))[$locale] ?? '';
                                    $suffix = optional(json_decode($field->suffix_text_json, true))[$locale] ?? '';
                                    $value = $data[$field_name][$locale] ?? '';
                                @endphp

                                @if($value)
                                    <div class="flex items-start space-x-3 group">
                                        @if($prefix)
                                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                                {{ $prefix }}
                                            </span>
                                        @endif
                                        <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
                                            {!! nl2br(e($value)) !!}
                                        </div>
                                        @if($suffix)
                                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                                {{ $suffix }}
                                            </span>
                                        @endif
                                    </div>
                                @endif
                                @break

                            @case('textarea')
                                @php
                                    $value = $data[$field_name][$locale] ?? '';
                                @endphp

                                @if($value)
                                    <div class="relative">
                                        <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono">
                                            TEXT
                                        </div>
                                        <div class="text-gray-800 text-sm font-mono leading-relaxed whitespace-pre-wrap bg-gradient-to-br from-gray-50 to-slate-50 p-6 border border-gray-200 rounded-xl shadow-inner">
                                            {!! nl2br(e($value)) !!}
                                        </div>
                                    </div>
                                @endif
                                @break

                            @case('file')
                                @php
                                    $image_ids = $data[$field_name][$locale] ?? [];
                                    $images = collect($image_ids)
                                        ->map(fn($id) => $data['attachments'][$id]->url ?? null)
                                        ->filter()
                                        ->values()
                                        ->all();
                                @endphp

                                @if(count($images))
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wide flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            Images ({{ count($images) }})
                                        </h4>
                                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                            @foreach($images as $img)
                                                <div class="group relative aspect-square bg-gray-100 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                                                    <img src="{{ $img }}" alt="Image" class="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300">
                                                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center py-8 text-gray-400">
                                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <p class="text-sm">No images available</p>
                                    </div>
                                @endif
                                @break

                            @case('codes')
                                @php
                                    $htmlCode = json_decode($field->html_code_json, true)[$locale] ?? '';
                                    $isEnabled = $field->is_enabled ?? true;
                                @endphp

                                @if($isEnabled && $htmlCode)
                                    <div class="relative">
                                        <div class="absolute top-4 right-4 text-xs text-emerald-600 font-mono bg-emerald-100 px-2 py-1 rounded">
                                            HTML
                                        </div>
                                        <div class="prose prose-sm max-w-none text-gray-700 bg-gradient-to-br from-emerald-50 to-teal-50 p-6 border border-emerald-200 rounded-xl shadow-inner">
                                            {!! $htmlCode !!}
                                        </div>
                                    </div>
                                @elseif($isEnabled)
                                    <div class="text-center py-6 text-red-400 bg-red-50 rounded-xl border border-red-200">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <p class="text-sm font-medium">No code available</p>
                                    </div>
                                @endif
                                @break

                            @case('categories')
                                @php
                                    $selectedCategories = $data[$field_name]['all'] ?? [];
                                @endphp

                                @if(is_array($selectedCategories) && count($selectedCategories))
                                    <div class="space-y-3">
                                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wide flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                                            </svg>
                                            Categories
                                        </h4>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($selectedCategories as $cat_id)
                                                <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-800 bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 rounded-full hover:from-purple-200 hover:to-pink-200 transition-all duration-200 shadow-sm">
                                                    <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ $data['categories'][$cat_id]['name'][$locale] ?? 'Untitled Category' }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center py-6 text-gray-400 bg-gray-50 rounded-xl border border-gray-200">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                        <p class="text-sm">No categories selected</p>
                                    </div>
                                @endif
                                @break

                            @case('options')
                                @php
                                    $dataValue = $data[$field_name]['all'] ?? [];
                                    $selected = is_array($dataValue) ? $dataValue : [$dataValue];
                                    $options = collect(json_decode($field->options, true) ?? [])
                                        ->filter(fn($opt) => $opt['enabled'] ?? false)
                                        ->sortBy('sort_order');
                                    $prefixText = json_decode($field->prefix_text_json, true)[$locale] ?? '';
                                    $suffixText = json_decode($field->suffix_text_json, true)[$locale] ?? '';
                                @endphp

                                @if(count($selected) && $selected[0])
                                    <div class="space-y-4">
                                        @if($prefixText)
                                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                                                <p class="text-sm text-blue-700 font-medium">{{ $prefixText }}</p>
                                            </div>
                                        @endif

                                        <div class="grid gap-3">
                                            @foreach($selected as $val)
                                                @php
                                                    $matched = $options->firstWhere('value', $val);
                                                    $labelText = $matched['label'][$locale] ?? $val;
                                                @endphp
                                                <div class="flex items-center p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg hover:from-indigo-100 hover:to-blue-100 transition-all duration-200 shadow-sm">
                                                    <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mr-4"></div>
                                                    <span class="text-gray-800 font-medium">{{ $labelText }}</span>
                                                </div>
                                            @endforeach
                                        </div>

                                        @if($suffixText)
                                            <div class="bg-slate-50 border-l-4 border-slate-400 p-4 rounded-r-lg">
                                                <p class="text-sm text-slate-600 font-medium">{{ $suffixText }}</p>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                @break

                            @default
                                @php
                                    dd($field);
                                @endphp 
                                <div class="text-center py-8 text-amber-600 bg-amber-50 rounded-xl border border-amber-200">
                                    <svg class="w-10 h-10 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <p class="text-sm font-medium">Unknown field type: {{ $field->field_type_id }}</p>
                                </div>
                        @endswitch
                    </div>
                @endforeach


            </div>
        </div>
    </div>
</div>
@endsection


你知道我的fields有好几种，我现在想让你根据这个 view,写个temlate service 
每种fields 写一个单独的方法，,还有一个总的方法， 当我把整个fields list,传进去的时候。他会以
循环输出模板内容，fields list的field key是 [field_type_id-field_id]这样。
还会有 "extras" => array:2 [▼
    "attachments" => Illuminate\Support\Collection {#762 ▼
      #items: array:4 [▼
        5 => {#765 ▼
          +"id": 5
          +"file_path": "uploads/2025/08/13/fX8D1vXKLpriuujPeVSEGmbB18afp6hDk5DCyfi1.webp"
          +"file_name": "fX8D1vXKLpriuujPeVSEGmbB18afp6hDk5DCyfi1.webp"
          +"file_ext": "webp"
          +"original_name": "existing_1.webp"
          +"title": null
          +"file_type": "image/webp"
          +"file_size": 25720
          +"user_id": 1
          +"created_at": "2025-08-13 16:33:46"
          +"updated_at": "2025-08-13 16:33:46"
          +"url": "http://localhost:8000/storage/uploads/2025/08/13/fX8D1vXKLpriuujPeVSEGmbB18afp6hDk5DCyfi1.webp"
        }
        6 => {#756 ▶}
        7 => {#767 ▶}
        8 => {#757 ▶}
      ]
      #escapeWhenCastingToString: false
    }
    "categories" => Illuminate\Support\Collection {#751 ▼
      #items: array:1 [▼
        7 => array:5 [▼
          "id" => 7
          "name" => array:4 [▶]
          "final_category" => false
          "parent_path_json" => array:3 [▶]
          "parents" => array:3 [▼
            1 => App\Models\Category {#738 ▶}
            3 => App\Models\Category {#737 ▼
              #connection: "mysql"
              #table: "categories"
              #primaryKey: "id"
              #keyType: "int"
              +incrementing: true
              #with: []
              #withCount: []
              +preventsLazyLoading: false
              #perPage: 15
              +exists: true
              +wasRecentlyCreated: false
              #escapeWhenCastingToString: false
              #attributes: array:4 [▶]
              #original: array:4 [▼
                "id" => 3
                "name" => "{"en-us": "Clothing", "fr-ca": "Vêtements", "kz-kz": "Киім", "zh-cn": "服装"}"
                "final_category" => 1
                "parent_path_json" => "[1]"
              ]
              #changes: []
              #casts: array:7 [▶]
              #classCastCache: []
              #attributeCastCache: []
              #dateFormat: null
              #appends: []
              #dispatchesEvents: []
              #observables: []
              #relations: []
              #touches: []
              +timestamps: true
              +usesUniqueIds: false
              #hidden: []
              #visible: []
              #fillable: array:11 [▶]
              #guarded: array:1 [▶]
            }
            5 => App\Models\Category {#736 ▶}
          ]
        ]
      ]
      #escapeWhenCastingToString: false
    }
  ]
  存分类和附件。这是data输出的模板，后面还会有forml输出模板（那个后面再说）,每个field得有fields name用laravel view 注释，存一个fielld name(只存当前语言的)
我传进的的参数是fiels
当我把field_type_id传进去的时候。会输出模板的code,一个简单的code就成，因为用户要自己选择fields并生成模板code.
每个field 有个简单的div就成，用户肯定还会息AI再设计一次。
$fieldCategoriesResponse = $fc->getFieldsByContentTypeDynamic($contentTypeId);
dd($fieldCategoriesResponse);
fields data是这样结构的
data: "{"1-1":{"id":1,"field_type_id":1,"content_type_id":4,"label_json":"{\"en-us\": \"Product Name\", \"fr-ca\": \"Nom du produit\", \"kz-kz\": \"\u04e8\u043d\u0456\u043c\u043d\u0456\u04a3 \u0430\u0442\u0430\u0443\u044b\", \"zh-cn\": \"\u4ea7\u54c1\u540d\u79f0\"}","is_seo":0,"seo_type":null,"subtype":"text","placeholder_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","default_value_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","prefix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","suffix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","is_required":1,"min_length":null,"max_length":null,"display_color":"#3b82f6","help_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","created_at":"2025-08-12 18:31:37","updated_at":"2025-08-12 18:31:37","sort_order":5},"1-2":{"id":2,"field_type_id":1,"content_type_id":4,"label_json":"{\"en-us\": \"slug\", \"fr-ca\": \"slug\", \"kz-kz\": \"slug\", \"zh-cn\": \"slug\"}","is_seo":1,"seo_type":"slug","subtype":"text","placeholder_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","default_value_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","prefix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","suffix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","is_required":0,"min_length":null,"max_length":null,"display_color":"#3b82f6","help_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","created_at":"2025-08-12 18:32:08","updated_at":"2025-08-12 18:32:08","sort_order":10},"1-3":{"id":3,"field_type_id":1,"content_type_id":4,"label_json":"{\"en-us\": \"Price\", \"fr-ca\": \"Prix\", \"kz-kz\": \"\u0411\u0430\u0493\u0430\u0441\u044b\", \"zh-cn\": \"\u4ef7\u683c\"}","is_seo":0,"seo_type":null,"subtype":"number","placeholder_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","default_value_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","prefix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","suffix_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","is_required":0,"min_length":null,"max_length":null,"display_color":"#3b82f6","help_text_json":"{\"en-us\": null, \"fr-ca\": null, \"kz-kz\": null, \"zh-cn\": null}","created_at":"2025-08-12 18:33:50","updated_at":"2025-08-12 18:38:46","sort_order":15}}

不急着生成的代码，你明白我的意思了吗