@php
    $locale = app()->getLocale();
@endphp

{{-- Text Field --}}
@if($field->field_type === 'text')
    @php
        $field_name = "fields_{$field->field_type_id}_{$field->id}";
        $prefix = optional(json_decode($field->prefix_text_json, true))[$locale] ?? '';
        $suffix = optional(json_decode($field->suffix_text_json, true))[$locale] ?? '';
        $value = $data[$field_name][$locale] ?? '';
    @endphp

    <div class="flex mb-4">
        <div class="flex-1">
            <div class="flex items-start space-x-2">
                @if($prefix)
                    <span class="text-gray-500 text-sm mt-1">{{ $prefix }}</span>
                @endif
                <div class="text-gray-900 text-sm whitespace-pre-line">
                    {!! nl2br(e($value)) !!}
                </div>
                @if($suffix)
                    <span class="text-gray-500 text-sm mt-1">{{ $suffix }}</span>
                @endif
            </div>
        </div>
    </div>
@endif

{{-- Textarea Field --}}
@if($field->field_type === 'textarea')
    @php
        $field_name = "fields_{$field->field_type_id}_{$field->id}";
        $value = $data[$field_name][$locale] ?? '';
    @endphp

    <div class="flex mb-4">
        <div class="flex-1">
            <div class="text-gray-900 text-sm font-mono whitespace-pre-wrap bg-gray-50 p-3 border border-gray-200 rounded-md">
                {!! nl2br(e($value)) !!}
            </div>
        </div>
    </div>
@endif

{{-- File/Image Field --}}
@if($field->field_type === 'file')
    @php
        $field_name = "fields_{$field->field_type_id}_{$field->id}";
        $shared = $data["shared_{$field_name}"] ?? false;
        $image_ids = $data[$field_name][$locale] ?? [];

        if ($shared && empty($image_ids)) {
            $image_ids = $data[$field_name][app()->getLocale()] ?? [];
        }

        $images = collect($image_ids)
            ->map(fn($id) => $data['attachments'][$id]->url ?? null)
            ->filter()
            ->values()
            ->all();
    @endphp

    <div class="mb-4">
        @if(count($images))
            <div class="flex flex-wrap gap-2">
                @foreach($images as $img)
                    <div class="w-24 h-24 border rounded overflow-hidden">
                        <img src="{{ $img }}" alt="Image" class="object-cover w-full h-full">
                    </div>
                @endforeach
            </div>
        @else
            <p class="text-sm text-gray-400">No image available.</p>
        @endif
    </div>
@endif

{{-- HTML Code Field --}}
@if($field->field_type === 'code')
    @php
        $htmlCode = json_decode($field->html_code_json, true)[$locale] ?? '';
        $isEnabled = $field->is_enabled ?? true;
    @endphp

    @if($isEnabled)
        <div class="mb-6">
            @if($htmlCode)
                <div class="prose max-w-full text-sm text-gray-700">
                    {!! $htmlCode !!}
                </div>
            @else
                <p class="text-sm text-red-400 italic">[No code available for current language]</p>
            @endif
        </div>
    @endif
@endif

{{-- Category Field --}}
@if($field->field_type === 'category')
    @php
        $field_name = "fields_{$field->field_type_id}_{$field->id}";
        $selectedCategories = $data[$field_name]['all'] ?? [];
    @endphp

    <div class="mb-4">
        <div id="selectedCategories_{{ $field->field_type_id }}_{{ $field->id }}" class="@if(empty($selectedCategories)) hidden @endif flex-1 ml-4 text-lg text-gray-600 truncate">
            @if(is_array($selectedCategories))
                @foreach($selectedCategories as $cat_id)
                    <span class="inline-flex items-center px-3 py-1 mr-2 mb-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                        {{ $data['categories'][$cat_id]['name'][$locale] ?? 'Untitled Category' }}
                    </span>
                @endforeach
            @else
                <p class="text-sm text-gray-400">No categories selected.</p>
            @endif
        </div>
    </div>
@endif

{{-- Options Field --}}
@if($field->field_type === 'options')
    @php
        $field_name = "fields_{$field->field_type_id}_{$field->id}";
        $dataValue = $data[$field_name]['all'] ?? [];
        $selected = is_array($dataValue) ? $dataValue : [$dataValue];

        $options = collect(json_decode($field->options, true) ?? [])
            ->filter(fn($opt) => $opt['enabled'] ?? false)
            ->sortBy('sort_order');

        $prefixText = json_decode($field->prefix_text_json, true)[$locale] ?? '';
        $suffixText = json_decode($field->suffix_text_json, true)[$locale] ?? '';
    @endphp

    <div class="mb-6">
        @if($prefixText)
            <p class="text-sm text-gray-600 mb-2">{{ $prefixText }}</p>
        @endif

        <div class="space-y-1">
            @foreach($selected as $val)
                @php
                    $matched = $options->firstWhere('value', $val);
                    $labelText = $matched['label'][$locale] ?? $val;
                @endphp
                <div class="text-base text-gray-800">
                    • {{ $labelText }}
                </div>
            @endforeach
        </div>

        @if($suffixText)
            <p class="text-sm text-gray-600 mt-3">{{ $suffixText }}</p>
        @endif
    </div>
@endif
