{{-- kazview:5 --}} 
@php
    $locale = app()->getLocale();
@endphp

@forelse ($contents['data'] as $dataId => $data)
{{-- field name: Product Name --}}
@php
    $value = isset($data['1-1']) && isset($data['1-1']->data) ? json_decode($data['1-1']->data,true)[$locale] ?? '' : '';
    $prefix = isset($fields['1-1']['prefix_text_json']) ? json_decode($fields['1-1']['prefix_text_json'], true)[$locale] ?? '' : '';
    $suffix = isset($fields['1-1']['suffix_text_json']) ? json_decode($fields['1-1']['suffix_text_json'], true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="flex items-start space-x-3 group">
    @if($prefix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $prefix }}
        </span>
    @endif
    <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
        {!! nl2br(e($value)) !!}
    </div>
    @if($suffix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $suffix }}
        </span>
    @endif
</div>
@endif

{{-- field name: slug --}}
@php
    $value = isset($data['1-2']) && isset($data['1-2']->data) ? json_decode($data['1-2']->data,true)[$locale] ?? '' : '';
    $prefix = isset($fields['1-2']['prefix_text_json']) ? json_decode($fields['1-2']['prefix_text_json'], true)[$locale] ?? '' : '';
    $suffix = isset($fields['1-2']['suffix_text_json']) ? json_decode($fields['1-2']['suffix_text_json'], true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="flex items-start space-x-3 group">
    @if($prefix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $prefix }}
        </span>
    @endif
    <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
        {!! nl2br(e($value)) !!}
    </div>
    @if($suffix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $suffix }}
        </span>
    @endif
</div>
@endif

{{-- field name: SKU --}}
@php
    $value = isset($data['1-5']) && isset($data['1-5']->data) ? json_decode($data['1-5']->data,true)[$locale] ?? '' : '';
    $prefix = isset($fields['1-5']['prefix_text_json']) ? json_decode($fields['1-5']['prefix_text_json'], true)[$locale] ?? '' : '';
    $suffix = isset($fields['1-5']['suffix_text_json']) ? json_decode($fields['1-5']['suffix_text_json'], true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="flex items-start space-x-3 group">
    @if($prefix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $prefix }}
        </span>
    @endif
    <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
        {!! nl2br(e($value)) !!}
    </div>
    @if($suffix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $suffix }}
        </span>
    @endif
</div>
@endif

{{-- field name: Title for SEO --}}
@php
    $value = isset($data['1-6']) && isset($data['1-6']->data) ? json_decode($data['1-6']->data,true)[$locale] ?? '' : '';
    $prefix = isset($fields['1-6']['prefix_text_json']) ? json_decode($fields['1-6']['prefix_text_json'], true)[$locale] ?? '' : '';
    $suffix = isset($fields['1-6']['suffix_text_json']) ? json_decode($fields['1-6']['suffix_text_json'], true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="flex items-start space-x-3 group">
    @if($prefix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $prefix }}
        </span>
    @endif
    <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
        {!! nl2br(e($value)) !!}
    </div>
    @if($suffix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $suffix }}
        </span>
    @endif
</div>
@endif

{{-- field name: Description for SEO --}}
@php
    $value = isset($data['1-8']) && isset($data['1-8']->data) ? json_decode($data['1-8']->data,true)[$locale] ?? '' : '';
    $prefix = isset($fields['1-8']['prefix_text_json']) ? json_decode($fields['1-8']['prefix_text_json'], true)[$locale] ?? '' : '';
    $suffix = isset($fields['1-8']['suffix_text_json']) ? json_decode($fields['1-8']['suffix_text_json'], true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="flex items-start space-x-3 group">
    @if($prefix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $prefix }}
        </span>
    @endif
    <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
        {!! nl2br(e($value)) !!}
    </div>
    @if($suffix)
        <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
            {{ $suffix }}
        </span>
    @endif
</div>
@endif

{{-- field name: Product Description --}}
@php
    $value = isset($data['2-2']) && isset($data['2-2']->data) ? json_decode($data['2-2']->data,true)[$locale] ?? '' : '';
@endphp

@if($value)
<div class="relative">
    <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono">
        TEXT
    </div>
    <div class="text-gray-800 text-sm font-mono leading-relaxed whitespace-pre-wrap bg-gradient-to-br from-gray-50 to-slate-50 p-6 border border-gray-200 rounded-xl shadow-inner">
        {!! nl2br(e($value)) !!}
    </div>
</div>
@endif

{{-- field name: Product Images --}}
@php
    $image_ids = isset($data['3-1']) && isset($data['3-1']->data) ? json_decode($data['3-1']->data,true)[$locale] ?? [] : [];
    //dd($image_ids,$contents['extras']['attachments']);
    $images = collect($image_ids)
        ->map(fn($id) => $contents['extras']['attachments'][$id]->url ?? null)
        ->filter()
        ->values()
        ->all();
        //dd($images);
@endphp

@if(count($images))
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
    @foreach($images as $img)
        <div class="group relative aspect-square bg-gray-100 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
            <img src="{{ $img }}" alt="Image" class="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300">
        </div>
    @endforeach
</div>
@else
<div class="text-center py-8 text-gray-400">
    <p class="text-sm">No images available</p>
</div>
@endif

{{-- field name: Category --}}
@php
    $htmlCode = isset($fields['4-1']['html_code_json']) ? json_decode($fields['4-1']['html_code_json'], true)[$locale] ?? '' : '';
    $isEnabled = $fields['4-1']['is_enabled'] ?? true;
@endphp

@if($isEnabled && $htmlCode)
<div class="prose prose-sm max-w-none text-gray-700 bg-gradient-to-br from-emerald-50 to-teal-50 p-6 border border-emerald-200 rounded-xl shadow-inner">
    {!! $htmlCode !!}
</div>
@endif

@empty
    <p>No content available.</p>
@endforelse
