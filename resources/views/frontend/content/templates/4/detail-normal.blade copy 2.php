@extends('frontend.layouts.kazcms')

@section('title', $data['fields_1_1'][app()->getLocale()] ?? __('Content'))

@section('content')
@php
    $locale = app()->getLocale();
    $attachments = $data['attachments'];
    $categories = $data['categories'];
    $firstImage = $data['attachments']->first();
@endphp
<div class="bg-gray-100 max-w-5xl p-3 mx-auto">
    <nav aria-label="breadcrumb">
        <div class="bg-gray-50 rounded-lg px-4 py-3">
            <ol class="flex items-center space-x-1 text-sm">
                @php
                    $locale = app()->getLocale();
                @endphp

                <!-- 首页 -->
                <li class="flex items-center">
                    <a href="/" class="flex items-center text-gray-600 hover:text-gray-900 hover:bg-white px-3 py-1.5 rounded-full transition-all duration-200">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        {{ __('Home') }}
                    </a>
                </li>

                <!-- 当前内容类型 -->
                @if(!empty($contentType))
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        <a href="{{ localized_route('dynamic.frontend', ['lang' => $locale, 'path' => $contentType->slug[$locale]]) }}" 
                           class="flex items-center text-gray-600 hover:text-gray-900 hover:bg-white px-3 py-1.5 rounded-full transition-all duration-200">
                            {{ $contentType->name[$locale] ?? '' }}
                        </a>
                    </li>
                @endif

                @php
                    $categoryCount = count($categories ?? []);
                @endphp

                @foreach($categories ?? [] as $index => $cat)
                    <!-- 分隔符 -->
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </li>

                    <li class="flex items-center">
                        <a href="{{ $cat['url'] ?? '#' }}" class="flex items-center text-gray-600 hover:text-gray-900 hover:bg-white px-3 py-1.5 rounded-full transition-all duration-200">
                            {{ $cat['name'][$locale] ?? '' }}
                        </a>
                    </li>
                @endforeach

                <!-- 分隔符 -->
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </li>

                <!-- 当前产品标题 -->
                <li class="flex items-center">
                    <span class="flex items-center  text-blue-900 px-3 py-1.5  font-medium">
                        {{ $data['fields_1_1'][$locale] ?? '' }}
                    </span>
                </li>
            </ol>
        </div>
    </nav>
</div>


<!-- Product Detail Section -->
<div class="max-w-6xl mx-auto px-6 py-16">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
        <!-- Product Images -->
        <div class="space-y-4">
            <!-- Main Image -->
            <div class="aspect-square rounded-2xl overflow-hidden bg-gray-50">
                <img id="mainImage" 
                     src="{{ $firstImage->url ?? 'https://via.placeholder.com/800' }}" 
                     alt="{{ $data['fields_1_1'][$locale] ?? '' }}" 
                     class="w-full h-full object-cover">
            </div>
            
            <!-- Thumbnail Images -->
            <div class="flex space-x-3">
                @foreach($attachments as $attachment)
                    <button onclick="changeMainImage('{{ $attachment->url }}')" 
                            class="w-20 h-20 rounded-lg overflow-hidden bg-gray-50 border-2 border-transparent hover:border-gray-300">
                        <img src="{{ $attachment->url }}" 
                             alt="{{ $data['fields_1_1'][$locale] ?? '' }}" 
                             class="w-full h-full object-cover">
                    </button>
                @endforeach
            </div>
        </div>

        <!-- Product Information -->
        <div class="flex flex-col justify-center space-y-8">
            <!-- Category -->
            <div class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                {{ $categories[array_key_first($categories)]['name'][$locale] ?? '' }}
            </div>

            <!-- Product Name -->
            <h1 class="text-4xl lg:text-5xl font-light text-gray-900 leading-tight">
                {{ $data['fields_1_1'][$locale] ?? '' }}
            </h1>

            <!-- Price -->
            <div class="text-3xl font-light text-gray-900">
                ${{ $data['fields_1_3'][$locale] ?? '' }}
            </div>

            <!-- Short Description -->
            <p class="text-lg text-gray-600 leading-relaxed">
                {{ $data['fields_2_1'][$locale] ?? '' }}
            </p>

            <!-- Stock Status -->
            <div class="flex items-center space-x-4 text-sm">
                <span class="text-green-600">● In Stock ({{ $data['fields_1_4'][$locale] ?? '' }})</span>
                <span class="text-gray-400">SKU: {{ $data['fields_1_5'][$locale] ?? '' }}</span>
            </div>

            <!-- Action Button -->
            <button class="w-full bg-black text-white py-4 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                Add to Cart
            </button>
        </div>
    </div>

    <!-- Product Description -->
    <div class="mt-24 max-w-3xl">
        <h2 class="text-2xl font-light text-gray-900 mb-8">Details</h2>
        <div class="text-gray-600 leading-relaxed text-lg">
            {{ $data['fields_2_2'][$locale] ?? '' }}
        </div>
    </div>
</div>

<script>
function changeMainImage(imageSrc) {
    document.getElementById('mainImage').src = imageSrc;
    
    // Update active thumbnail border
    const thumbnails = document.querySelectorAll('.space-x-3 button');
    thumbnails.forEach(btn => {
        btn.classList.remove('border-black');
        btn.classList.add('border-transparent');
    });
    
    // Find and highlight the clicked thumbnail
    event.target.closest('button').classList.remove('border-transparent');
    event.target.closest('button').classList.add('border-black');
}
</script>
@endsection
