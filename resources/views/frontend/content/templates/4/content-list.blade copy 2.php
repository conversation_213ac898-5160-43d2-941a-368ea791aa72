@php
    $locale = app()->getLocale();
    $selectedStatuses = request('status', ['published']);
    if (!is_array($selectedStatuses)) {
        $selectedStatuses = [$selectedStatuses];
    }
@endphp

{{-- Content List Section --}}
<div class="bg-white rounded-xl shadow-lg border border-gray-200">
    <div class="p-8 border-b border-gray-200 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-t-xl">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            {{ $contentType->name[app()->getLocale()] ?? $contentType->name['en-us'] ?? '' }}
        </h1>
        <p class="text-indigo-100 mt-2 text-lg">      {{ $contentType->description[app()->getLocale()] ?? $contentType->description['en-us'] ?? '' }}
        </p>
    </div>

    {{-- Filter Section --}}
    <div class="p-6 border-b border-gray-200 bg-gray-50">
        <button id="filterToggle" class="inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg shadow-md transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            Filters
            <svg id="filterArrow" class="w-4 h-4 ml-2 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>

        {{-- Filter Panel --}}
        <div id="filterPanel" class="hidden mt-6 p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {{-- Status Filter --}}
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Publication Status</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="status[]" value="published" 
                                   {{ in_array('published', $selectedStatuses) ? 'checked' : '' }}
                                   class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-600">Published</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="status[]" value="draft" 
                                   {{ in_array('draft', $selectedStatuses) ? 'checked' : '' }}
                                   class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-600">Draft</span>
                        </label>
                    </div>
                </div>

                {{-- Start Date --}}
                <div>
                    <label for="start_date" class="block text-sm font-semibold text-gray-700 mb-3">Start Date</label>
                    <input type="date" name="start_date" id="start_date" 
                           value="{{ request('start_date') }}"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                {{-- End Date --}}
                <div>
                    <label for="end_date" class="block text-sm font-semibold text-gray-700 mb-3">End Date</label>
                    <input type="date" name="end_date" id="end_date" 
                           value="{{ request('end_date') }}"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                {{-- Filter Actions --}}
                <div class="md:col-span-3 flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button type="button" onclick="window.location.href='{{ url()->current() }}'" 
                            class="px-6 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200">
                        Clear Filters
                    </button>
                    <button type="submit" 
                            class="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium shadow-md transition-colors duration-200">
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    {{-- Content Grid --}}
    {{-- Content Grid --}}
<div class="p-8">
    @forelse ($contents['data'] as $dataId => $fields)
        @php

            // Slug
            $slugFieldId = $contents['slugFieldIdList'][0] ?? null;
            $dataSlugString = '';
            if (isset($fields[join('-', $slugFieldId)])) {
                $dataSlug = $fields[join('-', $slugFieldId)]->data ?? '{}';
                $decoded = json_decode($dataSlug, true);
                $dataSlugString = $decoded[$locale] ?? '';
            }

            // Title field (assume field_type_id 1 = text/title)
            $titleField = null;
            foreach ($fields as $f) {
   
                if ($f->field_type_id == 1) {
                //if ($f->field_type_id == 1 && ($f->subtype == 'text') && ($f->seo_type  == null)) {
                    $titleField = $f;
                    break;
                }
            }
            $titleData = json_decode($titleField->data ?? '', true) ?? [];
            $title = $titleData[$locale] ?? 'No Title';

            // Image field (assume field_type_id 2 = image)
            $imageField = null;
            foreach ($fields as $f) {
                if ($f->field_type_id == 2) {
                    $imageField = $f;
                    break;
                }
            }
            $imageData = json_decode($imageField->data ?? '', true) ?? [];
            $imageSrc = $imageData[$locale] ?? 'https://via.placeholder.com/400x300?text=No+Image';
        @endphp

        @if ($loop->first)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        @endif

        <div class="group cursor-pointer">
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 hover:border-indigo-300">
                {{-- Image --}}
                <div class="aspect-[4/3] bg-gray-100 flex items-center justify-center overflow-hidden">
                    <img src="{{ $imageSrc }}" alt="{{ $title }}" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                </div>

                {{-- Content Info --}}
                <div class="p-6">
                    <a href="{{ localized_route('dynamic.frontend', ['lang' => $locale, 'path' => $contentType->slug[$locale] . '/'.$dataSlugString]) }}" class="hover:underline">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-indigo-600 transition-colors duration-200">
                            dd{{ Str::limit(strip_tags($title), 80) }}
                        </h3>
                    </a>

                    <div class="flex items-center justify-between mt-4">
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ \Carbon\Carbon::parse($titleField?->updated_at ?? now())->diffForHumans() }}</span>
                        </div>
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <span class="text-xs font-medium text-indigo-600 bg-indigo-50 px-2 py-1 rounded-full">
                                View Details
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if ($loop->last)
            </div>
        @endif
    @empty
        <div class="text-center py-16">
            <div class="max-w-md mx-auto">
                <svg class="w-20 h-20 text-gray-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-400 mb-2">No Content Found</h3>
                <p class="text-gray-300">Try adjusting your filters or create new content to get started</p>
            </div>
        </div>
    @endforelse
</div>



    {{-- Pagination --}}
    <div class="px-8 py-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
        <div class="flex items-center justify-between">
            <div class="text-sm font-medium text-gray-600">
                Showing content results
            </div>
            <div class="pagination-wrapper">
                {{ $paginator->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>

{{-- JavaScript for Filter Toggle --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterToggle = document.getElementById('filterToggle');
    const filterPanel = document.getElementById('filterPanel');
    const filterArrow = document.getElementById('filterArrow');

    filterToggle.addEventListener('click', function() {
        const isHidden = filterPanel.classList.contains('hidden');
        
        if (isHidden) {
            filterPanel.classList.remove('hidden');
            filterArrow.style.transform = 'rotate(180deg)';
        } else {
            filterPanel.classList.add('hidden');
            filterArrow.style.transform = 'rotate(0deg)';
        }
    });
});
</script>