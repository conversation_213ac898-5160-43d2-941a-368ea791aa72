<div class="bg-white rounded-xl shadow-lg border border-blue-100 sticky top-6">
    <div class="p-6">
        <h2 class="text-xl font-bold text-blue-900 mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-bar-stacked-icon lucide-chart-bar-stacked"><path d="M11 13v4"/><path d="M15 5v4"/><path d="M3 3v16a2 2 0 0 0 2 2h16"/><rect x="7" y="13" width="9" height="4" rx="1"/><rect x="7" y="5" width="12" height="4" rx="1"/></svg>
            Categories
        </h2>

        {{-- Display category tree grouped by root ID --}}
        @foreach ($fieldCategories as $field)
    @php
    //dd($field);
    $selectedCategoryIds = $selectedCategoryFieldIds[$field['id']]['category_ids'] ?? [];
        $lang = app()->getLocale();

        // 找到这个字段对应的分类列表
        $fieldRootId =  $field['category_id'];

        // 获取这个 rootId 对应的分类集合（是一个子 Collection）
        $categories = $categoryTree[$fieldRootId] ?? collect();

        // 找 root 分类（parent_id 为 0 或 id == rootId）
        $root = $categories->firstWhere('id', $fieldRootId);
        $children = $categories->where('parent_id', $fieldRootId);
        //$categoriesByPath = $fieldCategoryMap[$field['id']]['categories'] ?? [];
        $selectedIds = $selectedCategoryFieldIds[$field['id']]['category_ids'] ?? [];
    @endphp

    @if ($root)
        <div class="mb-6 last:mb-0">
            <div class="bg-blue-50 rounded-lg p-3 mb-3 border-l-4 border-blue-500">
                <div class="font-semibold text-blue-800 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span data-slug="{{ Str::slug(json_decode($field['label_json'], true)[$lang] ?? 'Field Label') }}">{{ json_decode($field['label_json'], true)[$lang] ?? '' }}</span>
                </div>
            </div>

            @if ($children->isNotEmpty())
                <ul class="space-y-2">
                    @foreach ($children as $child)
                        @php
                            $fieldSlug = Str::slug(json_decode($field['label_slug_json'], true)[$lang] ?? '');
                            $fullSlug = $child['slug'][$lang] ?? '';
                            $hasChildren = !empty(data_get($tree, "{$field['id']}.category_tree.{$child['id']}.children"));

                            
                        @endphp

                        <li class="flex items-center space-x-2">
                           

                            <a href="#"
                            id="category_a_{{ $child['id'] }}"
                            data-field="{{ $fieldSlug }}"

                            data-slug="{{ $fullSlug }}"
                            class="kazcms-left-category-a flex-1 text-sm px-3 py-1 rounded-lg transition
                                    {{-- @if(in_array($child['id'], $selectedCategoryIds)) --}}
                                    @if((!$hasChildren) && in_array($child['id'], $selectedIds))
                                        selected text-blue-700 bg-blue-50
                                    @else
                                        text-gray-700 hover:text-blue-700 hover:bg-blue-50
                                    @endif
                            ">
                            {{ $child['name'][$lang] ?? '' }}
                            </a>
                            @if ($child['final_category'] == 1)
                                <button type="button"
                                        
                                        data-slug="{{ $fullSlug }}"
                                        data-field="{{ $fieldSlug }}"

                                        data-id="{{ $child['id'] }}"
                                        class="w-4 h-4 flex-shrink-0 text-gray-400 hover:text-blue-500 transition-colors kazcms-left-category-btn @if(!$hasChildren) rotate-90 @endif">

                                        <svg
                                            class="w-4 h-4 text-gray-500 transform transition-transform duration-200 "
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                        >
                                            <polyline points="6 9 12 15 18 9"></polyline> <!-- 向下箭头 -->
                                        </svg>
                                                                        </button>
                            @else
                                <span class="w-4 h-4 inline-block"></span> {{-- 占位对齐 --}}
                            @endif
                            @if((!$hasChildren) && in_array($child['id'], $selectedIds))

                            <span role="button" tabindex="0"
                                class="kazcms-category-remove inline-flex items-center px-1 py-0.5 rounded-full text-xs font-light text-blue-800 cursor-pointer"
                                data-id="{{ $child['id'] }}">
                                    <svg 
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" 
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="w-4 h-4">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="m15 9-6 6"/>
                                        <path d="m9 9 6 6"/>
                                    </svg>
                            </span>
                            @endif
                            

                        </li>
                            @if(isset($tree[$field['id']]['category_tree']))
                                @foreach ($tree[$field['id']]['category_tree'] as $node)
                                    @if ($node['id'] == $child['id'])
                                        @if (!empty($node['children']))
                                            @include('frontend.content.data.category-tree', [
                                                'tree' => $node['children'],
                                                'fieldSlug' => $fieldSlug,
                                                'fullSlug' => $fullSlug,
                                                'selectedCategoryIds' => $selectedCategoryIds
                                                ])
                                        @endif
                                    @endif
                                @endforeach
                            @endif
                    @endforeach
                </ul>

            @endif
        </div>
    @endif
@endforeach

    </div>
</div>

@push('scripts')
<script>
document.addEventListener("DOMContentLoaded", () => {
    attachCategoryClickHandlers();

    // 分类移除按钮功能
    document.querySelectorAll('.kazcms-category-remove').forEach(span => {
        span.addEventListener('click', () => {
            const id = span.dataset.id;
            const a = document.getElementById('category_a_' + id);
            if (a) {
                a.click(); // 模拟点击取消选中
            }
        });
    });

    // 动态绑定分类点击 + 子分类展开逻辑
    function attachCategoryClickHandlers() {
        // 展开按钮绑定
        document.querySelectorAll(".kazcms-left-category-btn").forEach(button => {
            if (button.dataset.bound === 'true') return;
            button.dataset.bound = 'true';

            button.addEventListener("click", handleClick);
        });

        // 分类选择绑定
        document.querySelectorAll('a.kazcms-left-category-a').forEach(a => {
            if (a.dataset.bound === 'true') return;
            a.dataset.bound = 'true';

            a.addEventListener('click', e => {
                e.preventDefault();
                a.classList.toggle('selected');

                const selected = {};
                document.querySelectorAll('a.kazcms-left-category-a.selected').forEach(el => {
                    const field = el.dataset.field;
                    const slug = el.dataset.slug;
                    if (!selected[field]) selected[field] = [];
                    selected[field].push(slug);
                });

                const params = new URLSearchParams();
                for (const [field, slugs] of Object.entries(selected)) {
                    params.set(field, slugs.join('~'));
                }

                const baseUrl = window.location.origin + window.location.pathname;
                const queryString = params.toString();
                const newUrl = baseUrl + (queryString ? '?' + queryString : '');
                window.location.href = newUrl;
            });
        });
    }

    // 展开子分类按钮点击逻辑
    async function handleClick(e) {
        e.preventDefault();

        const button = e.currentTarget;
        const categoryId = button.dataset.id;
        const parentLi = button.closest("li");
        const parentSlug = button?.dataset.slug || "";
        const fieldKey = button?.dataset.field || "";

        const existingSublist = parentLi.nextElementSibling;
        if (existingSublist && existingSublist.classList.contains("child-category")) {
            existingSublist.remove();
            button.classList.add('rotate-90');
            return;
        }

        try {
            const response = await fetch(`/${document.documentElement.lang}/ajax/categories/${categoryId}`);
            const json = await response.json();
            const children = json[categoryId];
            if (!children || children.length === 0) return;

            button.classList.remove('rotate-90');

            const ul = document.createElement("ul");
            ul.classList.add("space-y-2", "child-category", "pl-4", "mt-1");

            children.forEach(child => {
                const li = document.createElement("li");
                li.className = "flex items-center space-x-2";

                const fullSlug = (parentSlug ? parentSlug + '.' : '') + child.slug;

                const a = document.createElement("a");
                a.href = "#";
                a.dataset.field = fieldKey;
                a.dataset.slug = fullSlug;
                a.className = "kazcms-left-category-a flex-1 text-sm text-gray-700 hover:text-blue-700 hover:bg-blue-50 rounded-lg px-3 py-1 transition";
                a.textContent = typeof child.name === "string"
                    ? child.name
                    : (child.name[document.documentElement.lang] || "");

                li.appendChild(a);

                if (child.final_category === true) {
                    const btn = document.createElement("button");
                    btn.type = "button";
                    btn.dataset.id = child.id;
                    btn.dataset.field = fieldKey;
                    btn.dataset.slug = fullSlug;
                    btn.className = "w-4 h-4 flex-shrink-0 text-gray-400 hover:text-blue-500 transition-colors kazcms-left-category-btn rotate-90";
                    btn.innerHTML = `
                        <svg class="w-4 h-4 text-gray-500 transform transition-transform duration-200"
                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                             stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>`;
                    li.appendChild(btn);
                } else {
                    const span = document.createElement("span");
                    span.className = "w-4 h-4 inline-block";
                    li.appendChild(span);
                }

                ul.appendChild(li);
            });

            parentLi.insertAdjacentElement("afterend", ul);

            // 对新生成的按钮和 a 再次绑定事件
            attachCategoryClickHandlers();
        } catch (error) {
            console.error("Error loading children:", error);
        }
    }
});
</script>

@endpush