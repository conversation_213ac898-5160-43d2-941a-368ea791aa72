@extends('frontend.layouts.kazcms')

@section('title', $title ?? __('Content'))

@section('content')
@php
    $locale = app()->getLocale();
@endphp
<div class="bg-gray-100 max-w-5xl p-3 mx-auto">
    <nav aria-label="面包屑导航">
        <div class="bg-gray-50 rounded-lg px-4 py-3">
            <ol class="flex items-center space-x-1 text-sm">
                <!-- 首页 -->
                <li class="flex items-center">
                    <a href="/" class="flex items-center text-gray-600 hover:text-gray-900 hover:bg-white px-3 py-1.5 rounded-full transition-all duration-200">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        首页
                    </a>
                </li>

                <!-- 分隔符 -->
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </li>

      

 

 



                <!-- 智能手机 -->
                <li class="flex items-center">
                    <a href="/products/electronics/smartphones" class="flex items-center text-gray-600 hover:text-gray-900 hover:bg-white px-3 py-1.5 rounded-full transition-all duration-200">
                        智能手机
                    </a>
                </li>

                <!-- 分隔符 -->
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </li>

                <!-- 当前页面 -->
                <li class="flex items-center">
                    <span class="flex items-center bg-blue-100 text-blue-900 px-3 py-1.5 rounded-full font-medium">
                        iPhone 15 Pro
                    </span>
                </li>
            </ol>
        </div>
    </nav>
</div>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-5xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="p-8 sm:p-12">
                {{-- Render each field --}}
                @foreach($fields as $index => $field)
                    @php
                        $fieldType = $fieldTypes[$field->field_type_id]['related_table'];
                        $field_name = "fields_{$field->field_type_id}_{$field->id}";
                        $isLast = $loop->last;
                    @endphp

                    <div class="@if(!$isLast) mb-10 pb-8 border-b border-gray-100 @else mb-0 @endif">
                        {{-- Render content by field type --}}
                        @switch($fieldType)

                            @case('text')
                                @php
                                    $prefix = optional(json_decode($field->prefix_text_json, true))[$locale] ?? '';
                                    $suffix = optional(json_decode($field->suffix_text_json, true))[$locale] ?? '';
                                    $value = $data[$field_name][$locale] ?? '';
                                @endphp

                                @if($value)
                                    <div class="flex items-start space-x-3 group">
                                        @if($prefix)
                                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                                {{ $prefix }}
                                            </span>
                                        @endif
                                        <div class="flex-1 text-gray-800 text-base leading-relaxed whitespace-pre-line">
                                            {!! nl2br(e($value)) !!}
                                        </div>
                                        @if($suffix)
                                            <span class="flex-shrink-0 text-slate-500 text-sm font-medium bg-slate-100 px-3 py-1 rounded-full mt-1">
                                                {{ $suffix }}
                                            </span>
                                        @endif
                                    </div>
                                @endif
                                @break

                            @case('textarea')
                                @php
                                    $value = $data[$field_name][$locale] ?? '';
                                @endphp

                                @if($value)
                                    <div class="relative">
                                        <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono">
                                            TEXT
                                        </div>
                                        <div class="text-gray-800 text-sm font-mono leading-relaxed whitespace-pre-wrap bg-gradient-to-br from-gray-50 to-slate-50 p-6 border border-gray-200 rounded-xl shadow-inner">
                                            {!! nl2br(e($value)) !!}
                                        </div>
                                    </div>
                                @endif
                                @break

                            @case('file')
                                @php
                                    $image_ids = $data[$field_name][$locale] ?? [];
                                    $images = collect($image_ids)
                                        ->map(fn($id) => $data['attachments'][$id]->url ?? null)
                                        ->filter()
                                        ->values()
                                        ->all();
                                @endphp

                                @if(count($images))
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wide flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            Images ({{ count($images) }})
                                        </h4>
                                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                            @foreach($images as $img)
                                                <div class="group relative aspect-square bg-gray-100 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                                                    <img src="{{ $img }}" alt="Image" class="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300">
                                                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center py-8 text-gray-400">
                                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <p class="text-sm">No images available</p>
                                    </div>
                                @endif
                                @break

                            @case('codes')
                                @php
                                    $htmlCode = json_decode($field->html_code_json, true)[$locale] ?? '';
                                    $isEnabled = $field->is_enabled ?? true;
                                @endphp

                                @if($isEnabled && $htmlCode)
                                    <div class="relative">
                                        <div class="absolute top-4 right-4 text-xs text-emerald-600 font-mono bg-emerald-100 px-2 py-1 rounded">
                                            HTML
                                        </div>
                                        <div class="prose prose-sm max-w-none text-gray-700 bg-gradient-to-br from-emerald-50 to-teal-50 p-6 border border-emerald-200 rounded-xl shadow-inner">
                                            {!! $htmlCode !!}
                                        </div>
                                    </div>
                                @elseif($isEnabled)
                                    <div class="text-center py-6 text-red-400 bg-red-50 rounded-xl border border-red-200">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <p class="text-sm font-medium">No code available</p>
                                    </div>
                                @endif
                                @break

                            @case('categories')
                                @php
                                    $selectedCategories = $data[$field_name]['all'] ?? [];
                                @endphp

                                @if(is_array($selectedCategories) && count($selectedCategories))
                                    <div class="space-y-3">
                                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wide flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                                            </svg>
                                            Categories
                                        </h4>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($selectedCategories as $cat_id)
                                                <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-800 bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 rounded-full hover:from-purple-200 hover:to-pink-200 transition-all duration-200 shadow-sm">
                                                    <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ $data['categories'][$cat_id]['name'][$locale] ?? 'Untitled Category' }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center py-6 text-gray-400 bg-gray-50 rounded-xl border border-gray-200">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                        <p class="text-sm">No categories selected</p>
                                    </div>
                                @endif
                                @break

                            @case('options')
                                @php
                                    $dataValue = $data[$field_name]['all'] ?? [];
                                    $selected = is_array($dataValue) ? $dataValue : [$dataValue];
                                    $options = collect(json_decode($field->options, true) ?? [])
                                        ->filter(fn($opt) => $opt['enabled'] ?? false)
                                        ->sortBy('sort_order');
                                    $prefixText = json_decode($field->prefix_text_json, true)[$locale] ?? '';
                                    $suffixText = json_decode($field->suffix_text_json, true)[$locale] ?? '';
                                @endphp

                                @if(count($selected) && $selected[0])
                                    <div class="space-y-4">
                                        @if($prefixText)
                                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                                                <p class="text-sm text-blue-700 font-medium">{{ $prefixText }}</p>
                                            </div>
                                        @endif

                                        <div class="grid gap-3">
                                            @foreach($selected as $val)
                                                @php
                                                    $matched = $options->firstWhere('value', $val);
                                                    $labelText = $matched['label'][$locale] ?? $val;
                                                @endphp
                                                <div class="flex items-center p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg hover:from-indigo-100 hover:to-blue-100 transition-all duration-200 shadow-sm">
                                                    <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mr-4"></div>
                                                    <span class="text-gray-800 font-medium">{{ $labelText }}</span>
                                                </div>
                                            @endforeach
                                        </div>

                                        @if($suffixText)
                                            <div class="bg-slate-50 border-l-4 border-slate-400 p-4 rounded-r-lg">
                                                <p class="text-sm text-slate-600 font-medium">{{ $suffixText }}</p>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                @break

                            @default
                                @php
                                    dd($field);
                                @endphp 
                                <div class="text-center py-8 text-amber-600 bg-amber-50 rounded-xl border border-amber-200">
                                    <svg class="w-10 h-10 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <p class="text-sm font-medium">Unknown field type: {{ $field->field_type_id }}</p>
                                </div>
                        @endswitch
                    </div>
                @endforeach


            </div>
            <div class="mt-8">
            <h3 class="flex items-center text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200 mx-4">
                <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
                目录章节
            </h3>
                <div class="flex flex-wrap gap-3 p-4 bg-gray-50 rounded-lg shadow-sm">
                    @foreach($documentTree as $index => $node)
                    @php
                $parentNoteUrl = '/'.$node->slug[$locale];
                $chapterUrl = localized_route(
                    'dynamic.frontend',
                    [
                        'lang' => $locale,
                        'path' => $contentType->slug[$locale] .'/'.$extraSegments[0].$parentNoteUrl,
                    ]
                ) . '?version=' . $versionId;
                @endphp
                    <a href="{{ $chapterUrl }}" class="w-full sm:w-auto">
                            <div class="flex items-center p-3 bg-white rounded-md shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 hover:border-gray-300 min-w-fit">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                {{ $node->name[$locale] }}
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            </div>
                            </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection