<ul class="space-y-2 pl-4 mt-1 child-category">
    @foreach ($tree as $child)
    @php
        $fullSlug = $fullSlug.'.'.($child['slug'] ?? '');
        $hasChildren = !empty($child['children']);
    @endphp
        <li class="flex items-center space-x-2">


            <a href="#"
               id="category_a_{{ $child['id'] }}"
               data-field="{{ $fieldSlug }}"
               data-slug="{{ $fullSlug }}"
               class="kazcms-left-category-a flex-1 text-sm px-3 py-1 rounded-lg transition
                    @if (!$hasChildren)
                       selected text-blue-700 bg-blue-50
                   @else
                       text-gray-700 hover:text-blue-700 hover:bg-blue-50
                   @endif">
                {{ $child['name'] ?? '' }}
            </a>
            @if ($child['final_category'] == 1)
                <button type="button"
                        data-slug="{{ $fullSlug }}"
                        data-field="{{ $fieldSlug }}"
                        data-id="{{ $child['id'] }}"
                        class="w-4 h-4 flex-shrink-0 text-gray-400 hover:text-blue-500 transition-colors kazcms-left-category-btn @if(!$hasChildren) rotate-90 @endif">
                        <svg
                                            class="w-4 h-4 text-gray-500 transform transition-transform duration-200 "
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                        >
                                            <polyline points="6 9 12 15 18 9"></polyline> <!-- 向下箭头 -->
                                        </svg>
                </button>
            @else
                <span class="w-4 h-4 inline-block"></span> {{-- Align placeholder --}}
            @endif
            @if (!$hasChildren)
            <span role="button" tabindex="0"
                class="kazcms-category-remove inline-flex items-center px-1 py-0.5 rounded-full text-xs font-light text-blue-800 cursor-pointer"
                data-id="{{ $child['id'] }}">
                    <svg 
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" 
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-4 h-4">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m15 9-6 6"/>
                        <path d="m9 9 6 6"/>
                    </svg>
            </span>
            @endif
        </li>

        {{-- Recursive children rendering --}}
        @if (!empty($child['children']))
            @include('frontend.content.data.category-tree', [
                'tree' => $child['children'],
                'fieldSlug' => $fieldSlug,
                'fullSlug' => $fullSlug,
                'selectedCategoryIds' => $selectedCategoryIds
            ])
        @endif
    @endforeach
</ul>
