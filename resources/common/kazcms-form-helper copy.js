// kazcms-form-helper.js - Tailwind 版本
window.KazCMSFormHelper = {
    modalId: 'kazcmsFormHelperModal',
    textareaId: 'kazcmsFormHelperTextarea',
    buttonId: 'kazcmsQuickFillBtn', // 全局唯一 ID

    init: function({fields, languages, buttonContainerId}) {
        this.fields = fields || [];
        this.languages = languages || ['en-us'];

        this.buttonContainer = document.getElementById(buttonContainerId);
        if (!this.buttonContainer) {
            console.error('KazCMSFormHelper: button container not found');
            return;
        }

        this.createModal();
        this.addQuickFillButton();
    },

    createModal: function() {
        if (document.getElementById(this.modalId)) return;
    
        const modal = document.createElement('div');
        modal.id = this.modalId;
        // 外层 modal 占满屏幕，背景半透明且模糊，隐藏状态
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm hidden';
    
        // 内部 div 占屏幕 90%，宽度最大 6xl，flex 布局，便于 textarea 占满剩余空间
        modal.innerHTML = `
            <div class="bg-white rounded-lg w-full max-w-6xl h-[90vh] flex flex-col p-6 shadow-xl">
                <!-- Header -->
                <h3 class="text-lg font-semibold mb-2">Form Helper JSON Input</h3>
                <p class="text-sm text-gray-600 mb-3">
                    Copy this template to AI, generate values, then paste the resulting JSON here. 
                    Click "Fill Form" to automatically populate the form fields.
                </p>
    
                <!-- Textarea 占满剩余空间 -->
 <textarea id="kazcmsFormHelperTextarea"
          class="w-full p-3 border border-gray-300 rounded-md font-mono resize-none"
          style="height:70vh;"></textarea>
                <!-- Footer Buttons -->
                <div class="mt-3 flex justify-end gap-2">
                    <button id="kazcmsModalCancel" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button id="kazcmsModalFill" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Fill Form
                    </button>
                </div>
            </div>
        `;
    
        document.body.appendChild(modal);
    
        // 关闭按钮事件
        document.getElementById('kazcmsModalCancel').addEventListener('click', () => {
            modal.classList.add('hidden');
        });
    
        // 填充表单按钮事件
        document.getElementById('kazcmsModalFill').addEventListener('click', () => {
            this.fillFormFromTextarea();
            modal.classList.add('hidden');
        });
    },
    
    

    addQuickFillButton: function() {
        if(document.getElementById(this.buttonId)) return;

        const button = document.createElement('button');
        button.id = this.buttonId;
        button.type = 'button';
        button.textContent = 'Quick Fill from AI';
        button.className = 'px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700';
        button.addEventListener('click', () => {
            this.populateTextareaTemplate();
            document.getElementById(this.modalId).classList.remove('hidden');
        });

        this.buttonContainer.appendChild(button);
    },

    populateTextareaTemplate: function() {
        const template = {};
        this.fields.forEach(field => {
            if(this.languages.length > 1){
                template[field] = {};
                this.languages.forEach(lang => {
                    template[field][lang] = `// ${field} value`;
                });
            } else {
                template[field] = `// ${field} value`;
            }
        });
        document.getElementById(this.textareaId).value = JSON.stringify(template, null, 4);
    },

    fillFormFromTextarea: function() {
        let json;
        try {
            json = JSON.parse(document.getElementById(this.textareaId).value);
        } catch(e) {
            alert('Invalid JSON');
            return;
        }

        Object.keys(json).forEach(field => {
            const value = json[field];
            if(typeof value === 'object'){
                Object.keys(value).forEach(lang => {
                    const input = document.querySelector(`[name="${field}[${lang}]"]`);
                    if(input) input.value = value[lang];
                });
            } else {
                const input = document.querySelector(`[name="${field}"]`);
                if(input) input.value = value;
            }
        });
    }
};
