// kazcms-form-helper.js - Tailwind 版本 + 支持 options
window.KazCMSFormHelper = {
    modalId: 'kazcmsFormHelperModal',
    textareaId: 'kazcmsFormHelperTextarea',
    buttonId: 'kazcmsQuickFillBtn', // 全局唯一 ID

    init: function({fields, languages, buttonContainerId}) {
        this.fields = fields || [];
        this.languages = languages || ['en-us'];

        this.buttonContainer = document.getElementById(buttonContainerId);
        if (!this.buttonContainer) {
            console.error('KazCMSFormHelper: button container not found');
            return;
        }

        this.createModal();
        this.addQuickFillButton();
    },

    createModal: function() {
        if (document.getElementById(this.modalId)) return;
    
        const modal = document.createElement('div');
        modal.id = this.modalId;
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm hidden';
    
        modal.innerHTML = `
            <div class="bg-white rounded-lg w-full max-w-6xl h-[90vh] flex flex-col p-6 shadow-xl">
                <!-- Header -->
                <h3 class="text-lg font-semibold mb-2">Form Helper JSON Input</h3>
                <p class="text-sm text-gray-600 mb-3">
                    Copy this template to AI, generate values (including options), then paste the resulting JSON here. 
                    Click "Fill Form" to automatically populate the form fields.
                </p>
    
                <!-- Textarea -->
                <textarea id="kazcmsFormHelperTextarea"
                    class="w-full p-3 border border-gray-300 rounded-md font-mono resize-none"
                    style="height:70vh;"></textarea>
    
                <!-- Footer Buttons -->
                <div class="mt-3 flex justify-end gap-2">
                    <button id="kazcmsModalCancel" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button id="kazcmsModalFill" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Fill Form
                    </button>
                </div>
            </div>
        `;
    
        document.body.appendChild(modal);
    
        document.getElementById('kazcmsModalCancel').addEventListener('click', () => {
            modal.classList.add('hidden');
        });
    
        document.getElementById('kazcmsModalFill').addEventListener('click', () => {
            this.fillFormFromTextarea();
            modal.classList.add('hidden');
        });
    },
    
    addQuickFillButton: function() {
        if(document.getElementById(this.buttonId)) return;

        const button = document.createElement('button');
        button.id = this.buttonId;
        button.type = 'button';
        button.textContent = 'Quick Fill from AI';
        button.className = 'px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700';
        button.addEventListener('click', () => {
            this.populateTextareaTemplate();
            document.getElementById(this.modalId).classList.remove('hidden');
        });

        this.buttonContainer.appendChild(button);
    },

    populateTextareaTemplate: function() {
        const template = {};
        this.fields.forEach(field => {
            if(field === 'options'){ 
                // 动态读取页面上已有的 options
                const optionItems = document.querySelectorAll('.option-item');
                template['options'] = [];
                optionItems.forEach((item, index) => {
                    const opt = {
                        value: item.querySelector(`input[name^="options"][name$="[value]"]`)?.value || '',
                        label: {}
                    };
                    this.languages.forEach(lang => {
                        const input = item.querySelector(`input[name^="options"][name$="[label][${lang}]"]`);
                        // 如果用户已经输入，就用用户输入的值
                        opt.label[lang] = input ? (input.value || `// option ${index + 1} label (${lang})`) : `// option ${index + 1} label (${lang})`;
                    });
                    template['options'].push(opt);
                });
            } else {
                if(this.languages.length > 1){
                    template[field] = {};
                    this.languages.forEach(lang => {
                        const input = document.querySelector(`[name="${field}[${lang}]"]`);
                        // 优先使用用户已经填写的值
                        template[field][lang] = input ? (input.value || `// ${field} value (${lang})`) : `// ${field} value (${lang})`;
                    });
                } else {
                    const input = document.querySelector(`[name="${field}"]`);
                    template[field] = input ? (input.value || `// ${field} value`) : `// ${field} value`;
                }
            }
        });
        document.getElementById(this.textareaId).value = JSON.stringify(template, null, 4);
    },
    

    fillFormFromTextarea: function() {
        let json;
        try {
            json = JSON.parse(document.getElementById(this.textareaId).value);
        } catch(e) {
            alert('Invalid JSON');
            return;
        }

        Object.keys(json).forEach(field => {
            const value = json[field];

            if(field === 'options' && Array.isArray(value)){
                // special case: fill options
                this.fillOptions(value);
            } 
            else if(typeof value === 'object'){
                Object.keys(value).forEach(lang => {
                    const input = document.querySelector(`[name="${field}[${lang}]"]`);
                    if(input) input.value = value[lang];
                });
            } else {
                const input = document.querySelector(`[name="${field}"]`);
                if(input) input.value = value;
            }
        });
    },

    fillOptions: function(options){
        const optionItems = document.querySelectorAll('#option-list .option-item');
    
        options.forEach((opt, index) => {
            const item = optionItems[index];
            if(!item) return; // 如果用户没有生成这么多 options，就跳过
    
            // 填充 value
            const valueInput = item.querySelector(`input[name^="options[${index}][value]"]`);
            if(valueInput) valueInput.value = opt.value || '';
    
            // 填充 label per language
            this.languages.forEach(lang => {
                const labelInput = item.querySelector(`input[name^="options[${index}][label][${lang}]"]`);
                if(labelInput) labelInput.value = (opt.label && opt.label[lang]) ? opt.label[lang] : '';
            });
    
            // 如果还有 enabled checkbox
            const enabledInput = item.querySelector(`input[name^="options[${index}][enabled]"]`);
            if(enabledInput) enabledInput.checked = opt.enabled ?? true;
        });
    }
    
};
