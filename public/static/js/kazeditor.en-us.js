window.KazEditorLang = {
  bold: { label: 'B', description: 'Bold text' },
  italic: { label: 'I', description: 'Italic text' },
  underline: { label: 'U', description: 'Underline text' },
  strike: { label: 'S', description: 'Strikethrough text' },
  subscript: { label: 'X₂', description: 'Subscript text' },
  superscript: { label: 'X²', description: 'Superscript text' },
  removeFormat: { label: 'Tx', description: 'Remove formatting' },

  h1: { label: 'H1', description: 'Heading 1' },
  h2: { label: 'H2', description: 'Heading 2' },
  h3: { label: 'H3', description: 'Heading 3' },
  h4: { label: 'H4', description: 'Heading 4' },
  h5: { label: 'H5', description: 'Heading 5' },
  h6: { label: 'H6', description: 'Heading 6' },

  p: { label: 'P', description: 'Paragraph' },
  blockquote: { label: '❝', description: 'Blockquote' },
  pre: { label: '</>', description: 'Preformatted Text' },

  ul: { label: '• List', description: 'Unordered List' },
  ol: { label: '1. List', description: 'Ordered List' },
  checklist: { label: '☑ Task', description: 'Task List (checkbox)' },

  justifyLeft: { label: 'L', description: 'Align Left' },
  justifyCenter: { label: 'C', description: 'Align Center' },
  justifyRight: { label: 'R', description: 'Align Right' },
  justifyFull: { label: 'J', description: 'Justify' },

  indent: { label: '→', description: 'Indent' },
  outdent: { label: '←', description: 'Outdent' },

  link: { label: '🔗', description: 'Insert Link', prompt: 'Enter URL:' },
  unlink: { label: '❌', description: 'Remove Link' },

  image: { label: '🖼️', description: 'Insert Image', prompt: 'Enter Image URL:' },
  video: { label: '🎥', description: 'Insert Video', prompt: 'Enter Video URL:' },
  file: { label: '📎', description: 'Insert File', prompt: 'Enter File URL:' },

  foreColor: { label: 'A', description: 'Text Color', prompt: 'Enter color (name or hex):' },
  backColor: { label: '🖌️', description: 'Background Color', prompt: 'Enter color (name or hex):' },
  fontName: { label: 'F', description: 'Font Name', prompt: 'Enter font family:' },
  fontSize: { label: 'S', description: 'Font Size', prompt: 'Enter font size (1-7):' },

  undo: { label: '↺', description: 'Undo' },
  redo: { label: '↻', description: 'Redo' },

  viewSource: { label: 'Code', description: 'View Source HTML' }
};
