window.kazImageCraftLang = {
    duplicate: filename => `图片 "${filename}" 已经上传过了，是否继续上传？`,
    removeImage: '删除图片',
    dragDropHint: '拖拽文件到这里，或点击上传',
    previewImage: '预览图片',
    cropAndRotate: '裁剪 & 旋转',
    reset: '重置',
    cancel: '取消',
    confirmCrop: '确认裁剪',
    cropConfirm: '✔️',
    cropCancel: '✖️',
    rotateConfirm: '✔️',
    rotateCancel: '✖️',
    resetWarning: '⚠️ 这将覆盖你的编辑图片并恢复到原始状态。',
    submissionBlocked: '提交被阻止',
    switchToImage: '切换到图片:'
    
  };
  