/* 富文本编辑器现代化样式 */
.kaz-editor-wrapper {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    transition: all 0.2s ease;
    background: #ffffff;
    overflow: hidden;
  }
  
  .kaz-editor-wrapper:hover {
    border-color: #c7d2fe;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .kaz-editor-wrapper:focus-within {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  .kaz-editor-toolbar {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
  }
  
  .kaz-editor-toolbar button {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    user-select: none;
    outline: none;
  }
  
  .kaz-editor-toolbar button:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .kaz-editor-toolbar button:active {
    background: #e9ecef;
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .kaz-editor-toolbar button:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  }
  
  /* 工具栏按钮组分隔 */
  .kaz-editor-toolbar button:nth-child(3n):after {
    content: '';
    display: inline-block;
    width: 1px;
    height: 20px;
    background: #dee2e6;
    margin-left: 12px;
    margin-right: 6px;
  }
  
  .kaz-editor-content {
    min-height: 180px;
    padding: 20px;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-size: 15px;
    line-height: 1.6;
    color: #212529;
    background: #ffffff;
    transition: background-color 0.2s ease;
  }
  
  .kaz-editor-content:focus {
    background: #fefefe;
  }
  
  .kaz-editor-content:empty:before {
    content: attr(placeholder);
    color: #adb5bd;
    pointer-events: none;
    font-style: italic;
  }
  
  /* 源码视图样式 */
  .kaz-editor-content.kaz-editor-source {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: #f8f9fa;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 20px;
    border-radius: 0;
  }
  
  /* 内容区域的基础样式 */
  .kaz-editor-content h1,
  .kaz-editor-content h2,
  .kaz-editor-content h3,
  .kaz-editor-content h4,
  .kaz-editor-content h5,
  .kaz-editor-content h6 {
    margin: 1.2em 0 0.6em 0;
    font-weight: 600;
    line-height: 1.3;
  }
  
  .kaz-editor-content h1 { font-size: 2em; color: #1a202c; }
  .kaz-editor-content h2 { font-size: 1.5em; color: #2d3748; }
  .kaz-editor-content h3 { font-size: 1.25em; color: #4a5568; }
  
  .kaz-editor-content p {
    margin: 0.8em 0;
  }
  
  .kaz-editor-content blockquote {
    margin: 1em 0;
    padding: 12px 20px;
    border-left: 4px solid #6366f1;
    background: #f8fafc;
    font-style: italic;
    color: #64748b;
  }
  
  .kaz-editor-content ul,
  .kaz-editor-content ol {
    margin: 1em 0;
    padding-left: 2em;
  }
  
  .kaz-editor-content li {
    margin: 0.4em 0;
  }
  
  .kaz-editor-content a {
    color: #6366f1;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.15s ease;
  }
  
  .kaz-editor-content a:hover {
    border-bottom-color: #6366f1;
  }
  
  .kaz-editor-content code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 0.9em;
    color: #e11d48;
  }
  
  .kaz-editor-content pre {
    background: #1e293b;
    color: #f1f5f9;
    padding: 16px 20px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1.2em 0;
  }
  
  .kaz-editor-content pre code {
    background: none;
    padding: 0;
    color: inherit;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .kaz-editor-toolbar {
      padding: 10px 12px;
      gap: 4px;
    }
    
    .kaz-editor-toolbar button {
      min-width: 32px;
      height: 32px;
      padding: 6px 8px;
      font-size: 13px;
    }
    
    .kaz-editor-content {
      padding: 16px;
      font-size: 14px;
    }
  }
  
  /* 深色模式支持 */
  @media (prefers-color-scheme: dark) {
    .kaz-editor-wrapper {
      background: #1f2937;
      border-color: #374151;
    }
    
    .kaz-editor-toolbar {
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
      border-bottom-color: #4b5563;
    }
    
    .kaz-editor-toolbar button {
      background: #374151;
      border-color: #4b5563;
      color: #f9fafb;
    }
    
    .kaz-editor-toolbar button:hover {
      background: #4b5563;
      border-color: #6b7280;
    }
    
    .kaz-editor-content {
      background: #1f2937;
      color: #f9fafb;
    }
    
    .kaz-editor-content:focus {
      background: #111827;
    }
    
    .kaz-editor-content.kaz-editor-source {
      background: #111827;
      color: #d1d5db;
    }
  }

  /* KazEditor Modal Styles */
.kaz-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.kaz-editor-modal.hidden {
  opacity: 0;
  visibility: hidden;
}

.kaz-editor-modal-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}

.kaz-editor-modal-content {
  position: relative;
  z-index: 10;
  background: #ffffff;
  border-radius: 12px;
  width: 480px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: scale(1);
  transition: transform 0.3s ease;
}

.kaz-editor-modal.hidden .kaz-editor-modal-content {
  transform: scale(0.95);
}

.kaz-editor-modal-header {
  padding: 24px 24px 0 24px;
}

.kaz-editor-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.5;
}

.kaz-editor-modal-body {
  padding: 16px 24px 24px 24px;
  color: #4b5563;
  line-height: 1.6;
}

.kaz-editor-modal-body input {
  width: 90%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  outline: none;
  background: #ffffff;
}

.kaz-editor-modal-body input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.kaz-editor-modal-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.kaz-editor-modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  min-width: 80px;
}

.kaz-editor-modal-btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.kaz-editor-modal-btn-cancel {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.kaz-editor-modal-btn-cancel:hover {
  background: #e5e7eb;
  color: #4b5563;
}

.kaz-editor-modal-btn-cancel:focus {
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.2);
}

.kaz-editor-modal-btn-confirm {
  background: #3b82f6;
  color: #ffffff;
  border: 1px solid #3b82f6;
}

.kaz-editor-modal-btn-confirm:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.kaz-editor-modal-btn-confirm:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.kaz-editor-modal-btn:active {
  transform: translateY(1px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .kaz-editor-modal-content {
    background: #1f2937;
    border: 1px solid #374151;
  }
  
  .kaz-editor-modal-title {
    color: #f9fafb;
  }
  
  .kaz-editor-modal-body {
    color: #d1d5db;
  }
  
  .kaz-editor-modal-body input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .kaz-editor-modal-body input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }
  
  .kaz-editor-modal-btn-cancel {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .kaz-editor-modal-btn-cancel:hover {
    background: #4b5563;
    color: #f3f4f6;
  }
}

/* Responsive design */
@media (max-width: 640px) {
  .kaz-editor-modal-content {
    width: 95vw;
    margin: 16px;
  }
  
  .kaz-editor-modal-header,
  .kaz-editor-modal-body,
  .kaz-editor-modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .kaz-editor-modal-footer {
    flex-direction: column;
  }
  
  .kaz-editor-modal-btn {
    width: 100%;
  }
}