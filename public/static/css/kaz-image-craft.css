/* =================
   Image Uploader - Base Styles
   ================= */

/* Upload Area */
.kaz-image-craft-wrapper {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
  min-height: 120px;
  background-color: #f9f9f9;
}

.kaz-image-craft-wrapper.hover {
  border-color: #3399ff;
  background-color: #eef6ff;
}

/* Preview Item Container */
.kaz-image-craft-preview-item {
  display: inline-block;
  margin: 5px;
  position: relative;
  width: 110px;
  user-select: none;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 4px;
  cursor: move;
  background: #fff;
  transition: border 0.2s ease;
}

.kaz-image-craft-preview-item.kaz-image-craft-drag-over {
  border: 2px dashed #2196f3 !important;
}

.kaz-image-craft-preview-item.kaz-image-craft-dragging {
  opacity: 0.5;
}

/* Image Box */
.kaz-image-craft-image-box {
  position: relative;
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.kaz-image-craft-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.kaz-image-craft-image:hover {
  transform: scale(1.05);
}

/* Delete Button */
.kaz-image-craft-delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(244, 67, 54, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease, background 0.2s ease;
}

.kaz-image-craft-delete-btn:hover {
  background: rgba(244, 67, 54, 1);
  transform: scale(1.1);
}

/* Image Name */
.kaz-image-craft-image-name {
  font-size: 12px;
  margin-top: 5px;
  color: #555;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* =================
   Modal - Base Structure
   ================= */

.kaz-image-craft-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.kaz-image-craft-modal[style*="flex"],
.kaz-image-craft-modal[style*="block"] {
  opacity: 1;
}

/* Modal Title */
.kaz-image-craft-modal-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 8px 20px;
  border-radius: 20px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.kaz-image-craft-modal-title::before {
  content: "图片预览";
}

/* Modal Backdrop */
.kaz-image-craft-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  cursor: pointer;
}

/* Modal Content */
.kaz-image-craft-modal-content {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  max-width: 95%;
  max-height: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.kaz-image-craft-modal[style*="flex"] .kaz-image-craft-modal-content,
.kaz-image-craft-modal[style*="block"] .kaz-image-craft-modal-content {
  transform: scale(1);
}

/* Close Button */
.kaz-image-craft-modal-close {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: #666;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 10;
}

.kaz-image-craft-modal-close:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  transform: scale(1.1);
}

/* =================
   Modal - Main Content Area
   ================= */

.kaz-image-craft-modal-main {
  display: flex;
  flex: 1;
  gap: 16px;
  min-height: 0;
}

/* Toolbar */
.kaz-image-craft-modal-toolbar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 80px;
  height: fit-content;
}

.kaz-tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px 2px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.kaz-tool-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}

.kaz-tool-item:active {
  transform: translateY(0);
}

.kaz-tool-icon {
  font-size: 14px;
  margin-bottom: 4px;
}

.kaz-tool-text {
  font-size: 10px;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

/* Image Display Area */
.kaz-image-craft-modal-image-area {
  width: 600px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.kaz-image-craft-modal-image-area img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

#kaz-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

/* =================
   Modal - Bottom Image List
   ================= */

.kaz-image-craft-modal-image-list {
  margin-top: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
}

.kaz-image-list-container {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  flex: 1;
  padding: 4px 0;
}

.kaz-image-list-container::-webkit-scrollbar {
  height: 4px;
}

.kaz-image-list-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.kaz-image-list-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.kaz-image-craft-image-preview-item {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  border: 2px solid transparent;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.kaz-image-craft-image-preview-item:hover {
  border-color: rgba(99, 102, 241, 0.5);
  transform: scale(1.05);
}

.kaz-image-craft-image-preview-item.active {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.kaz-image-craft-image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Navigation Buttons */
.kaz-list-nav {
  display: flex;
  gap: 4px;
}

.kaz-nav-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.kaz-nav-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  transform: scale(1.05);
}

.kaz-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Modal Footer */
.kaz-image-craft-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 12px;
}

/* =================
   Feature Components - Crop Related
   ================= */

.kaz-image-craft-modal-crop-box {
  position: absolute;
  border: 2px dashed #fff;
  box-sizing: border-box;
  cursor: move;
  z-index: 10;
}

.crop-handle {
  position: absolute;
  width: 18px;
  height: 18px;
  background: #fff;
  border: 1px solid #000;
  z-index: 10;
}

.crop-handle-nw { top: -6px; left: -6px; cursor: nwse-resize; }
.crop-handle-ne { top: -6px; right: -6px; cursor: nesw-resize; }
.crop-handle-sw { bottom: -6px; left: -6px; cursor: nesw-resize; }
.crop-handle-se { bottom: -6px; right: -6px; cursor: nwse-resize; }

/* Crop Control Buttons */
.kaz-crop-box-controls {
  position: absolute;
  top: -40px;
  right: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1000;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
}

.kaz-crop-confirm,
.kaz-crop-cancel {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 32px;
  padding: 0 16px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.kaz-crop-confirm:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.kaz-crop-cancel:hover {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.kaz-crop-confirm:active,
.kaz-crop-cancel:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.kaz-crop-confirm:focus,
.kaz-crop-cancel:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

.kaz-crop-confirm:disabled,
.kaz-crop-cancel:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* =================
   Feature Components - Rotate Related
   ================= */

.kaz-image-craft-modal-rotate-box {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.kaz-image-craft-modal-rotate-box .rotate-circle {
  position: absolute;
  width: 100px;
  height: 100px;
  border: 1px dashed #999;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
  user-select: none;
}

.kaz-image-craft-modal-rotate-box .rotate-center {
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  font-weight: bold;
  color: red;
  cursor: move;
  user-select: none;
}

.kaz-rotate-confirm {
  top: 10px;
  left: 10px;
}

.kaz-rotate-cancel {
  top: 10px;
  left: 40px;
}

/* =================
   Toolbar - Extended Styles
   ================= */

.kaz-image-craft-tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  width: 56px;
  font-family: Arial, sans-serif;
  font-size: 12px;
  color: #333;
  user-select: none;
}

.kaz-image-craft-tool-item:hover {
  color: #007bff;
}

.kaz-image-craft-tool-icon {
  font-size: 24px;
  line-height: 1;
  margin-bottom: 4px;
}

.kaz-image-craft-tool-grid {
  width: 56px;
  padding: 4px;
}

.kaz-image-craft-tool-icon-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 6px;
}

.kaz-image-craft-tool-icon-cell {
  width: 24px;
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.kaz-image-craft-tool-icon-cell:hover {
  background-color: #d0e8ff;
}

.kaz-image-craft-empty-cell {
  background: transparent;
  cursor: default;
  pointer-events: none;
}

.kaz-image-craft-zoom-tool .kaz-image-craft-tool-icon {
  font-size: 24px;
  user-select: none;
}

.kaz-image-craft-tool-text {
  user-select: none;
  font-size: 12px;
  line-height: 1.2;
  text-align: center;
}

/* =================
   Responsive Design
   ================= */

@media (max-width: 768px) {
  .kaz-image-craft-modal-content {
    max-width: 98%;
    max-height: 95%;
    padding: 16px;
  }
  
  .kaz-image-craft-modal-main {
    flex-direction: column;
    gap: 12px;
  }
  
  .kaz-image-craft-modal-toolbar {
    flex-direction: row;
    width: 100%;
    height: 60px;
    overflow-x: auto;
    padding: 8px 12px;
  }
  
  .kaz-tool-item {
    min-width: 20px;
    padding: 1px;
  }
  
  .kaz-tool-icon {
    font-size: 10px;
    margin-bottom: 2px;
  }
  
  .kaz-tool-text {
    font-size: 10px;
  }
  
  .kaz-image-craft-modal-image-area {
    height: 300px;
  }
  
  .kaz-image-craft-image-preview-item {
    width: 40px;
    height: 40px;
  }
}

/* =================
   High DPI Screen Optimization
   ================= */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .kaz-crop-confirm,
  .kaz-crop-cancel {
    border-width: 0.5px;
  }
}