class ImageUploader {
  constructor(fileInput, previewContainer, form, orderInputName = 'image_order') {
    this.fileInput = fileInput;
    this.previewContainer = previewContainer;
    this.form = form;
    this.orderInputName = orderInputName;
    this.uploadedFiles = []; // 只存 File 对象，不存 id
    this.dragSrcEl = null;
    this._init();
  }

  _init() {
    this.fileInput.addEventListener('change', e => {
      this._handleFiles(e.target.files);
    });
    // 不再动态注入隐藏 file input，取消 form submit 绑定
    this._renderPreview();
  }

  _handleFiles(files) {
    const newFiles = Array.from(files);
    newFiles.forEach(file => {
      if (!file.type.startsWith('image/')) return;
      // 过滤重复文件，防止同名同大小文件重复添加
      const duplicate = this.uploadedFiles.some(f => f.name === file.name && f.size === file.size);
      if (duplicate) {
        alert(`图片 "${file.name}" 已添加过`);
        return;
      }
      this.uploadedFiles.push(file);
    });
    this._renderPreview();
    // 清空 file input，方便重复选择同一文件
    this.fileInput.value = '';
  }

  _renderPreview() {
    this.previewContainer.innerHTML = '';
    this.uploadedFiles.forEach((file, idx) => {
      const previewUrl = URL.createObjectURL(file);
      const div = document.createElement('div');
      div.className = 'image-preview-item border border-gray-200 rounded p-1 cursor-move';
      div.dataset.index = idx; // 用索引作为标识
      div.style.cssText = 'display:inline-block; margin:5px; position:relative; width:110px; user-select:none;';
      div.setAttribute('draggable', 'true');
      div.innerHTML = `
        <img src="${previewUrl}" alt="预览" style="width:100px; height:100px; object-fit:cover; border-radius:4px;">
        <button type="button" aria-label="删除图片" style="
          position:absolute; top:2px; right:2px; background:#f44336; color:#fff; border:none; 
          border-radius:50%; width:20px; height:20px; cursor:pointer; line-height:18px; font-weight:bold;">×</button>
      `;
      div.querySelector('button').addEventListener('click', () => {
        this.uploadedFiles.splice(idx, 1);
        this._renderPreview();
      });
      div.addEventListener('dragstart', e => this._handleDragStart(e));
      div.addEventListener('dragover', e => this._handleDragOver(e));
      div.addEventListener('dragleave', e => this._handleDragLeave(e));
      div.addEventListener('drop', e => this._handleDrop(e));
      div.addEventListener('dragend', e => this._handleDragEnd(e));
      this.previewContainer.appendChild(div);
    });
    this._updateOrderInputs();
  }

  _updateOrderInputs() {
    // 移除旧的顺序隐藏 input
    this.form.querySelectorAll(`input[name^="${this.orderInputName}"]`).forEach(el => el.remove());
    // 添加当前顺序隐藏 input，值是索引
    this.uploadedFiles.forEach((file, idx) => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = `${this.orderInputName}[]`;
      input.value = idx;
      this.form.appendChild(input);
    });
  }

  // 拖拽开始
  _handleDragStart(e) {
    this.dragSrcEl = e.currentTarget;
    this.dragSrcEl.classList.add('opacity-50');
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', '');
  }

  // 拖拽经过
  _handleDragOver(e) {
    e.preventDefault();
    const el = e.currentTarget;
    if (el !== this.dragSrcEl) {
      el.classList.add('border-blue-500');
      el.classList.remove('border-gray-200');
    }
  }

  // 拖拽离开
  _handleDragLeave(e) {
    e.currentTarget.classList.remove('border-blue-500');
    e.currentTarget.classList.add('border-gray-200');
  }

  // 拖拽放下
  _handleDrop(e) {
    e.preventDefault();
    const el = e.currentTarget;
    if (el !== this.dragSrcEl) {
      const parent = this.previewContainer;
      const dragIndex = Array.from(parent.children).indexOf(this.dragSrcEl);
      const dropIndex = Array.from(parent.children).indexOf(el);
      if (dragIndex < dropIndex) {
        parent.insertBefore(this.dragSrcEl, el.nextSibling);
      } else {
        parent.insertBefore(this.dragSrcEl, el);
      }
      this._reorderFilesByDOM();
    }
    el.classList.remove('border-blue-500');
    el.classList.add('border-gray-200');
    this.dragSrcEl.classList.remove('opacity-50');
    this.dragSrcEl = null;
  }

  // 拖拽结束
  _handleDragEnd(e) {
    this.previewContainer.querySelectorAll('.image-preview-item').forEach(item => {
      item.classList.remove('opacity-50', 'border-blue-500');
      item.classList.add('border-gray-200');
    });
    this.dragSrcEl = null;
  }

  // 根据 DOM 顺序重排 this.uploadedFiles 顺序
  _reorderFilesByDOM() {
    const newOrder = [];
    this.previewContainer.querySelectorAll('.image-preview-item').forEach(div => {
      const idx = parseInt(div.dataset.index, 10);
      // 这里因为后面 _renderPreview 会重写 dataset.index ，所以先缓存文件对应旧索引
      newOrder.push(this.uploadedFiles[idx]);
    });
    this.uploadedFiles = newOrder;
    this._renderPreview(); // 重新渲染以刷新 dataset.index 和隐藏 input
  }
}


function initImageUploaders(fileInputClass, formClass) {
  document.querySelectorAll(`form.${formClass}`).forEach(form => {
    form.querySelectorAll(`input.${fileInputClass}[type="file"]`).forEach(input => {
      const previewId = input.dataset.preview;
      const previewContainer = previewId ? document.getElementById(previewId) : form.querySelector('.kaz-preview-container');
      if (!previewContainer) {
        console.warn('找不到对应预览容器 for input:', input);
        return;
      }
      const orderInputName = 'image_order_' + input.name.replace(/\W+/g, '_');
      new ImageUploader(input, previewContainer, form, orderInputName);
    });
  });
}

window.initImageUploaders = initImageUploaders;
