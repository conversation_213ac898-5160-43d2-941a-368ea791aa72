(function(c,m){typeof exports=="object"&&typeof module<"u"?m(exports):typeof define=="function"&&define.amd?define(["exports"],m):(c=typeof globalThis<"u"?globalThis:c||self,m(c.<PERSON><PERSON><PERSON>or={}))})(this,function(c){"use strict";const m={bold:{command:"bold",group:"format"},italic:{command:"italic",group:"format"},underline:{command:"underline",group:"format"},strike:{command:"strikeThrough",group:"format"},subscript:{command:"subscript",group:"format"},superscript:{command:"superscript",group:"format"},removeFormat:{command:"removeFormat",group:"format"},h1:{command:"formatBlock",value:"<h1>",group:"header"},h2:{command:"formatBlock",value:"<h2>",group:"header"},h3:{command:"formatBlock",value:"<h3>",group:"header"},h4:{command:"formatBlock",value:"<h4>",group:"header"},h5:{command:"formatBlock",value:"<h5>",group:"header"},h6:{command:"formatBlock",value:"<h6>",group:"header"},p:{command:"formatBlock",value:"<p>",group:"paragraph"},blockquote:{command:"formatBlock",value:"<blockquote>",group:"paragraph"},pre:{command:"formatBlock",value:"<pre>",group:"paragraph"},ul:{command:"insertUnorderedList",group:"list"},ol:{command:"insertOrderedList",group:"list"},checklist:{command:"insertCheckboxList",group:"list"},justifyLeft:{command:"justifyLeft",group:"format"},justifyCenter:{command:"justifyCenter",group:"format"},justifyRight:{command:"justifyRight",group:"format"},justifyFull:{command:"justifyFull",group:"format"},indent:{command:"indent",group:"format"},outdent:{command:"outdent",group:"format"},link:{command:"createLink",group:"insert"},unlink:{command:"unlink",group:"insert"},image:{command:"insertImage",group:"insert"},video:{command:"insertVideo",group:"insert"},file:{command:"insertFile",group:"insert"},foreColor:{command:"foreColor",group:"format"},backColor:{command:"hiliteColor",group:"format"},fontName:{command:"fontName",group:"format"},fontSize:{command:"fontSize",group:"format"},undo:{command:"undo",group:"utility"},redo:{command:"redo",group:"utility"},viewSource:{command:null,group:"utility"}},g={init(){if(document.getElementById("kaz-editor-modal"))return;const e=this._createModal();document.body.appendChild(e),this._bindEvents(e)},open({title:e="",bodyHTML:t="",onConfirm:o=null,onCancel:r=null}){const n=document.getElementById("kaz-editor-modal");if(!n){console.warn("Modal not initialized. Call KazEditorModal.init() first.");return}this._setContent(n,e,t),this._show(n),this._bindConfirmEvent(n,o),this._bindCancelEvent(n,r),this._focusInput(n)},close(){const e=document.getElementById("kaz-editor-modal");e&&this._hide(e)},_createModal(){const e=document.createElement("div");return e.id="kaz-editor-modal",e.className="kaz-editor-modal hidden",e.innerHTML=`
        <div class="kaz-editor-modal-backdrop"></div>
        <div class="kaz-editor-modal-content">
          <div class="kaz-editor-modal-header">
            <h3 id="kaz-editor-modal-title" class="kaz-editor-modal-title"></h3>
          </div>
          <div id="kaz-editor-modal-body" class="kaz-editor-modal-body"></div>
          <div class="kaz-editor-modal-footer">
            <button id="kaz-editor-modal-cancel" class="kaz-editor-modal-btn kaz-editor-modal-btn-cancel">
              取消
            </button>
            <button id="kaz-editor-modal-confirm" class="kaz-editor-modal-btn kaz-editor-modal-btn-confirm">
              确认
            </button>
          </div>
        </div>
      `,e},_bindEvents(e){e.querySelector(".kaz-editor-modal-backdrop").addEventListener("click",()=>{this._hide(e)}),document.addEventListener("keydown",t=>{t.key==="Escape"&&!e.classList.contains("hidden")&&this._hide(e)}),e.querySelector("#kaz-editor-modal-cancel").addEventListener("click",()=>{this._hide(e)})},_setContent(e,t,o){const r=e.querySelector("#kaz-editor-modal-title"),n=e.querySelector("#kaz-editor-modal-body");r.textContent=t,n.innerHTML=o},_show(e){e.classList.remove("hidden"),document.body.style.overflow="hidden"},_hide(e){e.classList.add("hidden"),document.body.style.overflow=""},_bindConfirmEvent(e,t){const o=e.querySelector("#kaz-editor-modal-confirm"),r=o.cloneNode(!0);o.parentNode.replaceChild(r,o),r.addEventListener("click",()=>{const n=e.querySelector("#kaz-editor-modal-body input")?.value||"";t?t(n)!==!1&&this._hide(e):this._hide(e)})},_bindCancelEvent(e,t){},_focusInput(e){setTimeout(()=>{const t=e.querySelector("#kaz-editor-modal-body input");t&&(t.focus(),t.select())},100)}};let f=null;function y(){const e=window.getSelection();e.rangeCount>0&&(f=e.getRangeAt(0)),g.open({title:"Insert Link",bodyHTML:'<input type="url" class="kaz-editor-link-input" placeholder="https://...">',onConfirm:()=>{const o=document.querySelector(".kaz-editor-link-input")?.value.trim();if(o&&f){const r=window.getSelection();r.removeAllRanges(),r.addRange(f),document.execCommand("createLink",!1,o)}}})}function k(e){if(!/^h[1-6]$/.test(e))return;const t=window.getSelection();if(t.rangeCount===0)return;const o=t.getRangeAt(0);if(o.collapsed)return;const r=document.createElement(e);r.appendChild(o.extractContents()),o.insertNode(r),t.removeAllRanges();const n=document.createRange();n.selectNodeContents(r),t.addRange(n)}const z=Object.freeze(Object.defineProperty({__proto__:null,heading:k,link:y},Symbol.toStringTag,{value:"Module"}));g.init();const E={standard:["bold","italic","underline","link","unlink","ul","ol","h1","h2"]},C={lang:"en-us",init(e={}){this.lang=e.lang||"en-us";const t=e.toolbar||E.standard,o=this._parseSelector(e.target||'[data-editor="true"]'),r=document.querySelectorAll(o),n=window.KazEditorLang||{};r.forEach((d,_)=>{const s=document.createElement("div");s.className="kaz-editor-wrapper";const h=document.createElement("div");h.className="kaz-editor-toolbar",t.forEach(i=>{const l=m[i];if(!l)return;const L=n[i]?.label||l.label?.en||i,S=n[i]?.description||l.description?.en||"",p=document.createElement("button");p.type="button",p.innerHTML=L,p.title=S,p.addEventListener("click",()=>{const v=z[i];if(/^h[1-6]$/.test(i)){k(i);return}if(i==="viewSource"){const a=s.querySelector(".kaz-editor-content");a.dataset.viewingSource?(a.dataset.viewingSource="",a.innerHTML=a.dataset.originalHtml,a.contentEditable=!e.readonly,a.classList.remove("kaz-editor-source")):(a.dataset.viewingSource="true",a.dataset.originalHtml=a.innerHTML,a.textContent=a.innerHTML,a.contentEditable=!1,a.classList.add("kaz-editor-source"));return}if(typeof v=="function"){v();return}if(l.prompt){const a=n[i]?.prompt||l.prompt?.en||"Enter value:",b=prompt(a);b&&document.execCommand(l.command,!1,b)}else document.execCommand(l.command,!1,l.value||null)}),h.appendChild(p)});const u=document.createElement("div");u.contentEditable=!e.readonly,u.className="kaz-editor-content",u.innerHTML=e.content||d.value||"",d.style.display="none",d.parentNode.insertBefore(s,d),s.appendChild(h),s.appendChild(u),s.appendChild(d),e.autosync!==!1&&d.closest("form")?.addEventListener("submit",()=>{d.value=u.innerHTML})})},_parseSelector(e){if(!e||typeof e!="string")return'textarea[data-editor="true"]';const[t,o]=e.split("=");return!t||!o?"textarea":o.startsWith("^")?`textarea[${t}^="${o.slice(1)}"]`:o.startsWith("*")?`textarea[${t}*="${o.slice(1)}"]`:`textarea[${t}="${o}"]`}};c.KazEditor=C,Object.defineProperty(c,Symbol.toStringTag,{value:"Module"})});
